<?php

use App\Http\Controllers\Billing\BillingProfileController;
use App\Http\Controllers\Billing\BillingProfilePoliciesController;
use App\Http\Controllers\Billing\BillingSubscriptionController;
use App\Http\Controllers\Billing\CompanyPaymentMethodController;
use App\Http\Controllers\Billing\CreditController;
use App\Http\Controllers\Billing\ActionApprovalController;
use App\Http\Controllers\Billing\InvoiceCollectionsController;
use App\Http\Controllers\Billing\InvoiceChargebackController;
use App\Http\Controllers\Billing\InvoiceController;
use App\Http\Controllers\Billing\InvoiceCreditController;
use App\Http\Controllers\Billing\InvoiceImportLeadsController;
use App\Http\Controllers\Billing\InvoicePaymentsController;
use App\Http\Controllers\Billing\InvoiceRefundController;
use App\Http\Controllers\Billing\InvoiceReportController;
use App\Http\Controllers\Billing\InvoiceSnapshotController;
use App\Http\Controllers\Billing\InvoiceTemplateController;
use App\Http\Controllers\Billing\InvoiceTransactionController;
use App\Http\Controllers\Billing\InvoiceWriteOffsController;
use App\Http\Controllers\CompanyCampaignController;
use App\Http\Controllers\DashboardAPI\V4\CompanyBillingControllerV4;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Billing Internal API Routes
|--------------------------------------------------------------------------
|
*/

Route::name('v1.')->prefix('/v1')->group(function () {
    Route::prefix('/companies')->group(function () {
        Route::apiResource('billing-subscriptions', BillingSubscriptionController::class);

        Route::prefix('/{company}')->group(function () {
            Route::get('/billing-version', [CompanyBillingControllerV4::class, 'getBillingVersion']);
            Route::get('/default-billing-profile', [CompanyBillingControllerV4::class, 'getDefaultBillingProfile']);

            Route::prefix('/billing-profiles')->group(function () {
                Route::get('/', [BillingProfileController::class, 'getCompanyBillingProfiles']);
            });

            Route::get('/payment-methods', [CompanyPaymentMethodController::class, 'getCompanyBillingProfiles']);
        });
    });
    Route::name('billing.')->prefix('/billing')->group(function () {
        Route::name('invoices.')->prefix('/invoices')->controller(InvoiceController::class)->group(function () {
            Route::post('/leads-upload', [InvoiceImportLeadsController::class, 'getInvoiceLeadsImportData']);
            Route::post('/leads-import', [InvoiceImportLeadsController::class, 'importLeadsViaCsv']);
            Route::prefix('/collections')->controller(InvoiceCollectionsController::class)->group(function () {
                Route::get('/', 'listInvoiceToCollections');
                Route::post('/', 'issueInvoiceToCollections');
                Route::patch('/', 'updateInvoiceCollections');
            });
            Route::prefix('/write-offs')->controller(InvoiceWriteOffsController::class)->group(function () {
                Route::get('/', 'listWriteOffs');
            });
            Route::prefix('/reports')->controller(InvoiceReportController::class)->group(function () {
                Route::get('/invoice-statuses', 'getInvoiceStatuses');
                Route::get('/receivable-invoices', 'getReceivableInvoicesReport');
                Route::get('/revenue', 'getRevenueReport');
                Route::get('/aged-invoices', 'getAgedInvoicesReport');
                Route::get('/invoices-balance', 'getInvoiceBalanceReport');
                Route::get('/credits-movement', 'getCreditsMovementReport');
                Route::get('/companies-overview', 'getCompaniesOverviewReport');
                Route::get('/credits-outstanding', 'getCreditsOutstandingReport');
            });
            Route::prefix('/chargebacks')->controller(InvoiceChargebackController::class)->group(function () {
                Route::get('/', 'getChargebacks');
            });
            Route::prefix('/aggregates')->controller(InvoiceSnapshotController::class)->group(function () {
                Route::get('/total', 'getTotalInvoicesValueByStatus');
                Route::get('/comparison', 'getRevenueComparison');
                Route::get('/payments', 'getPaidInvoiceItemValueByType');
                Route::get('/failed-payments', 'getFailedPayments');
                Route::get('/statuses', 'getInvoiceStatuses');
                Route::get('/bundles-sold', 'getBundlesSold');
            });
            Route::prefix('/{invoice}')->group(function () {
                Route::get('/chargebacks', [InvoiceChargebackController::class, 'getInvoiceChargebacks']);
                Route::get('/transactions', [InvoiceTransactionController::class, 'getInvoiceTransactions']);
                Route::prefix('/payments')->controller(InvoicePaymentsController::class)->group(function () {
                    Route::get('/', 'getInvoicePayments');
                    Route::post('/', 'makeInvoicePaymentRequest');
                });
                Route::post('/pdf', [InvoiceController::class, 'generatePdfSignedUrl']);
            });
            Route::prefix('/action-requests')->controller(ActionApprovalController::class)->group(function () {
                Route::get('/', 'getActionApprovalRequests');
                Route::get('/{actionRequest}', 'getActionApprovalRequest');
                Route::post('/{actionRequest}/review', 'reviewActionRequest');
            });
            Route::prefix('/transactions')->controller(InvoiceTransactionController::class)->group(function () {
                Route::get('/', 'listInvoiceTransactions');
                Route::get('/filters', 'getInvoiceTransactionFilters');
            });
            Route::get('/', 'getInvoices');
            Route::get('/invoice', 'getInvoice');
            Route::get('/invoice/events', 'getInvoiceEvents');
            Route::get('/uninvoiced-products', 'getUninvoicedProducts');
            Route::post('/', 'createInvoice');
            Route::post('/create-update', 'createUpdateInvoice');
            Route::get('/filters', 'getInvoiceFilters');

            Route::prefix('/collections')->controller(InvoiceCollectionsController::class)->group(function () {
                Route::get('/', 'listInvoiceToCollections');
                Route::post('/', 'issueInvoiceToCollections');
                Route::patch('/', 'updateInvoiceCollections');
            });

            Route::get('/refunds', [InvoiceRefundController::class, 'listInvoiceRefunds']);
            Route::prefix('/{invoiceId}')->group(function () {
                Route::prefix('/refunds')->controller(InvoiceRefundController::class)->group(function () {
                    Route::get('/', 'getInvoiceRefunds');
                    Route::post('/', 'issueRefund');
                });
                Route::prefix('/credits')->controller(InvoiceCreditController::class)->group(function () {
                    Route::get('/', 'getInvoiceCredits');
                    Route::post('/', 'applyCredit');
                });
            });
        });
        Route::prefix('/credits')->controller(CreditController::class)->group(function () {
            Route::post('/deduct', 'deductCredit');
            Route::get('/types', 'getCreditTypes');
            Route::post('/', 'createNewCreditType');
            Route::put('/', 'updateOrCreateCreditTypes');
            Route::prefix('/{companyId}')->group(function () {
                Route::get('/balances/', 'getBalances');
                Route::get('/all/', 'getCredits');
                Route::patch('/expire/{creditId}', 'expireCredit');
                Route::patch('/extend/{creditId}', 'extendCredit');
                Route::post('/', 'addCredit');
            });
        });

        Route::prefix('/profiles')->controller(BillingProfileController::class)->group(function () {
            Route::get('/', 'getBillingProfiles');
            Route::post('/', 'createBillingProfile');
            Route::prefix('/company-campaigns')
                ->controller(CompanyCampaignController::class)->group(function () {
                    Route::get('/', 'getCompanyCampaigns');
                });
            Route::prefix('/{billingProfile}')->group(function () {
                Route::get('/', 'getBillingProfile');
                Route::put('/', 'updateBillingProfile');
            });
        });

        Route::name('prefix-policies.')->prefix('/profile-policies')->controller(BillingProfilePoliciesController::class)->group(function () {
            Route::get('/events', 'listAvailableEvents');
            Route::get('/', 'listGlobalPolicies');
            Route::delete('/{profilePolicy}', 'deleteProfilePolicy');
            Route::post('/', 'createProfilePolicy')->name('create-profile-policy');
            Route::put('/batch', 'batchUpdate');
        });

        Route::prefix('/invoice-templates')->controller(InvoiceTemplateController::class)->group(function () {
            Route::get('/searchable-models', 'getSearchableModelTypes');
            Route::get('/', 'listInvoiceTemplates');
            Route::get('/model-search', 'searchModels');
            Route::get('/preview-template/{template}', 'previewTemplate');
            Route::get('/options','getInvoiceTemplateOptions');

            Route::post('/', 'createInvoiceTemplate');
            Route::put('/{template}', 'updateInvoiceTemplate');
            Route::delete('/{template}', 'deleteInvoiceTemplate');
        });

        Route::prefix('/payments')->group(function () {
            Route::prefix('{invoicePayment}')->controller(InvoicePaymentsController::class)->group(function () {
                Route::post('/cancel', 'cancelInvoicePayment');
            });
        });
    });
});
