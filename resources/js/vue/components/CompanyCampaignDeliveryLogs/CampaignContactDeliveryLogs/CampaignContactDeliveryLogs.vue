<template>
    <div>
        <simple-table
            :dark-mode="darkMode"
            title="Company Campaign Contact Delivery Logs"
            v-model="tableFilter"
            :table-filters="tableFilters"
            @update:modelValue="getContactDeliveryLogs"
            :pagination-data="paginationData"
            :data="data"
            :headers="headers"
            :loading="loading"
            @search="handleSearch"
            @reset="handleReset"
            :current-per-page="currentPerPage"
        >
            <template v-slot:visible-filters>
                <company-search-autocomplete
                    v-model="tableFilter.company_id"
                    :dark-mode="darkMode"
                />
            </template>
            <template v-slot:row.col.date="{item, value}">
                <div>{{value}}</div>
            </template>
            <template v-slot:row.col.company="{item, value}">
                <a :href="`/companies/${value?.id}`" target="_blank" class="flex text-primary-500 cursor-pointer" v-if="value">
                    {{ value?.name }} ({{ value?.id }})
                </a>
                <p v-else>N/A</p>
            </template>
            <template v-slot:row.col.campaign="{item, value}">
                <div class="flex gap-1 items-center" v-if="value">
                    <div>
                        {{ value?.name }} ({{ value?.id }})
                    </div>
                    <div class="flex flex-col">
                        <badge :dark-mode="darkMode">{{ value?.status }}</badge>
                    </div>
                </div>
                <p v-else>N/A</p>
            </template>
            <template v-slot:row.col.success="{item, value}">
                <badge
                    :dark-mode="darkMode"
                    :color="value === 'Success' ? 'green' : 'red'"
                >
                    {{ value }}
                </badge>
            </template>
            <template v-slot:row.col.payload="{item, value}">
                <div
                    @click="showPayload = item"
                    class="flex gap-1 text-primary-500 cursor-pointer "
                >
                    <div>View</div>
                    <simple-icon
                        :dark-mode="darkMode"
                        :color="simpleIcon.colors.BLUE"
                        :icon="simpleIcon.icons.ARROW_TOP_RIGHT_ON_SQUARE"
                    />
                </div>
            </template>
        </simple-table>

        <modal
            v-if="showPayload !== null"
            @close="showPayload = null"
            :dark-mode="darkMode"
            small
            hide-confirm
        >
            <template v-slot:header>View Contact Delivery Logs Payload</template>
            <template v-slot:content>
                <div class="rounded-md p-2" :class="{
                    'bg-green-100' : this.showPayload.success === 'Success',
                    'bg-red-100' : this.showPayload.success === 'Failure',
                }">
                    <div v-if="formattedPayload" :class="{'text-slate-500': !darkMode, 'text-blue-500': darkMode}">
                        <p class="font-semibold"><strong>To:</strong> {{ formattedPayload.to }}</p>
                        <p v-if="formattedPayload.subject" class="font-semibold mt-2"><strong>Subject:</strong> {{ formattedPayload.subject }}</p>
                        <div v-if="formattedPayload.content">
                            <p class="font-semibold mt-2"><strong>Content:</strong></p>
                            <div class="mt-2 p-3 rounded-md" v-html="formattedPayload.content"></div>
                        </div>
                        <div v-if="formattedPayload.error">
                            <p class="font-semibold mt-2"><strong>Error:</strong></p>
                            <pre class="whitespace-pre-wrap text-sm text-red-500 mt-2">{{ formattedPayload.error }}</pre>
                        </div>
                    </div>
                    <pre v-else class="whitespace-pre-wrap text-sm">{{ showPayload.payload }}</pre>
                </div>
            </template>
        </modal>
    </div>
</template>

<script>
import ApiService from "./services/api.js";
import {SimpleTableFilterTypesEnum} from "../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {SimpleTableHiddenFilterTypesEnum} from "../../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
import SimpleTable from "../../Shared/components/SimpleTable/SimpleTable.vue";
import CompanySearchAutocomplete from "../../Shared/components/Company/CompanySearchAutocomplete.vue";
import Badge from "../../Shared/components/Badge.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import Modal from "../../Shared/components/Modal.vue";


const DEFAULT_TABLE_FILTER = {
    page: 1,
    perPage: 10,
}

const simpleIcon = useSimpleIcon()
export default {
    name: "CampaignContactDeliveryLogs",
    components: {Modal, SimpleIcon, Badge, CompanySearchAutocomplete, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            api: ApiService.make(),
            simpleIcon,
            loading: false,
            tableFilter: {},
            headers: [
                {title: "Date", field: 'date'},
                {title: "Company", field: 'company', cols:2},
                {title: "Campaign", field: 'campaign', cols:2},
                {title: "Method", field: 'method'},
                {title: "Status", field: 'success'},
                {title: "Details", field: 'payload'},
            ],
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'campaign',
                    title: 'Enter Campaign Name or ID'
                },
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'consumer_product',
                    title: 'Enter Consumer Product ID'
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    field: 'delivery_status',
                    title: 'Delivery Status',
                    options: [
                        {name: 'Successful', id: '1'},
                        {name: 'Failed', id: '0'},
                    ]
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.DATE_RANGE,
                    field: 'date',
                    title: 'Date'
                }
            ],
            data: [],
            paginationData: {},
            showPayload: null,
            formattedPayload: null,
            currentPerPage: DEFAULT_TABLE_FILTER.perPage,
        }
    },
    created() {
        this.tableFilter = {...DEFAULT_TABLE_FILTER};
        this.getContactDeliveryLogs();
    },
    watch: {
      showPayload(newVal) {
          if (newVal !== null) {
              this.formatPayload(newVal.payload);
          } else {
              this.formattedPayload = null;
          }
      }
    },
    methods: {
        async getContactDeliveryLogs() {
            this.loading = true;

            let date = this.tableFilter?.date ?? null;

            const sentData = {
                ...this.tableFilter,
                "date[from]": date?.from,
                "date[to]": date?.to,
            };

            const response = await this.api.getContactDeliveryLogs(sentData)
            const { data, links, meta } = response.data;
            this.data = data.map(log => {
                log.date = (new Date(log.date * 1000)).toLocaleString();
                return log;
            });
            this.paginationData = {links, ...meta}
            this.loading = false;
        },
        async handleSearch() {
            this.tableFilter = {...this.tableFilter, ...DEFAULT_TABLE_FILTER};
            await this.getContactDeliveryLogs();
        },
        async handleReset() {
            this.tableFilter = {...DEFAULT_TABLE_FILTER}
            await this.getContactDeliveryLogs();
        },
        formatPayload(payload) {
            try {
                const parsedPayload = typeof payload === 'string' ? JSON.parse(payload) : payload;
                this.formattedPayload = {
                    to: parsedPayload.to || null,
                    subject: parsedPayload.email_subject || null,
                    content: parsedPayload.content || null,
                    error: parsedPayload.error || null,
                };
            } catch (error) {
                this.formattedPayload = null;
            }
        }
    }
}
</script>

<style scoped>

</style>
