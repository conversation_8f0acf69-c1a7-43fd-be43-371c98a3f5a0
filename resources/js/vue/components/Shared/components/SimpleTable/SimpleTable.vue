<template>
    <div class="border rounded-lg col-span-6"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div v-if="title" class="px-5 pt-5 flex justify-between">
            <h5 class="text-blue-550 text-sm uppercase font-bold leading-tight">{{ title }}</h5>
            <slot name="title-actions"></slot>
        </div>
        <div>
            <simple-table-filter
                v-if="tableFilters.length > 0 || $slots['visible-filters']"
                :active-menu="activeMenu"
                @active-menu="toggleActiveMenu"
                :model-value="modelValue"
                @update:modelValue="$emit('update:modelValue', $event)"
                :filters="tableFilters"
                :dark-mode="darkMode"
                @reset="handleReset"
                @search="handleSearch"
                @export-to-csv="$emit('export-to-csv')"
            >
                <template v-slot:visible-filters>
                    <slot name="visible-filters"/>
                </template>
                <template v-slot:hidden-cols>
                    <div class="relative">
                        <CustomButton @click="toggleFieldsPopup" :dark-mode="darkMode" v-if="columnToggle" color="slate-inverse">
                            <svg class="fill-current mr-2" width="17" height="16" viewBox="0 0 17 16" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M2.66667 2C2.55481 2 2.40141 2.05193 2.25638 2.22597C2.10691 2.40533 2 2.6818 2 3V13C2 13.3182 2.10691 13.5947 2.25638 13.774C2.40141 13.9481 2.55481 14 2.66667 14H4.33333C4.44519 14 4.59859 13.9481 4.74362 13.774C4.89309 13.5947 5 13.3182 5 13V3C5 2.6818 4.89309 2.40533 4.74362 2.22597C4.59859 2.05193 4.44519 2 4.33333 2H2.66667ZM6 0.654839C5.54679 0.248166 4.96618 0 4.33333 0H2.66667C1.89447 0 1.20002 0.369497 0.719934 0.945602C0.244279 1.51639 0 2.25734 0 3V13C0 13.7427 0.24428 14.4836 0.719934 15.0544C1.20002 15.6305 1.89446 16 2.66667 16H4.33333C4.96618 16 5.54679 15.7518 6 15.3452C6.45321 15.7518 7.03382 16 7.66667 16H9.33333C9.96618 16 10.5468 15.7518 11 15.3452C11.4532 15.7518 12.0338 16 12.6667 16H14.3333C15.1055 16 15.8 15.6305 16.2801 15.0544C16.7557 14.4836 17 13.7427 17 13V3C17 2.25734 16.7557 1.51639 16.2801 0.945602C15.8 0.369497 15.1055 0 14.3333 0H12.6667C12.0338 0 11.4532 0.248166 11 0.654839C10.5468 0.248166 9.96618 0 9.33333 0H7.66667C7.03382 0 6.45321 0.248166 6 0.654839ZM10 3C10 2.6818 9.89309 2.40533 9.74362 2.22597C9.59859 2.05193 9.44519 2 9.33333 2H7.66667C7.55481 2 7.40141 2.05193 7.25638 2.22597C7.10691 2.40533 7 2.6818 7 3V13C7 13.3182 7.10691 13.5947 7.25638 13.774C7.40141 13.9481 7.55481 14 7.66667 14H9.33333C9.44519 14 9.59859 13.9481 9.74362 13.774C9.89309 13.5947 10 13.3182 10 13V3ZM12 13C12 13.3182 12.1069 13.5947 12.2564 13.774C12.4014 13.9481 12.5548 14 12.6667 14H14.3333C14.4452 14 14.5986 13.9481 14.7436 13.774C14.8931 13.5947 15 13.3182 15 13V3C15 2.6818 14.8931 2.40533 14.7436 2.22597C14.5986 2.05193 14.4452 2 14.3333 2H12.6667C12.5548 2 12.4014 2.05193 12.2564 2.22597C12.1069 2.40533 12 2.6818 12 3V13Z"/>
                            </svg>
                            Fields
                        </CustomButton>
                        <div v-if="activeMenu === 'fields'"
                             class="z-20 absolute rounded-lg border w-64 grid gap-2 p-3 top-10 left-0 shadow-module text-sm font-medium"
                             :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                            <div>
                                <CustomButton :color="columnToggleButton === 'Deselect All' ? 'slate' : 'primary'" @click="toggleAllFieldsShown" :dark-mode="darkMode">
                                    {{ columnToggleButton }}
                                </CustomButton>
                            </div>
                            <div v-for="(header, key) in formattedHeaders" :key="header.field">
                                <div class="inline-flex items-center gap-x-2">
                                    <input type="checkbox" @input="updateFieldShown(header.field, !header.show)" :checked="header.show"
                                           class="mr-2 checked:bg-primary-500 rounded cursor-pointer focus:outline-none focus:ring-0"/>
                                    {{ header.title }}
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <template v-slot:custom-buttons="slotProps">
                    <slot name="custom-buttons" v-bind="slotProps"></slot>
                    <custom-button v-if="card" @click="toggleView" :dark-mode="darkMode" class="gap-2">
                        <div>
                            Toggle View
                        </div>
                        <simple-icon :dark-mode="darkMode" :icon="view === 'card' ? simpleIcon.icons.TABLE_CELLS : simpleIcon.icons.SQUARES_2X2"/>
                    </custom-button>
                </template>
            </simple-table-filter>
            <div v-if="$slots.disclaimer" class="px-5">
                <slot name="disclaimer"></slot>
            </div>
            <div v-if="view === views.TABLE" class="mt-3">
                <simple-table-header
                    @select-all="handleSelectAll"
                    :check-box-column="checkBox"
                    :grid-cols="gridCols"
                    :headers="formattedHeaders"
                    :header-classes="headerClasses"
                    @sort="sort"
                    @toggle-timezone="toggleTimezone"
                >
                </simple-table-header>
                <simple-table-body
                    :loading="loading"
                    :dark-mode="darkMode"
                    :data="data"
                    :grid-cols="gridCols"
                    :body-classes="bodyClasses"
                    :wrapper-classes="bodyWrapperClasses"
                    :key="refreshKey"
                >
                    <!-- Table row -->
                    <div
                        v-for="(item, idx) in data"
                        :class="rowClassStyle"
                        @click="() => handleRowClick(idx, item)"
                    >
                        <div v-for="(header, index) in displayedHeaders" :key="index" :class="[`col-span-${getColSpanByField(header.field)}`]">
                            <div :class="{[`${bodyClasses}`] : true}">
                                <input class="mr-3 rounded" @click.stop v-if="checkBox && index === 0" type="checkbox" v-model="selectedItems" :value="item" >
                                <slot :name="`row.col.${header.field}`" v-bind="{ rowIndex: idx, item, value:item[header.field], idx}">
                                    <div class="flex justify-start text-left" @click="handleColumnClicked(header, item)" :class="{'cursor-pointer text-blue-500': header.clickable}">
                                        <p class="text-sm">{{ resolveFieldKey(item, header) }}</p>
                                    </div>
                                </slot>
                            </div>
                        </div>
                        <div v-if="$slots['row.complement'] && idx === lastClickedRowIdx"
                             class="p-2 col-span-full cursor-default"
                             @click.stop="() => 'keepToAvoidCloseTheComplementOnClick'"
                        >
                            <slot name="row.complement" v-bind="{ item, idx }"></slot>
                        </div>
                    </div>
                    <div :class="rowClassStyle"
                         v-if="$slots['body.last-row']">
                        <slot name="body.last-row"></slot>
                    </div>
                </simple-table-body>

                <div v-if="!noPagination" class="p-3 flex items-center justify-end gap-2">
                    <div>
                        <span class="text-sm text-slate-500">Results Per Page</span>
                    </div>
                    <div>
                        <dropdown
                            :dark-mode="darkMode"
                            v-model="localPerPage"
                            placement="top"
                            :options="perPageOptions"
                            @input="handlePerPageChange"
                        />
                    </div>
                    <pagination
                        :dark-mode="darkMode"
                        :pagination-data="paginationData"
                        show-pagination
                        @change-page="handlePageChange"
                    />
                </div>
                <div v-else class="p-3">
                    <slot name="footer" />
                </div>
            </div>
            <div v-else>
                <div
                    class="overflow-y-auto divide-y"
                    :class="[cardStyle.base, bodyWrapperClasses]"
                >
                    <simple-table-card
                        v-for="(item, index) in data"
                        :dark-mode="darkMode"
                        :headers="displayedHeaders"
                        :data="item"
                        :key="index"

                    >
                        <template #card="{item}">
                            <slot name="card" v-bind="{item}">
                            </slot>
                        </template>
                        <template
                            v-for="(field,idx) in displayedHeaders"
                            #[`row.col.${field.field}`]="{item,value}"
                        >
                            <slot :name="`row.col.${field.field}`" v-bind="{ rowIndex: idx, item, value:value, idx}"/>
                        </template>
                    </simple-table-card>
                </div>
                <div v-if="!noPagination" class="p-3 flex items-center justify-end gap-2">
                    <div>
                        <span class="text-sm text-slate-500">Results Per Page</span>
                    </div>
                    <div>
                        <dropdown
                            :dark-mode="darkMode"
                            v-model="localPerPage"
                            placement="top"
                            :options="perPageOptions"
                            @input="handlePerPageChange"
                        />
                    </div>
                    <pagination
                        :dark-mode="darkMode"
                        :pagination-data="paginationData"
                        show-pagination
                        @change-page="handlePageChange"
                    />
                </div>
                <div v-else class="p-3">
                    <slot name="footer" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import LoadingSpinner from "../LoadingSpinner.vue";
import Pagination from "../Pagination.vue";
import Dropdown from "../Dropdown.vue";

import SimpleTableFilter from "./components/SimpleTableFilter.vue";
import SimpleTableHeader from "./components/SimpleTableHeader.vue";
import SimpleTableBody from "./components/SimpleTableBody.vue";
import CustomButton from "../CustomButton.vue";
import Badge from "../Badge.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import SimpleTableCard from "./components/SimpleTableCard.vue";
import useDateHelper from "../../../../../composables/useDateHelper.js";
const simpleIcon = useSimpleIcon();

const DEFAULT_PER_PAGE = 100
const PER_PAGE_OPTIONS = [5, 10, 25, 50, DEFAULT_PER_PAGE]
const views = {
    TABLE: 'table',
    CARD: 'card',
};

export default {
    name: "SimpleTable",
    components: {
        SimpleTableCard,
        SimpleIcon,
        Badge,
        CustomButton,
        SimpleTableFilter,
        SimpleTableHeader,
        SimpleTableBody,
        LoadingSpinner,
        Pagination,
        Dropdown,
    },
    props: {
        modelValue: {
            type: Object,
            required: false,
        },
        hasRowClick: {
            type: Boolean,
            default: false,
        },
        tableFilters: {
            type: Array,
            required: false,
            default: () => []
        },
        title: {
            type: String,
            required: false,
        },
        headers: {
            type: Array,
            required: true
        },
        data: {
            type: Array,
            required: true
        },
        darkMode: {
            type: Boolean,
            required: true
        },
        notFoundMessage: {
            type: String,
            default: 'No data found'
        },
        noPagination: {
          type: Boolean,
          default: false
        },
        paginationData: {
            type: Object,
            required: false,
        },
        currentPerPage: {
            type: Number,
            default: DEFAULT_PER_PAGE
        },
        perPageOptions: {
            type: Array,
            default: PER_PAGE_OPTIONS
        },
        loading: {
            type: Boolean,
            default: false
        },
        checkBox: {
            type: Boolean,
            default: false
        },
        columnToggle: {
            type: Boolean,
            default: false
        },
        headerClasses: {
            type: String,
        },
        bodyClasses: {
            type: String,
            default: "flex items-center justify-start"
        },
        rowClasses: {
            type: String,
            default: "gap-5 grid items-center py-4 rounded px-5 h-20",
        },
        selectedItems: {
            type: Array,
            default: []
        },
        showAllComplements: {
            type: Boolean,
            default: false
        },
        bodyWrapperClasses: {
            type: String,
            default: 'h-100'
        },
        card: {
            type: Boolean,
            default: false,
        },
        cardStyle: {
            type: Object,
            default: {
                base: 'bg-light-background border-y border-light-border gap-4 grid grid-cols-3 p-3',
                card: 'border rounded-md bg-light-module gap-2 p-2 grid grid-cols-2',
            }
        }
    },
    emits: [
        'per-page-change',
        'page-change',
        'click-row',
        'reset',
        'search',
        'export-to-csv',
        'select-all','update:selected',
        'update:modelValue',
        'sort',
        'column-clicked'
    ],

    data(){
        return {
            formattedHeaders: [],
            localPerPage: null,
            lastClickedRowIdx: null,
            activeMenu: null,
            originalFormattedHeaders: [],
            views,
            view: views.TABLE,
            simpleIcon,
            dateHelper: null,
            refreshKey: 0,
        }
    },


    mounted() {
        this.localPerPage = this.currentPerPage
        this.formatHeaders()
    },

    methods: {
        toggleView() {
            this.view = this.view === views.TABLE ? views.CARD : views.TABLE;
        },
        formatHeaders(){
            this.modelValue?.sort_by?.forEach((sort) => {
                const [field, direction] = sort.split(':')

                const idx = this.headers.findIndex(h => h.field === field)

                if (idx >= 0) {
                    this.headers[idx]['sort_by'] = direction
                }
            })

            this.formattedHeaders = this.headers.map(header => {
                return {
                    ...header,
                    cols: header.cols ?? 1,
                    show: header.show ?? true,
                    order_by: 'desc'
                }
            })
            this.originalFormattedHeaders = JSON.parse(JSON.stringify(this.formattedHeaders))
        },
        handleSearch(){
            this.$emit('search')
        },
        handleReset(){
            this.formattedHeaders = JSON.parse(JSON.stringify(this.originalFormattedHeaders))
            this.$emit('reset')
        },
        handlePerPageChange(event) {
            this.$emit('update:modelValue', {...this.modelValue, perPage: event, per_page: event, page: 1})
            this.$emit('per-page-change', event)
        },
        handlePageChange(event) {
            this.$emit('update:modelValue', {...this.modelValue, page: event.newPage})
            this.$emit('page-change', event);
        },
        getColSpanByField(field){
            const header = this.formattedHeaders.find(header => header.field === field)
            if (header.show === false) return 0;
            return header ? header.cols : 1
        },

        handleRowClick(lastClickedRowIdx, item){
            if (!this.hasRowClick) return
            this.$emit('click-row', item)

            if (this.lastClickedRowIdx === lastClickedRowIdx) {
                this.lastClickedRowIdx = null
                return
            }

            this.lastClickedRowIdx = this.lastClickedRowIdx === lastClickedRowIdx ? null : lastClickedRowIdx
        },

        handleColumnClicked(header, item) {
            if (header.clickable) {
                this.$emit('column-clicked', header.field, item);
            }
        },

        handleSelectAll(isAllSelected)
        {
            this.$emit('update:selected', !isAllSelected ? [] : this.data)
        },
        toggleFieldsPopup () {
            this.toggleActiveMenu('fields');
        },
        toggleActiveMenu(item) {
            this.activeMenu === item ? this.activeMenu = null : this.activeMenu = item;
        },
        updateFieldShown(headerField, showStatus) {
            let header = this.formattedHeaders.find(header => header.field === headerField);
            header.show = showStatus
        },
        toggleAllFieldsShown() {
            if (this.formattedHeaders.some(header => !header.show)) {
                this.formattedHeaders.forEach(header => header.show = true);
            } else {
                this.formattedHeaders.forEach(header => header.show = false);
                let firstHeader = this.formattedHeaders[0];
                firstHeader.show = true
            }
        },
        sort(sort) {
            // Sort headers by the most recent sorted to go first in the query
            const sortedHeaders = this.formattedHeaders.filter(h => h.sort_by && h.sortable)
                .sort((a, b) => a.number < b.number ? -1 : 1)

            this.$emit('update:modelValue', {
                ...this.modelValue,
                sort_by: sortedHeaders.map(h => `${h.field}:${h.sort_by}`)
            })
        },
        resolveFieldKey(item, header) {
            const value = /\./.test(header.field)
                ? this.resolveNestedKeys(item, header.field)
                : item[header.field];

            if (header.timestamp) {
                const timezone = header.timezoneField
                    ? this.resolveNestedKeys(item, header.timezoneField)
                    : null;

                return this.handleTimestamp(value, timezone, !!header.useLocalTime);
            }

            return value;
        },
        resolveNestedKeys(item, field) {
            const keys = field.split(/\./g);
            return keys.reduce((output, key) => {
                if (output !== undefined)
                    output = output[key];

                return output;
            }, item);
        },
        handleTimestamp(value, timezone, useLocalTime) {
            const dateFormat = "MM/dd/yyyy, hh:mm a";
            if (useLocalTime) {
                const date = this.getDateHelper.parseDate(value, null, 'local');

                return date.toFormat(dateFormat) + ` (${date.zoneName || 'local'})`;
            }
            else {
                const date = this.getDateHelper.parseDate(value, null, timezone ?? 'UTC');

                return date.toFormat(dateFormat) + ` (${timezone || 'UTC'})`;
            }
        },

    },

    computed: {
        rowClassStyle() {
            return [this.darkMode
                ? 'bg-dark-background border-dark-border hover:bg-dark-module'
                : 'bg-light-background border-light-border hover:bg-light-module',
                `grid grid-cols-${this.gridCols}`,
                (this.hasRowClick || this.$slots['row.complement'])
                    ? 'cursor-pointer'
                    : 'cursor-default',
                this.rowClasses,
                this.$slots['row.complement']
                    ? 'h-auto'
                    : ''
            ]
        },
        gridCols() {
            return this.displayedHeaders.reduce((prev, current) => prev + current.cols, 0)
        },
        columnToggleButton() {
            return this.formattedHeaders.some(header => !header.show) ? "Select All" : "Deselect All"
        },
        displayedHeaders() {
            return this.formattedHeaders.filter((item) => item?.show ?? true);
        },
        getDateHelper() {
            if (!this.dateHelper)
                this.dateHelper = new useDateHelper();

            return this.dateHelper;
        },
        toggleTimezone() {
            this.refreshKey = 1 - this.refreshKey;
        }
    },
    watch: {
        selectedItems: {
            deep: true,
            handler(newVal) {
                this.$emit('update:selected', newVal)
            }
        },
        headers: {
            deep: true,
            handler(newVal) {
                const newHeaders = [...newVal]
                this.formattedHeaders = newHeaders.map(header => ({...header, cols: header.cols ?? 1, show: header.show ?? true}))
            }
        }

    }
}
</script>
