<template>
    <div :class="[!disableDefaultPadding ? 'pt-5' : '']">
        <form @submit.prevent="search" @reset.prevent="reset">
            <div class="grid items-end mb-3 gap-x-3 px-5 mr-3 grid-cols-6">
                <div v-for="filter in visibleFilters">
                    <custom-input
                        v-model="modelValue[filter.field]"
                        :dark-mode="darkMode"
                        :placeholder="filter.title"
                        :search-icon="filter.search_icon"
                    />
                </div>
                <slot name="visible-filters"/>
                <div class="flex gap-x-3">
                    <custom-button type="submit" :dark-mode="darkMode">Search</custom-button>
                    <custom-button :dark-mode="darkMode" color="slate-inverse" type="reset">Reset</custom-button>
                    <simple-table-hidden-filters
                        :model-value="modelValue"
                        :active-menu="activeMenu"
                        @active-menu="$emit('active-menu', $event)"
                        @update:modelValue="$emit('update:modelValue', $event)"
                        :filters="hiddenFilters"
                        :dark-mode="darkMode"
                        @apply="search"
                    />
                    <slot name="hidden-cols"></slot>
                </div>
                <div class="col-start-6 col-end-7 flex gap-x-3 ml-auto">
                    <slot name="custom-buttons"></slot>
                    <custom-button v-if="false" :dark-mode="darkMode" id="exportcsv" class="" @click="handleExportToCSV">Export</custom-button>
                </div>
            </div>
        </form>
        <div class="flex items-center flex-wrap gap-2 px-5">
            <filter-pill
                v-for="(value, key) in appliedFilters"
                :dark-mode="darkMode"
                @click="removeFilter(key)"
                :removable="getFilter(key)?.removable"
            >
                <span v-html="getAppliedFilterChipsHtml(value, key)"></span>
            </filter-pill>
        </div>
    </div>
</template>

<script>
import FilterPill from "../../FilterPill.vue";
import CustomButton from "../../CustomButton.vue";
import CustomInput from "../../CustomInput.vue";

import SimpleTableHiddenFilters from "./SimpleTableHiddenFilters/SimpleTableHiddenFilters.vue";

import { SimpleTableHiddenFilterTypesEnum } from "../enum/simpleTableFilterHiddenTypes";
import { SimpleTableFilterTypesEnum } from "../enum/simpleTableFilterLocationsEnum";

import { DateTime } from "luxon";

export default {
    name: "SimpleTableFilter",
    components: { FilterPill, SimpleTableHiddenFilters, CustomButton, CustomInput },
    props: {
        modelValue: {
            type: Object,
            required: true,
        },

        filters: {
            type: Array,
            required: true,
        },

        darkMode: {
            type: Boolean,
            required: true
        },
        disableDefaultPadding: {
            type: Boolean,
            required: false
        },
        activeMenu: String,
    },

    emits: ['search', 'reset', 'export-to-csv', 'active-menu'],

    methods: {
        handleRemoveFilterFromField(field) {
            delete this.modelValue[field]
        },

        search(){
            this.$emit('search', this.modelValue)
        },

        reset(){
            this.filters.forEach(f => this.handleRemoveFilterFromField(f.field))
            this.$emit('reset', this.modelValue)
        },

        getAppliedFilterChipsHtml(value, key){
            if (!value) return ''
            let operator = 'is'

            const filter = this.filters.find(f => f.field === key)
            let formatted = value

            const htmlMethod = filter['getChipsHtml']

            if (htmlMethod) return htmlMethod(value, filter)

            switch (filter.type){
                case SimpleTableHiddenFilterTypesEnum.DATE_RANGE:
                    operator = ''
                    formatted = this.formatChipDateRangeHtml(value, filter)
                    break;
                case SimpleTableHiddenFilterTypesEnum.INDUSTRY:
                case SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS:
                    formatted = this.formatChipOptionsHtml(value, filter)
                    break;
                case SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION:
                    formatted = this.formatChipSingleOptionHtml(value, filter)
                    break;
                case SimpleTableHiddenFilterTypesEnum.NUMBER_RANGE:
                    formatted = this.formatNumberRange(value, filter)
                    break;
            }

            // TODO - Make is generic
            return [filter.title, this.getNormalTextHtml(operator), formatted].map(s => s.trim()).join(' ')
        },

        formatChipOptionsHtml(selectedValue, filter){
            const optionNamesMapped = selectedValue.map(v => filter.options.find(o => o.id === v).name)

            return optionNamesMapped.join(', ')
        },

        formatNumberRange(value, filter){
            const prefix = filter?.prefix ?? '$';

            return [
                value?.min > 0 ? `greater than ${prefix}${value?.min}` : '',
                value?.max > 0 && value?.min > 0  ? `and less than ${prefix}${value?.max}` : value?.max > 0 ? `less than ${prefix}${value?.max}` : '',
            ].filter(e => !!e).join(' ')
        },

        formatChipDateRangeHtml(value, filter){
            const texts = []

            // TODO - Improve
            const [formattedFrom, formattedTo] = [value.from, value.to].map(function (date) {
                if (!date) {
                    return ''
                }

                let parsed = DateTime.fromISO(date);

                if (filter.timezone) {
                    parsed = parsed.setZone(filter.timezone)
                }

                return parsed.toFormat('DD')
            })

            if (formattedFrom) texts.push(`${this.getNormalTextHtml('from')} ${formattedFrom}`)
            if (formattedTo) texts.push(`${this.getNormalTextHtml('to')} ${formattedTo}`)

            return texts.join(' ')
        },

        getFilter(slug){
            return this.filters.find( e=> e.field === slug)
        },

        getNormalTextHtml(word = ''){
            return word.length > 0 ? `<span class="text-slate-500">${word}</span>` : ''
        },

        formatChipSingleOptionHtml(value, filter){
            return filter.options.find(o => o.id === value).name
        },

        removeFilter(filterField){
            const filter = this.getFilter(filterField)

            if (filter?.removable === false) {
                return
            }

            this.handleRemoveFilterFromField(filterField)
            this.$emit('search')
        },

        handleExportToCSV() {
            this.$emit('export-to-csv');
        }
    },

    computed: {
        hiddenFilters(){
            return this.filters.filter(f => f.location === SimpleTableFilterTypesEnum.HIDDEN)
        },

        visibleFilters(){
            return this.filters.filter(f => f.location !== SimpleTableFilterTypesEnum.HIDDEN)
        },

        appliedFilters(){
            // Show hidden filters pills removing either null or empty strings/objects/arrays
            return Object.entries(this.modelValue)
                .filter(([_, val]) =>
                    // Remove null objects values
                    !window._.isNull(val)
                    // Remove empty objects values
                    && !window._.isEmpty(val)
                    // Remove empty properties from objects values
                    && Object.values(window._.pickBy(val, window._.identity)).length > 0
                )
                .filter(([key]) => this.hiddenFilters.find(f => f.field === key))
                .reduce((prev, [key, val]) => Object.assign(prev, {[key]: val}), {})
        }
    }
}
</script>
