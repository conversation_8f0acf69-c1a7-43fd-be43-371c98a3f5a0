<template>
    <div>
        <simple-table
            title="Billing Profile Management"
            :dark-mode="darkMode"
            :loading="loading"
            :data="data"
            :headers="headers"
            :pagination-data="paginationData"
            v-model="tableFilter"
            :current-per-page="tableFilter.per_page"
            @page-change="handlePageChange"
            :table-filters="tableFilters"
            @update:modelValue="getBillingProfiles"
            @search="handleSearch"
            @reset="handleReset"
            has-row-click
            @click-row="(r) => toggleCreateBillingProfileModal(true, r)"
            header-classes="uppercase text-xs font-medium rounded-lg flex"
            body-classes="flex"
            row-classes="gap-5 grid items-center py-2 rounded px-5 text-sm"
        >
            <template v-slot:title-actions>
                <custom-button @click="toggleCreateBillingProfileModal(true)">
                    Create profile
                </custom-button>
            </template>
            <template v-slot:visible-filters>
                <company-search-autocomplete
                    v-model="tableFilter.company_id"
                />
            </template>
            <template v-slot:row.col.name="{item, value}">
                <entity-hyperlink type="company" :entity-id="item.company.id" :prefix="item.company.name">
                </entity-hyperlink>
            </template>
            <template v-slot:row.col.contact="{item, value}">
                <badge :dark-mode="darkMode">{{value.name}}</badge>
            </template>
            <template v-slot:row.col.default="{value}">
                <simple-icon
                    :icon="value ? simpleIcon.icons.CHECK_CIRCLE : simpleIcon.icons.X_CIRCLE"
                    :color="value ? simpleIcon.colors.GREEN : simpleIcon.colors.RED"
                >
                </simple-icon>
            </template>
            <template v-slot:row.col.invoice_template_name="{value}">
                {{value ?? 'Automatic'}}
            </template>
            <template v-slot:row.col.threshold="{item, value}">
                {{$filters.currency(value)}}
            </template>
            <template v-slot:row.col.payment_method="{value}">
                <payment-method-badge :type="value" />
            </template>
            <template v-slot:row.col.frequency="{item}">
                <div>
                    {{simpleFrequencyHelper.getHumanReadableText(item.frequency_type, item.frequency_data)}}
                </div>
            </template>
            <template v-slot:row.col.campaigns="{item, value}">
                <limited-list :list-items="value" empty-message="No Associated Campaigns"/>
            </template>
        </simple-table>
        <edit-billing-profile-modal
            v-if="showBillingProfileModal"
            :dark-mode="darkMode"
            :data="selectedProfile"
            :billing-profile-id="selectedProfileId"
            @close="toggleCreateBillingProfileModal(false)"
            @confirm="handleProfileConfirmed"
        />
    </div>
</template>
<script>
import SimpleTable from "../../Shared/components/SimpleTable/SimpleTable.vue";
import ApiService from "../services/billing-profiles-api";
import {SimpleTableFilterTypesEnum} from "../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum";
import CustomButton from "../../Shared/components/CustomButton.vue";
import Badge from "../../Shared/components/Badge.vue";
import EditBillingProfileModal from "../components/Modals/EditBillingProfileModal.vue";
import CompanySearchAutocomplete from "../../Shared/components/Company/CompanySearchAutocomplete.vue";
import {useSimpleFrequencyHelper} from "../../../../composables/useSimpleFrequencyHelper.js";
import EntityHyperlink from "../components/EntityHyperlink.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import LimitedList from "../../Shared/components/Simple/LimitedList.vue";
import useQueryParams from "../../../../composables/useQueryParams.js";
import PaymentMethodBadge from "../../Billing/InvoicePaymentsModule/PaymentMethodBadge.vue";

export default {
    name: "BillingProfilesAndPolicies",
    components: {
        PaymentMethodBadge,
        LimitedList,
        SimpleIcon,
        EntityHyperlink, CompanySearchAutocomplete, EditBillingProfileModal, Badge, CustomButton, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            apiService: ApiService.make(),
            loading: false,
            data: [],
            headers: [
                {title: "Profile Id", field: "id"},
                {title: "Company", field: "name"},
                {title: "Invoice Template", field: "invoice_template_name"},
                {title: "Method", field: "payment_method"},
                {title: "Preferred", field: "default"},
                {title: "Billing Contact", field: "contact"},
                {title: "Threshold", field: "threshold"},
                {title: "Frequency", field: "frequency"},
                {title: "Charge Attempt Allowance", field: "charge_attempts"},
                {title: "Associated Campaigns", field: "campaigns"},
            ],
            tableFilter: {
                per_page: 25
            },
            showBillingProfileModal: false,
            paginationData: {},
            selectedProfileId: null,
            tableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'billing_contact',
                    title: "Billing Contact's Name or Email",
                },
            ],
            tableFilterCompanyOptions: [],
            selectedProfile: null,
            simpleFrequencyHelper: useSimpleFrequencyHelper(),
            showCreateBillingProfileModal: false
        }
    },
    created() {
        this.tableFilter = {
            page: 1,
            per_page: 25
        }
        this.getBillingProfiles();

        const {id, ...rest} = this.queryParamsHelper.getCurrentParams()

        if (id) {
            this.selectedProfileId = id
            this.toggleCreateBillingProfileModal(true)
            this.queryParamsHelper.setQueryParamsOnCurrentUrl(rest)
        }
    },
    computed: {
        simpleIcon(){
            return useSimpleIcon()
        },
        queryParamsHelper(){
            return useQueryParams()
        }
    },
    methods: {
        toggleCreateBillingProfileModal(visible, profileData = {}){
            this.showBillingProfileModal = visible
            this.selectedProfile = profileData
            if (!visible) {
                this.selectedProfileId = null
            }
        },
        handleProfileConfirmed() {
            this.toggleCreateBillingProfileModal(false)
            this.getBillingProfiles();
        },
        async handleSearch(){
            await this.getBillingProfiles()
        },
        async handleReset(){
            this.tableFilter = {page: 1, perPage: 25, per_page: 25}
            await this.getBillingProfiles()
        },
        handlePageChange({newPage}){
            this.tableFilter.page = newPage
            this.getBillingProfiles()
        },
        async getBillingProfiles() {
            this.loading = true;
            const response = await this.apiService.getBillingProfiles(this.tableFilter);
            const { data, links, meta} = response.data;
            this.data = data;
            this.paginationData = {links, ...meta}
            this.loading = false;
        }
    }
}
</script>
