<template>
    <div>
        <simple-table
            v-model="filters"
            :dark-mode="darkMode"
            :data="data"
            :headers="headers"
            :loading="loading"
            :pagination-data="paginationData"
            :current-per-page="filters.per_page"
            title="Revenue report"
            @search="getReport"
            @reset="handleReset"
            @page-change="handlePageChange"
            :table-filters="tableFilters"
            row-classes="gap-5 grid items-center py-2 rounded px-5 text-sm"
        >
            <template v-slot:title-actions>
                <CustomButton v-if="isFinanceOwner" :disabled="exporting" :dark-mode="darkMode" @click="handleExportReport('csv')">
                    <svg v-if="exporting" aria-hidden="true" class="w-4 h-4 text-gray-200 animate-spin fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/></svg>
                    <p v-else>Export</p>
                </CustomButton>
            </template>
            <template v-slot:disclaimer>
                <div class="p-2 mb-10 grid grid-cols-6 items-center">
                    <div v-for="(val, key) in aggregates">
                        <labeled-value :label="key">
                            {{$filters.currency(val)}}
                        </labeled-value>
                    </div>
                </div>
            </template>
            <template v-slot:visible-filters>
                <date-picker
                    v-model="filters.date_range"
                    :enable-time-picker="false"
                    :dark="darkMode"
                    auto-apply
                    placeholder="mm-dd-yy"
                    format="PP"
                    timezone="America/Denver"
                    range
                >
                    <template #input-icon>
                        <svg class="ml-2" width="14" height="16" viewBox="0 0 14 16" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M3.11133 7H4.66688V8.55556H3.11133V7ZM3.11133 10.1111H4.66688V11.6667H3.11133V10.1111ZM6.22244 7H7.77799V8.55556H6.22244V7ZM6.22244 10.1111H7.77799V11.6667H6.22244V10.1111ZM9.33355 7H10.8891V8.55556H9.33355V7ZM9.33355 10.1111H10.8891V11.6667H9.33355V10.1111Z"
                                fill="#0081FF"/>
                            <path
                                d="M1.55556 15.5556H12.4444C13.3023 15.5556 14 14.8579 14 14V3.11111C14 2.25322 13.3023 1.55556 12.4444 1.55556H10.8889V0H9.33333V1.55556H4.66667V0H3.11111V1.55556H1.55556C0.697667 1.55556 0 2.25322 0 3.11111V14C0 14.8579 0.697667 15.5556 1.55556 15.5556ZM12.4444 4.66667L12.4452 14H1.55556V4.66667H12.4444Z"
                                fill="#0081FF"/>
                        </svg>
                    </template>
                </date-picker>
                <autocomplete
                    :dark-mode="darkMode"
                    v-model="filters.company_id"
                    :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}"
                    :options="companyOptions"
                    placeholder="Company"
                    @search="searchCompanies($event)"
                />
            </template>
            <template v-slot:row.col.company_id="{item}">
                <entity-hyperlink type="company" :entity-id="item.company?.id" :prefix="item.company?.id">
                </entity-hyperlink>
            </template>
            <template v-slot:row.col.company_name="{item}">
                <entity-hyperlink type="company" :entity-id="item.company?.id" :prefix="item.company?.name">
                </entity-hyperlink>
            </template>
            <template v-slot:row.col.type="{value}">
                <badge>
                    {{value}}
                </badge>
            </template>
            <template v-slot:row.col.status="{value}">
                <invoice-status-badge :status="value"></invoice-status-badge>
            </template>
            <template v-slot:row.col.invoice_id="{value}">
                <entity-hyperlink :entity-id="value" type="invoice" :prefix="value"></entity-hyperlink>
            </template>
        </simple-table>
    </div>
</template>
<script>
import SimpleTable from "../../../Shared/components/SimpleTable/SimpleTable.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import DatePicker from "@vuepic/vue-datepicker";
import Autocomplete from "../../../Shared/components/Autocomplete.vue";
import SharedApiService from "../../../Shared/services/api";
import SimpleAlert from "../../../Shared/components/SimpleAlert.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import {
    SimpleTableHiddenFilterTypesEnum
} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes";
import {SimpleTableFilterTypesEnum} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum";
import InvoiceStatusBadge from "../../components/InvoiceStatusBadge.vue";
import ApiService from "../../services/invoices-report-api.js";
import InvoiceTransactionsApi from "../../services/invoices-transactions-api.js";
import DisplayInvoiceTransactionStatuses
    from "../../../Billing/CreateInvoiceModalContentCompanyData/components/DisplayInvoiceTransactionStatuses.vue";
import Badge from "../../../Shared/components/Badge.vue";
import LabeledValue from "../../../Shared/components/LabeledValue.vue";
import EntityHyperlink from "../../components/EntityHyperlink.vue";
import {downloadCsvString} from "../../../../../composables/exportToCsv.js";
import {ROLES, useRolesPermissions} from "../../../../../stores/roles-permissions.store.js";
import {DateTime} from "luxon";

export default {
    name: "RevenueReport",
    components: {
        EntityHyperlink,
        LabeledValue,
        Badge,
        DisplayInvoiceTransactionStatuses,
        InvoiceStatusBadge,
        Dropdown, SimpleAlert, Autocomplete, DatePicker, CustomButton, SimpleTable
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        }
    },
    created() {
        this.handleReset()
        this.getTableFilters();
    },
    data() {
        return {
            exporting: false,
            sharedApi: SharedApiService.make(),
            showViewInvoiceModal: false,
            invoiceTransactionsApi: InvoiceTransactionsApi.make(),
            selectedReceivableInvoice: null,
            headers: [
                {title: 'Date', field: 'created_at', sortable: true},
                {title: 'Company Id', field: 'company_id', sortable: true},
                {title: 'Company Name', field: 'company_name', sortable: false},
                {title: 'Invoice', field: 'invoice_id', sortable: true},
                {title: 'Type', field: 'type', sortable: true},
                {title: 'Amount', field: 'amount', sortable: true},
                {title: 'Status', field: 'status', sortable: true},
            ],
            companyOptions: [],
            invoiceStatusOptions: [],
            tableFilters: [],
            filters: {
                date_range: [],
                per_page: 25
            },
            aggregates: {},
            api: ApiService.make(),
            data: [],
            loading: false,
            paginationData: {},
            rolesPermissions: useRolesPermissions()
        }
    },

    methods: {
        async retrieveRevenueReport(params = {}){
            const dateRange = this.filters.date_range?.map(date =>
                date ? DateTime.fromJSDate(date).setZone('America/Denver').toISO() : undefined
            );

            return await this.api.getRevenueReport({
                ...this.filters,
                ...params,
                date_range: dateRange
            });
        },

        async getReport() {
            this.loading = true

            const res = await this.retrieveRevenueReport();

            this.data = res.data.data.data
            this.aggregates = res.data.aggregates
            this.paginationData = res.data.data.meta

            this.loading = false
        },
        handlePageChange({newPage}){
            this.filters.page = newPage
            this.getReport()
        },
        async handleExportReport(){
            this.exporting = true

            const response = await this.retrieveRevenueReport({
                all: 1
            });

            const formattedItems = response.data.data.data.map(item => [
                item.created_at,
                item.company?.id,
                item.company?.name,
                item.invoice_id,
                item.type,
                item.amount,
                item.status,
            ])

            const headers = [
                'Date',
                'Company Id',
                'Company Name',
                'Invoice Id',
                'Type',
                'Amount',
                'Status',
            ];

            const formattedAggregates = Object.entries(response.data.aggregates)
                .map(([key, value]) => `${key}: $${value}`)
                .map((item, idx) => [idx === 0 ? 'Aggregates' : '', item])

            const formattedFilters = Object.entries(this.filters)
                .map(([key, value]) => `${key}=${JSON.stringify(value)}`)
                .map((item, idx) => [idx === 0 ? 'Filters' : '', item])

            const exportInfoData = [
                ['Exported date', this.$filters.dateFromTimestamp((new Date()).toISOString(), 'usWithTime')],
                ...formattedFilters,
                ...formattedAggregates,
                ['Total Items', formattedItems.length],
            ]

            downloadCsvString(
                headers,
                [
                    ...formattedItems,
                    Array.from({length: headers.length}, () => ''),
                    ...exportInfoData,
                ],
                `revenue_report_${Math.floor(Date.now() / 1000)}`
            )

            this.exporting = false
        },
        async getTableFilters() {
            const response = await this.invoiceTransactionsApi.getInvoiceTransactionFilterOptions();

            this.tableFilters = [
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS,
                    field: 'transaction_types',
                    title: 'Transaction Type',
                    options: response.data.data.filters.transaction_types,
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS,
                    field: 'transaction_scenarios',
                    title: 'Transaction Scenario',
                    options: response.data.data.filters.transaction_scenarios,
                }
            ];
        },
        async searchCompanies(query) {
            this.sharedApi.searchCompanyNamesAndId(query).then(res => {
                if (res.data.data.status === true) {
                    this.companyOptions = res.data.data.companies;
                }
            })
        },
        handleReset() {
            this.filters = {
                date_range: [
                    new Date(),
                    new Date(),
                ],
                per_page: 25
            }

            this.getReport()
        },
    },
    computed: {
        isFinanceOwner(){
            return this.rolesPermissions.hasAnyRole([
                ROLES.FINANCE_OWNER,
            ])
        }
    },
}
</script>
