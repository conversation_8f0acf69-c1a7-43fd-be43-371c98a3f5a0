<template>
    <div>
        <loading-spinner class="col-span-full" :dark-mode="darkMode" v-if="loading"/>
        <div v-for="profile in profiles">
            <simple-card :dark-mode="darkMode">
                <div class="p-5 gap-4 flex flex-inline h-full">
                    <div class="grid grid-cols-4 gap-2">
                        <labeled-value label="Threshold">
                            {{$filters.currency(profile.threshold)}}
                        </labeled-value>
                        <labeled-value label="Max Charge Attempts">
                            {{ profile.charge_attempts }}
                        </labeled-value>
                        <labeled-value label="Payment Method" class="col-span-2">
                            <payment-method-badge
                                :type="profile.payment_method"
                                :reference="profile?.payment_method_data?.number ? `${profile?.payment_method_data?.number} ${profile?.payment_method_data?.expiry}` : null"
                            />
                        </labeled-value>
                        <labeled-value label="Process Auto">
                            {{profile.process_auto ? 'Yes' : 'No'}}
                        </labeled-value>
                        <labeled-value label="Preferred">
                            {{profile.default ? 'Yes': 'No'}}
                        </labeled-value>
                        <labeled-value label="Due in Days">
                            {{ profile.due_in_days}}
                        </labeled-value>
                        <labeled-value label="Invoice Template">
                            {{ profile.invoice_template_name ?? 'Automatic'}}
                        </labeled-value>
                        <labeled-value label="Frequency" class="col-span-3">
                            {{ simpleFrequencyHelper.getHumanReadableText(profile.frequency_type, profile.frequency_data) }}
                        </labeled-value>
                    </div>
                    <simple-icon class="self-center" clickable @click="toggleCreateBillingProfileModal(true, profile)" :color="simpleIcon.colors.BLUE" :size="simpleIcon.sizes.MD" :dark-mode="darkMode" :icon="simpleIcon.icons.COG_SIX_TOOTH"/>
                </div>
            </simple-card>
        </div>
        <div>
            <button
                class="text-primary-500 text-sm font-semibold rounded-md px-5 py-2.5" :class="[darkMode ? 'bg-dark-background' : 'bg-primary-50 hover:bg-primary-100']"
                @click="toggleCreateBillingProfileModal(true)">
                + Add Billing Profile
            </button>
        </div>
        <edit-billing-profile-modal
            v-if="showBillingProfileModal"
            :dark-mode="darkMode"
            :data="selectedProfile"
            @close="toggleCreateBillingProfileModal(false)"
            @confirm="handleProfileConfirmed"
            :company-id="companyId"
        />
    </div>
</template>
<script>
import ApiService from "../../../BillingManagement/services/billing-profiles-api.js";
import Api from "../../../Billing/services/company-billing-profiles.js";
import SimpleCard from "../../../MarketingCampaign/SimpleCard.vue";
import LabeledValue from "../../../Shared/components/LabeledValue.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import EditBillingProfileModal from "../../../BillingManagement/components/Modals/EditBillingProfileModal.vue";
import PaymentMethodBadge from "../../../Billing/InvoicePaymentsModule/PaymentMethodBadge.vue";
import {useSimpleFrequencyHelper} from "../../../../../composables/useSimpleFrequencyHelper.js";
import Badge from "../../../Shared/components/Badge.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
const simpleIcon = useSimpleIcon()
export default {
    name: "BillingDetails",
    components: {
        LoadingSpinner,
        Badge, PaymentMethodBadge, EditBillingProfileModal, SimpleIcon, LabeledValue, SimpleCard},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Object,
            default: {},
        }
    },
    data() {
        return {
            profiles: [],
            loading: false,
            apiService: ApiService.make(),
            companyBillingProfilesApi: Api.make(),
            showBillingProfileModal: false,
            selectedProfile: null,
            simpleIcon,
            simpleFrequencyHelper: useSimpleFrequencyHelper(),
        }
    },
    created() {
        this.getBillingProfiles();
    },
    methods: {
        toggleCreateBillingProfileModal(visible, profileData = {}){
            this.showBillingProfileModal = visible
            this.selectedProfile = profileData
        },
        handleProfileConfirmed() {
            this.toggleCreateBillingProfileModal(false)
            this.getBillingProfiles();
        },
        async getBillingProfiles() {
            this.loading = true;
            const response = await this.apiService.getBillingProfiles({company_id: this.companyId, all: true});
            this.profiles = response.data.data;
            this.loading = false;
        }
    }

}
</script>
