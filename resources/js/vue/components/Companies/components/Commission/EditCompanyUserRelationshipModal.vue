<template>
    <div>
        <alerts-container :dark-mode="darkMode" v-if="alertActive" :alert-type="alertType" :text="alertText" />
        <Modal
            :dark-mode="darkMode"
            @close="$emit('close')"
            @confirm="handleConfirm"
            small
            no-min-height
        >
            <template #header>
                Edit Company Manager
            </template>
            <template #content>
                <div class="grid grid-cols-2 gap-2">
                    <labeled-value :loading="loading" label="Name">
                        {{relation.name}}
                    </labeled-value>
                    <labeled-value :loading="loading" label="Commissionable At">
                        <Datepicker
                            :dark-mode="darkMode"
                            v-model="relation.commissionable"
                            :dark="darkMode"
                            teleport="body"
                            timezone="America/Denver"
                        />
                    </labeled-value>
                    <labeled-value :loading="loading" label="Role">
                        {{relation.role}}
                    </labeled-value>
                    <labeled-value :loading="loading" label="Commissionable To">
                        <Datepicker
                            :dark-mode="darkMode"
                            v-model="relation.deleted_at"
                            :dark="darkMode"
                            teleport="body"
                            :max-date="new Date()"
                            timezone="America/Denver"
                        />
                    </labeled-value>
                </div>

            </template>
        </Modal>
    </div>
</template>
<script>
import Modal from "../../../Shared/components/Modal.vue";
import ApiService from "./service/api.js";
import LabeledValue from "../../../Shared/components/LabeledValue.vue";
import Datepicker from "@vuepic/vue-datepicker";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../../../mixins/alerts-mixin.js";

export default {
    name: "EditCompanyUserRelationshipModal",
    components: {AlertsContainer, Datepicker, LabeledValue, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyUserRelationId: {
            type: Number,
            required: true,
        }
    },
    data() {
        return {
            api: ApiService.make(),
            loading: false,
            relation: {},
        }
    },
    emits: ['close'],
    mixins: [AlertsMixin],
    created() {
        this.getCompanyUserRelationship();
    },
    methods: {
        async getCompanyUserRelationship() {
            this.loading = true;
            this.api.getCompanyUserRelation(this.companyUserRelationId)
                .then(resp => {
                    this.relation = resp.data.data;
                })
                .catch(error => {
                    this.showAlert("error", error.response.data.message)
                })
                .finally(() => this.loading = false)
        },
        async handleConfirm() {
            this.api.updateCompanyUserRelation(this.relation)
                .then(() => {
                    this.$emit('close','Manager Updated Successfully')
                })
                .catch(error => {
                    this.showAlert("error", error.response.data.message)
                });
        }
    }
}
</script>
