<template>

    <div class="main-layout font-body">
        <alerts-container :text="alertText" :alert-type="alertType" v-if="alertActive" :dark-mode="darkMode"/>

        <div class="flex relative">
            <div class="ml-10 w-72 flex-shrink-0 relative z-0"></div>
            <div class="w-72 flex-shrink-0 fixed top-[5.25rem] left-10 border rounded-lg z-40" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                <!-- Back Button -->
                <div>
                    <a href="/companies" class="text-base text-primary-500 font-medium leading-none px-5 py-4 flex items-center border-b" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                        <svg class="mr-2" width="7" height="12" viewBox="0 0 7 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M6.70711 11.7071C6.31658 12.0976 5.68342 12.0976 5.29289 11.7071L0.292894 6.70711C-0.0976305 6.31658 -0.0976304 5.68342 0.292894 5.29289L5.29289 0.292893C5.68342 -0.0976316 6.31658 -0.0976315 6.70711 0.292893C7.09763 0.683417 7.09763 1.31658 6.70711 1.70711L2.41421 6L6.70711 10.2929C7.09763 10.6834 7.09763 11.3166 6.70711 11.7071Z" fill="#0081FF"/></svg>
                        Back to companies list
                    </a>
                </div>
                <!-- Company -->
                <div class="p-5 border-b" :class="[darkMode ? 'border-dark-border text-white' : 'border-light-border text-slate-900']">
                    <h3 class="text-lg font-bold mb-1">{{ company.name }}</h3>
                    <ruleset-score v-for="score in company.ruleset_scores" :score="score" :dark-mode="darkMode" />
                    <a v-if="company.website_verified_url ?? company.website"
                       :title="company.website_verified_url ? '' : 'Warning: Website not verified.'"
                       :href="company.website_verified_url" target="_blank"
                       class="inline-block text-sm truncate items-center font-medium w-[14rem] mb-1"
                       :class="[darkMode ? 'text-slate-400' : 'text-slate-500', company.website_verified_url ? 'hover:text-primary-500' : '']">
                        <svg v-if="company.website_verified_url" class="text-primary-500 fill-current mr-1 inline w-3 h-3" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M4.26686 1.45486C4.91012 1.4035 5.52077 1.15049 6.01186 0.73186C6.56652 0.259451 7.27129 0 7.99986 0C8.72843 0 9.4332 0.259451 9.98786 0.73186C10.4789 1.15049 11.0896 1.4035 11.7329 1.45486C12.4593 1.51292 13.1413 1.82782 13.6566 2.34313C14.1719 2.85843 14.4868 3.54043 14.5449 4.26686C14.5959 4.90986 14.8489 5.52086 15.2679 6.01186C15.7403 6.56652 15.9997 7.27129 15.9997 7.99986C15.9997 8.72843 15.7403 9.4332 15.2679 9.98786C14.8492 10.4789 14.5962 11.0896 14.5449 11.7329C14.4868 12.4593 14.1719 13.1413 13.6566 13.6566C13.1413 14.1719 12.4593 14.4868 11.7329 14.5449C11.0896 14.5962 10.4789 14.8492 9.98786 15.2679C9.4332 15.7403 8.72843 15.9997 7.99986 15.9997C7.27129 15.9997 6.56652 15.7403 6.01186 15.2679C5.52077 14.8492 4.91012 14.5962 4.26686 14.5449C3.54043 14.4868 2.85843 14.1719 2.34313 13.6566C1.82782 13.1413 1.51292 12.4593 1.45486 11.7329C1.4035 11.0896 1.15049 10.4789 0.73186 9.98786C0.259451 9.4332 0 8.72843 0 7.99986C0 7.27129 0.259451 6.56652 0.73186 6.01186C1.15049 5.52077 1.4035 4.91012 1.45486 4.26686C1.51292 3.54043 1.82782 2.85843 2.34313 2.34313C2.85843 1.82782 3.54043 1.51292 4.26686 1.45486ZM11.7069 6.70686C11.889 6.51826 11.9898 6.26566 11.9875 6.00346C11.9853 5.74126 11.8801 5.49045 11.6947 5.30504C11.5093 5.11963 11.2585 5.01446 10.9963 5.01219C10.7341 5.00991 10.4815 5.1107 10.2929 5.29286L6.99986 8.58586L5.70686 7.29286C5.51826 7.1107 5.26565 7.00991 5.00346 7.01219C4.74126 7.01446 4.49045 7.11963 4.30504 7.30504C4.11963 7.49045 4.01446 7.74126 4.01219 8.00346C4.00991 8.26566 4.1107 8.51826 4.29286 8.70686L6.29286 10.7069C6.48039 10.8943 6.7347 10.9996 6.99986 10.9996C7.26502 10.9996 7.51933 10.8943 7.70686 10.7069L11.7069 6.70686Z"/>
                        </svg>
                        <svg title="Warning: Website not verified." v-else class="text-amber-500 fill-current mr-1 inline w-3 h-3" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 1.6C6.30261 1.6 4.67475 2.27428 3.47452 3.47452C2.27428 4.67475 1.6 6.30261 1.6 8C1.6 8.84046 1.76554 9.67269 2.08717 10.4492C2.4088 11.2257 2.88022 11.9312 3.47452 12.5255C4.06881 13.1198 4.77434 13.5912 5.55083 13.9128C6.32731 14.2345 7.15954 14.4 8 14.4C8.84046 14.4 9.67269 14.2345 10.4492 13.9128C11.2257 13.5912 11.9312 13.1198 12.5255 12.5255C13.1198 11.9312 13.5912 11.2257 13.9128 10.4492C14.2345 9.67269 14.4 8.84046 14.4 8C14.4 6.30261 13.7257 4.67475 12.5255 3.47452C11.3253 2.27428 9.69739 1.6 8 1.6ZM2.34315 2.34315C3.84344 0.842855 5.87827 0 8 0C10.1217 0 12.1566 0.842855 13.6569 2.34315C15.1571 3.84344 16 5.87827 16 8C16 9.05057 15.7931 10.0909 15.391 11.0615C14.989 12.0321 14.3997 12.914 13.6569 13.6569C12.914 14.3997 12.0321 14.989 11.0615 15.391C10.0909 15.7931 9.05057 16 8 16C6.94943 16 5.90914 15.7931 4.93853 15.391C3.96793 14.989 3.08601 14.3997 2.34315 13.6569C1.60028 12.914 1.011 12.0321 0.608964 11.0615C0.206926 10.0909 0 9.05058 0 8C4.76837e-08 5.87827 0.842855 3.84344 2.34315 2.34315ZM8 4C8.44183 4 8.8 4.35817 8.8 4.8V8C8.8 8.44183 8.44183 8.8 8 8.8C7.55817 8.8 7.2 8.44183 7.2 8V4.8C7.2 4.35817 7.55817 4 8 4ZM7.2 11.2C7.2 10.7582 7.55817 10.4 8 10.4H8.008C8.44983 10.4 8.808 10.7582 8.808 11.2C8.808 11.6418 8.44983 12 8.008 12H8C7.55817 12 7.2 11.6418 7.2 11.2Z"/>
                        </svg>
                        {{ company.website_verified_url ?? company.website }}
                    </a>
                    <div class="flex items-center mb-3"
                         :class="editingBasicInfo ? ' w-full' : ''">
                        <svg class="text-slate-500 fill-current mr-2 inline w-3" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M15.7061 12.5333L12.0551 9.21377C11.8825 9.05691 11.6558 8.97325 11.4227 8.98046C11.1895 8.98766 10.9684 9.08517 10.8058 9.25239L8.65655 11.4627C8.13922 11.3639 7.09917 11.0397 6.02859 9.9718C4.958 8.90032 4.63377 7.85757 4.53767 7.34383L6.7462 5.19368C6.91363 5.03123 7.01128 4.81001 7.01848 4.57684C7.02569 4.34367 6.94189 4.11684 6.78482 3.94436L3.46619 0.294314C3.30905 0.121293 3.09065 0.0163429 2.85738 0.00175298C2.62411 -0.0128369 2.39434 0.0640828 2.21687 0.216176L0.2679 1.88762C0.112621 2.04346 0.0199401 2.25087 0.00743838 2.47051C-0.00603375 2.69505 -0.262902 8.01385 3.86137 12.1399C7.45933 15.737 11.9662 16.0001 13.2074 16.0001C13.3889 16.0001 13.5002 15.9947 13.5299 15.9929C13.7495 15.9807 13.9568 15.8876 14.1119 15.7316L15.7824 13.7817C15.9351 13.6048 16.0126 13.3752 15.9983 13.142C15.9841 12.9087 15.8792 12.6903 15.7061 12.5333Z"/>
                        </svg>
                        <p
                            class="text-sm font-medium hover:text-primary-500"
                            :class="[company.phone ? 'cursor-pointer' : '', darkMode ? 'text-slate-400' : 'text-slate-500']"
                            @click="dialCompanyPhone"
                        >
                            {{ $filters.formatPhoneNumber(company.phone) || '-' }}
                        </p>
                    </div>
                    <div class="inline-flex items-center gap-4">
                        <SocialMediaLinks :links="company.socialMediaLinks" :dark-mode="darkMode" />
                    </div>
                </div>
                <!-- Menu -->
                <div class="h-[calc(45vh-8rem)] overflow-y-auto">
                    <ol>
                        <li v-for="tab in filteredTabs" :key="tab"
                            @click="selectTab(tab.name)"
                            class="px-5 py-2 cursor-pointer relative"
                            :class="[
                                (darkMode ? (selectedTab === tab.name ? 'text-primary-500 font-semibold bg-dark-background' : 'hover:bg-dark-border hover:text-white font-medium text-slate-200')
                                 : (selectedTab === tab.name ? 'text-primary-500 font-semibold bg-primary-50' : 'hover:bg-slate-100 hover:text-slate-900 font-medium text-slate-700'))]">
                            {{tab.name}}
                            <span v-if="tab.redCount" :class="[darkMode ? 'text-rose-400 bg-dark-background' : 'text-rose-700 bg-rose-100', 'group leading-4 hidden ml-1 px-2 rounded-full text-[11px] font-bold md:inline-block relative']">{{tab.redCount}}</span>
                            <span v-if="tab.greenCount" :class="[darkMode ? 'text-emerald-400 bg-dark-background' : 'text-emerald-700 bg-emerald-100', 'group leading-4 hidden ml-1 px-2 rounded-full text-[11px] font-bold md:inline-block relative']">{{tab.greenCount}}</span>
                            <span v-if="tab.yellowCount" :class="[darkMode ? 'text-amber-400 bg-dark-background' : 'text-amber-700 bg-amber-100', 'group leading-4 hidden ml-1 px-2 rounded-full text-[11px] font-bold md:inline-block relative']">{{tab.yellowCount}}</span>
                            <span v-if="selectedTab === tab.name" class="absolute left-0 inset-y-0 h-full w-1 bg-primary-500"></span>
                        </li>
                    </ol>
                </div>
                <!-- Shadow / Reset Options -->
                <div class="grid gap-3 items-center px-5 py-5 border-t" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                    <CustomButton v-if="company.can_shadow" class="w-full" color="primary-outline" :dark-mode="darkMode" @click="dashboardSelected">
                        <svg class="fill-current text-primary-500 mr-2" width="13" height="9" viewBox="0 0 13 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6.1341 5.78572C6.4751 5.78572 6.80212 5.65026 7.04324 5.40915C7.28436 5.16803 7.41982 4.841 7.41982 4.50001C7.41982 4.15902 7.28436 3.83199 7.04324 3.59087C6.80212 3.34975 6.4751 3.21429 6.1341 3.21429C5.79311 3.21429 5.46608 3.34975 5.22497 3.59087C4.98385 3.83199 4.84839 4.15902 4.84839 4.50001C4.84839 4.841 4.98385 5.16803 5.22497 5.40915C5.46608 5.65026 5.79311 5.78572 6.1341 5.78572Z"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M0 4.5C0.819 1.89193 3.25543 0 6.13414 0C9.01286 0 11.4493 1.89193 12.2683 4.5C11.4493 7.10807 9.01286 9 6.13414 9C3.25543 9 0.819 7.10807 0 4.5ZM8.70557 4.5C8.70557 5.18199 8.43465 5.83604 7.95242 6.31827C7.47018 6.80051 6.81613 7.07143 6.13414 7.07143C5.45216 7.07143 4.7981 6.80051 4.31587 6.31827C3.83363 5.83604 3.56271 5.18199 3.56271 4.5C3.56271 3.81801 3.83363 3.16396 4.31587 2.68173C4.7981 2.19949 5.45216 1.92857 6.13414 1.92857C6.81613 1.92857 7.47018 2.19949 7.95242 2.68173C8.43465 3.16396 8.70557 3.81801 8.70557 4.5V4.5Z"/>
                        </svg>
                        Shadow Dashboard
                    </CustomButton>
                    <p class="text-slate-500 text-center font-semibold text-sm" v-else>Create User to Shadow</p>
                    <div class="flex items-center">
                        <CustomButton @click="toggleRequestOwnershipModal" class="w-full" :dark-mode="darkMode" color="primary-outline">
                            Request Ownership
                        </CustomButton>
                    </div>
                    <div v-if="hasEditRights" class="flex items-center">
                        <CustomButton class="w-full" :dark-mode="darkMode" color="primary-outline" @click="recalculateStatus">
                            Recalculate Status
                        </CustomButton>
                    </div>
                    <div v-if="canResetCrmRejections" class="flex items-center">
                        <CustomButton class="w-full" :dark-mode="darkMode" color="red-outline" @click="resetCrmRejections">
                            Reset CRM Rejections
                        </CustomButton>
                    </div>
                </div>
            </div>
            <div class="flex-grow flex-auto relative ml-4 mr-10 mt-5" :class="[darkMode ? 'bg-dark-background' : 'bg-light-background']">

                <!-- Fixed Company Info Panel -->
                <div class="border-b relative border rounded-lg mb-4" :class="[darkMode ? 'text-white bg-dark-module border-dark-border' : 'bg-light-module border-light-border text-slate-900']">
                    <div class="px-10 absolute inset-0 z-40 flex items-center justify-center" :class="{ 'bg-dark-module': darkMode, 'bg-light-module': !darkMode }"   v-if="saving">
                        <loading-spinner></loading-spinner>
                    </div>
                    <div class="p-5">
                        <div class="flex items-center justify-between">
                            <h5 class="text-sm uppercase text-primary-500 font-bold leading-tight pb-3">Main Info</h5>
                            <div v-if="hasEditRights" class="flex items-center">
                                <CustomButton :dark-mode="darkMode" color="primary-outline" @click="editBasicInfo">
                                    <svg class="mr-2" width="11" height="13" viewBox="0 0 11 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M8.25 0L10.3125 1.95117L8.74019 3.43926L6.67769 1.48809L8.25 0ZM0 7.79688V9.74805H2.0625L7.76806 4.35827L5.70556 2.40709L0 7.79688ZM0 11.6992H11V13H0V11.6992Z" fill="#0081FF"/>
                                    </svg>
                                    Edit Main Info
                                </CustomButton>
                            </div>
                        </div>
                        <div class="md:col-span-2 columns-1 lg:columns-2 2xl:columns-3 space-y-1" :class="[darkMode ? 'text-slate-400' : 'text-slate-700']">
                            <div>
                                <p class="font-semibold text-sm inline-flex items-center">
                                    Lead Rejection:
                                    <span class="font-normal ml-2">
                                            <span-hover-tooltip><template #title>{{
                                                    company?.lead?.rejection_percentage?.manual != null ? company?.lead?.rejection_percentage?.manual + '%' : 'N/A'
                                                }}</template>Manual</span-hover-tooltip> |
                                            <span-hover-tooltip><template #title>{{
                                                    company?.lead?.rejection_percentage?.crm != null ? company?.lead?.rejection_percentage?.crm + '%' : 'N/A'
                                                }}</template>CRM</span-hover-tooltip> |
                                            <span-hover-tooltip><template #title>{{
                                                    company?.lead?.rejection_percentage?.overall != null ? company?.lead?.rejection_percentage?.overall + '%' : 'N/A'
                                                }}</template>Overall</span-hover-tooltip>
                                    </span>
                                </p>
                            </div>
                            <div>
                                <p class="font-semibold text-sm inline-flex items-center">
                                    Direct Leads Rejection:
                                    <span class="font-normal ml-2">
                                            <span-hover-tooltip><template #title>{{
                                                    company?.direct_leads?.rejection_percentage?.manual != null ? company?.direct_leads?.rejection_percentage?.manual + '%' : 'N/A'
                                                }}</template>Manual</span-hover-tooltip> |
                                            <span-hover-tooltip><template #title>{{
                                                    company?.direct_leads?.rejection_percentage?.crm != null ? company?.direct_leads?.rejection_percentage?.crm + '%' : 'N/A'
                                                }}</template>CRM</span-hover-tooltip> |
                                            <span-hover-tooltip><template #title>{{
                                                    company?.direct_leads?.rejection_percentage?.overall != null ? company?.direct_leads?.rejection_percentage?.overall + '%' : 'N/A'
                                                }}</template>Overall</span-hover-tooltip>
                                    </span>
                                </p>
                            </div>
                            <div>
                                <div class="inline-flex items-center ">
                                    <p class="font-semibold text-sm mr-2">Never Exceed Budget:</p>
                                    <svg v-if="company.neverExceedBudget === true" width="12" height="10" viewBox="0 0 12 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M4.27411 6.5197L1.62626 3.69335L0 5.43719L4.27642 10L12 1.74138L10.3714 0L4.27411 6.5197Z" fill="#00AE07"/>
                                    </svg>
                                    <svg v-else-if="company.neverExceedBudget === false" width="9" height="9" viewBox="0 0 9 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0.219369 0.228966C0.359874 0.088363 0.550413 0.00937635 0.749086 0.00937635C0.947759 0.00937635 1.1383 0.088363 1.2788 0.228966L4.49532 3.44872L7.71183 0.228966C7.78095 0.157334 7.86362 0.100197 7.95503 0.0608901C8.04644 0.0215834 8.14476 0.00089368 8.24424 2.83178e-05C8.34373 -0.********* 8.44239 0.0181391 8.53447 0.0558497C8.62655 0.0935604 8.7102 0.14925 8.78055 0.21967C8.8509 0.290089 8.90653 0.373828 8.94421 0.466001C8.98188 0.558173 9.00084 0.656933 8.99997 0.756517C8.99911 0.856102 8.97844 0.954516 8.93917 1.04602C8.8999 1.13752 8.84282 1.22028 8.77126 1.28947L5.55475 4.50922L8.77126 7.72897C8.90774 7.87042 8.98326 8.05987 8.98156 8.25652C8.97985 8.45316 8.90105 8.64127 8.76214 8.78033C8.62322 8.91939 8.4353 8.99826 8.23885 8.99997C8.0424 9.00168 7.85314 8.92608 7.71183 8.78947L4.49532 5.56972L1.2788 8.78947C1.13749 8.92608 0.948232 9.00168 0.751782 8.99997C0.555332 8.99826 0.367412 8.91939 0.228496 8.78033C0.0895797 8.64127 0.0107821 8.45316 0.00907497 8.25652C0.00736788 8.05987 0.082888 7.87042 0.219369 7.72897L3.43588 4.50922L0.219369 1.28947C0.0789073 1.14882 0 0.958089 0 0.759216C0 0.560343 0.0789073 0.369612 0.219369 0.228966V0.228966Z" fill="#E13131"/>
                                    </svg>
                                    <span v-else class="text-sm">
                                                {{ company.neverExceedBudget }}
                                            </span>
                                </div>
                            </div>
                            <div>
                                <div class="font-semibold text-sm inline-flex items-center">
                                    Overall Budget Usage:
                                    <div class="flex items-center gap-2 font-normal ml-2">
                                        <loading-spinner v-if="loadingBudgetUsage" size="xs" :dark-mode="darkMode"/>
                                        <span-hover-tooltip v-else extra-large>
                                            <template #title>
                                                {{ budgetUsage?.overall }}
                                            </template>
                                            <div class="grid grid-cols-3">
                                                <div class="font-medium col-span-2">Campaign Name</div>
                                                <div class="font-medium">Budget Usage</div>
                                            </div>
                                            <div v-for="campaign in budgetUsage?.campaigns" class="grid grid-cols-3 col-span-3">
                                                <div class="col-span-2">{{ campaign?.campaign_name }}</div>
                                                <div class="flex gap-1">
                                                    <div>{{ campaign?.budget_usage_today }}</div>
                                                    <div v-if="campaign?.budget_type === 'No Limit'">(Unlimited)</div>
                                                </div>
                                            </div>
                                        </span-hover-tooltip>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="inline-flex items-center">
                                    <p class="font-semibold text-sm mr-2">Google Rating:</p>
                                    <div title="Google Rating" class="text-sm">
                                        <StarRating v-if="company?.google_rating" :rating="company?.google_rating"
                                                    star-width="w-20"></StarRating>
                                        <i class="text-gray-400" v-else>N/A</i>
                                        <span v-if="company?.google_review_count">({{
                                                company?.google_review_count
                                            }})</span>
                                        <i class="text-gray-400" v-else>(N/A)</i>
                                    </div>
                                </div>
                            </div>
                            <!--  Commenting out Territory Managers as we no longer use them right now. -->
<!--                            <div>-->
<!--                                <div class="text-sm inline-flex items-center w-full">-->
<!--                                    <span class="font-semibold mr-2">Territory Managers:</span>-->
<!--                                    <div class="flex items-center">-->
<!--                                        <div v-if="company.territory_managers?.length" class="flex items-center gap-2">-->
<!--                                            <Badge :dark-mode="darkMode" color="primary">{{ company.territory_managers[0].name }}</Badge>-->
<!--                                            <div v-if="company.territory_managers.length > 1"-->
<!--                                                class="group cursor-default relative">-->
<!--                                                <Badge :dark-mode="darkMode" color="primary">+ {{ company.territory_managers.length - 1 }} more</Badge>-->
<!--                                                <div class="absolute top-6 right-0 invisible group-hover:visible shadow-lg rounded-lg z-50 min-w-[8rem] p-3 space-y-2 border" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">-->
<!--                                                    <p v-for="manager in company.territory_managers.slice(1)" class="text-sm">{{ manager.name }}</p>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                        <div v-else>-->
<!--                                            - -->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            <div>
                                <div class="inline-flex items-center ">
                                    <p class="font-semibold text-sm mr-2">Success Manager:</p>
                                    <p class=" text-sm">{{company.success_manager.name || 'None Assigned' }}</p>
                                </div>
                            </div>
                            <div>
                                <div class="inline-flex items-center ">
                                    <p class="font-semibold text-sm mr-2">BDM:</p>
                                    <p class=" text-sm">{{company?.business_development_manager?.name || 'None Assigned' }}</p>
                                </div>
                            </div>
                            <div>
                                <PreAssignedAccountManager :dark-mode="darkMode"/>
                            </div>
                            <div>
                                <div class="inline-flex items-center ">
                                    <p class="font-semibold text-sm mr-2">Account Manager:</p>
                                    <p class=" text-sm">{{company.account_manager.name || 'None Assigned' }}</p>
                                </div>
                            </div>
                            <div>
                                <div class="inline-flex items-center ">
                                    <p class="font-semibold text-sm mr-2">Onboarding Manager:</p>
                                    <p class=" text-sm">{{company.onboarding_manager?.name || 'None Assigned' }}</p>
                                </div>
                            </div>
                            <div>
                                <div class="inline-flex items-center ">
                                    <p class="font-semibold text-sm mr-2">Sales Development Representative:</p>
                                    <p class=" text-sm">{{company.sales_development_representative?.name || 'None Assigned' }}</p>
                                </div>
                            </div>
                            <div>
                                <div class="text-sm inline-flex items-center">
                                    <span class="font-semibold mr-2">Office:</span>
                                    <div class="relative group z-30">
                                        <p class="truncate max-w-[20rem]">{{ company.mainOfficeLocation }}</p>
                                        <p class="absolute top-0 left-0 invisible group-hover:visible p-2 rounded border" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">{{ company.mainOfficeLocation }}</p>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="text-sm inline-flex items-center  w-full">
                                    <span class="font-semibold mr-2">Employees:</span>
                                    <span>{{ company.employees ?? '-' }}</span>

                                </div>
                            </div>
                            <div>
                                <div class="text-sm inline-flex items-center w-full">
                                    <span class="font-semibold mr-2">Revenue:</span>
                                    <span>{{ company.revenue ? $filters.currency(company.revenue, { precision: 0 }) : '-' }}</span>
                                </div>
                            </div>
                            <div v-if="companyMetrics?.avg_spend">
                                <div class="text-sm inline-flex items-center w-full">
                                    <span class="font-semibold mr-2">Average Ad Spend:</span>
                                    <span-hover-tooltip>
                                        <template #title>{{ companyMetrics.avg_spend }}</template>
                                        {{ companyMetrics && companyMetrics.last_calculated_at ? `Last calculated on: ${companyMetrics.last_calculated_at}` : 'Not yet calculated' }}
                                    </span-hover-tooltip>
                                </div>
                            </div>
                            <div v-if="mostRecentAdSpendEstSpent">
                                <div class="text-sm inline-flex items-center w-full">
                                    <span class="font-semibold mr-2">Most Recent Ad Spend Est:</span>
                                    <span> {{ mostRecentAdSpendEstSpent }} <span v-if="mostRecentAdSpendEstPeriod">({{ mostRecentAdSpendEstPeriod }})</span></span>
                                </div>
                            </div>
                            <div>
                                <div class="text-sm inline-flex items-center w-full">
                                    <span class="font-semibold mr-2">Legacy:</span>
                                    <span v-if="company.legacy_id" class="text-primary-500 truncate">
                                          <a :href="getLegacyAdminCompanyUrl(company.legacy_id)" target="_blank">
                                              {{ company.name }}
                                          </a>
                                      </span>
                                </div>
                            </div>
                            <div>
                                <div class="text-sm inline-flex items-center w-full">
                                    <span class="font-semibold mr-2">Industries:</span>
                                    <div class="flex items-center">
                                        <div v-if="company.industries.length > 1" class="flex items-center gap-2">
                                            <Badge :dark-mode="darkMode" color="green">{{ company.industries[0].name }}</Badge>
                                            <div class="relative group cursor-default">
                                                <Badge :dark-mode="darkMode" color="green">+ {{ company.industries.length - 1 }} more</Badge>
                                                <div class="absolute top-6 right-0 invisible group-hover:visible shadow-lg rounded-lg z-50 min-w-[8rem] p-3 space-y-2 border" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                                                    <p v-for="industry in company.industries.slice(1)" class="text-sm">{{ industry.name }}</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div v-else v-for="industry in company.industries">
                                            <Badge :dark-mode="darkMode" color="primary">{{ industry.name }}</Badge>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="text-sm inline-flex items-center w-full">
                                    <span class="font-semibold mr-2">QR Top 500:</span>
                                    <div>
                                        <Badge v-if="company.qr_top_500_company" color="green" :dark-mode="darkMode">
                                            <svg width="15" height="12" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M4.72946 9.12601L1.43646 5.83301L0.0224609 7.24701L4.72946 11.954L14.4365 2.24701L13.0225 0.833008L4.72946 9.12601Z" fill="currentColor"/>
                                            </svg>
                                        </Badge>
                                        <Badge v-else color="red" :dark-mode="darkMode">
                                            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M10.192 0.34375L5.94897 4.58575L1.70697 0.34375L0.292969 1.75775L4.53497 5.99975L0.292969 10.2418L1.70697 11.6558L5.94897 7.41375L10.192 11.6558L11.606 10.2418L7.36397 5.99975L11.606 1.75775L10.192 0.34375Z" fill="currentColor"/>
                                            </svg>
                                        </Badge>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="text-sm inline-flex items-center w-full">
                                    <span class="font-semibold mr-2">Known Lead Buyer:</span>
                                    <div>
                                        <Badge v-if="company.known_lead_buyer" color="green" :dark-mode="darkMode">
                                            <svg width="15" height="12" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M4.72946 9.12601L1.43646 5.83301L0.0224609 7.24701L4.72946 11.954L14.4365 2.24701L13.0225 0.833008L4.72946 9.12601Z" fill="currentColor"/>
                                            </svg>
                                        </Badge>
                                        <Badge v-else color="red" :dark-mode="darkMode">
                                            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M10.192 0.34375L5.94897 4.58575L1.70697 0.34375L0.292969 1.75775L4.53497 5.99975L0.292969 10.2418L1.70697 11.6558L5.94897 7.41375L10.192 11.6558L11.606 10.2418L7.36397 5.99975L11.606 1.75775L10.192 0.34375Z" fill="currentColor"/>
                                            </svg>
                                        </Badge>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-3 mt-5 items-center">
                            <div v-if="loadingSaleStatus" class="w-32">
                                <loading-spinner  size="xs" :dark-mode="darkMode" />
                            </div>
                            <Badge
                                class="hover:opacity-75 cursor-pointer"
                                :dark-mode="darkMode"
                                color="primary"
                                v-else-if="!editingSalesStatus"
                                @click="toggleEditStatus">
                                <svg class="mr-2" width="11" height="13" viewBox="0 0 11 13" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8.25 0L10.3125 1.95117L8.74019 3.43926L6.67769 1.48809L8.25 0ZM0 7.79688V9.74805H2.0625L7.76806 4.35827L5.70556 2.40709L0 7.79688ZM0 11.6992H11V13H0V11.6992Z" fill="currentColor"></path></svg>    {{ company.sales_status }}
                            </Badge>
                            <div class="flex" v-else>
                                <Dropdown
                                    class="min-w-48 mr-2"
                                    :dark-mode="darkMode"
                                    placeholder="Change the sales status"
                                    :options="salesStatusOptions"
                                    v-model="editing.sales_status"
                                    :selected="salesStatusOptions.find(i => i.name === editing.sales_status) || 0"
                                ></Dropdown>
                                <p
                                    @click="saveSalesStatus"
                                    class="flex-shrink-0 mr-2 inline-flex justify-center items-center gap-1 rounded-md border border-primary-500 px-3 py-1 group cursor-pointer text-xs text-primary-500 font-bold"
                                >
                                    Save
                                </p>
                                <button
                                    v-if="editingSalesStatus"
                                    @click="toggleEditStatus"
                                    class="flex-shrink-0 inline-flex justify-center items-center gap-1 rounded-md border border-slate-500 px-3 py-1 group cursor-pointer text-xs text-slate-500 font-bold"
                                >
                                    Cancel
                                </button>
                            </div>
                            <div>
                                <Badge :dark-mode="darkMode" color="primary">{{ company.system_status ?? 'Unknown' }}</Badge>
                            </div>
                            <div>
                                <Badge :dark-mode="darkMode" color="primary">{{ company.campaign_status ?? 'Unknown' }}</Badge>
                            </div>
                            <div>
                                <Badge :dark-mode="darkMode" color="primary">{{ company.admin_status_label ?? 'Unknown' }}</Badge>
                            </div>
                            <div>
                                <Badge :dark-mode="darkMode" :color="company.can_receive_leads ? 'green' : 'red'">
                                    <simple-icon icon="check"  v-if="company.can_receive_leads"></simple-icon>
                                    <simple-icon icon="x-mark" v-else></simple-icon>
                                   {{ company.status ?? 'Unknown' }}
                                </Badge>
                            </div>
                            <div>
                                <Badge :dark-mode="darkMode" color="primary">{{ company.type ? company.type : 'Unknown' }}</Badge>
                            </div>
                        </div>

                        <!-- TODO: disabling the status/admin-status and type update until we finalize the backend flow.
                                   Type/service handling possibly could go through the industry management screen and that's already done.
                         -->
                        <!--                                <div v-else class="grid grid-cols-2 gap-3 items-center mb-1">-->
                        <!--                                    <Dropdown :dark-mode="darkMode" :options="typeOptions" v-model="editing.industry" :selected="editing.industry"-->
                        <!--                                    ></Dropdown>-->
                        <!--                                </div>-->
<!--                        <div class="grid lg:grid-cols-2 gap-16">-->
<!--                            <company-opt-in-names :dark-mode="darkMode" :api="api" :company-id="company.id"></company-opt-in-names>-->
<!--                        </div>-->
                    </div>
                </div>
                <div>
                    <Overview
                        :has-edit-rights="hasEditRights"
                        @send-to-tab="sendToTab"
                        :dark-mode="darkMode"
                        v-if="selectedTab === 'Overview'"
                        :company-id="company.id"
                        :company-name="company.name"
                        :legacy-company-id="company.legacy_id"
                        :current-user-id="currentUserId"
                        :action-categories="actionCategories"
                    />

                    <UsersAndContacts :company-name="company.name" :company-website="company.website" :has-edit-rights="hasEditRights" :dark-mode="darkMode" v-if="selectedTab === 'Users & Contacts'" :company-id="company.id" :company-dashboard-type="companyDashboardType"/>

                    <!-- Disabling edit rights until completed to a satisfactory level. -->
                    <CompanyProfile
                        :has-edit-rights="hasEditRights"
                        :dark-mode="darkMode"
                        v-if="selectedTab === 'Company Profile'"
                        :company-id="company.id"
                        :legacy-company-id="company.legacy_id"
                        @activate-alert="handleActivateAlert"
                    />

                    <LeadsPage :dark-mode="darkMode" v-if="selectedTab === 'Leads'" :company-id="company.id"/>

                    <CompanyMissedLeadsPage :dark-mode="darkMode" v-if="selectedTab === 'Missed Leads'" :company-id="company.id"/>

                    <CampaignsPage :has-edit-rights="hasEditRights" :dark-mode="darkMode" v-if="selectedTab === 'Campaigns'" :company="company" :legacy-company-id="company.legacy_id" :campaign-activity-log-relation-class="campaignActivityLogRelationClass"/>

                    <Revenue :dark-mode="darkMode" v-if="selectedTab === 'Revenue'" :company-id="company.id"/>

                    <InvoicesAndBilling :dark-mode="darkMode" v-if="selectedTab === 'Invoices & Billing'" :company-id="company.id" :company-name="company.name"/>

                    <CompanyContractsPage :dark-mode="darkMode" v-if="selectedTab === 'Contracts'" :company-id="company.id" :company-name="company.name" />

                    <CompanyTasks :dark-mode="darkMode"  v-if="selectedTab === 'Tasks'" :company-id="company.id" :company-name="company.name"/>

                    <BestRevenueScenarioLogs :dark-mode="darkMode" :company-id="company.id" v-if="selectedTab === 'BRS Logs'"/>

                    <CompanyReviews :dark-mode="darkMode" :company-id="company.id" :company-name="company.name" v-if="selectedTab === 'Reviews'"/>
                    <SalesRole :dark-mode="darkMode" :company-id="company.id" :company-name="company.name" v-if="selectedTab === 'Sales Roles'"/>
                    <History :company-id="company.id" :dark-mode="darkMode" v-if="selectedTab === 'Role History'"/>
                    <CommissionManagement :company-id="company.id" :dark-mode="darkMode" v-if="selectedTab === 'Commissions'"/>

                    <CompanyConfigurations v-if="selectedTab === 'Configurations' && canAccessCompanyConfigurations" :dark-mode="darkMode" :company-id="company.id" />

                    <div class="pb-10">
                        <ActivityPage :dark-mode="darkMode" v-if="selectedTab === 'Activity Feed'" :company-id="company.id"/>

                    </div>

                </div>
                <Modal
                    class="z-100"
                    v-if="editingBasicInfo"
                    @close="editBasicInfo"
                    @confirm="saveBasicInfo"
                    :dark-mode="darkMode"
                    :disable-confirm="saving"
                >
                    <template v-slot:header>
                        <h4 class="text-xl font-medium">Edit Company Main Info</h4>
                    </template>
                    <template v-slot:content>
                        <div class="relative">
                            <span class="block text-sm italic pb-4 text-right mr-6"><span class="text-red-600">*</span> Required fields</span>
                            <div class="grid grid-cols-2 gap-6 pr-3 pb-32"
                                :class="[saving ? 'pointer-events-none opacity-50' : '']"
                            >
                                <div>
                                    <p class="text-sm font-semibold pb-1">Company Name <span class="text-red-600">*</span></p>
                                    <CustomInput :dark-mode="darkMode" type="text" :name="company.name" v-model="editing.name"/>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold pb-1">Company Website <span class="text-red-600">*</span></p>
                                    <CustomInput :dark-mode="darkMode" type="text" :name="company.website" v-model="editing.website"/>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold pb-1">Sourced From</p>
                                    <CustomInput :dark-mode="darkMode" type="text" :name="company.sourced_from" v-model="editing.sourced_from"/>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold pb-1">Company Phone</p>
                                    <CustomInput :dark-mode="darkMode" type="text" :name="company.phone" v-model="editing.phone"/>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold pb-1">Success Manager</p>
                                    <Dropdown
                                        :disabled="!company.can_edit_success_manager"
                                        :dark-mode="darkMode"
                                        :options="successManagerOptions"
                                        v-model="editing.success_manager"
                                        :selected="successManagerOptions.find(i => i.id === editing.success_manager) || 0"
                                    />
                                </div>
                                <div>
                                    <p class="text-sm font-semibold pb-1">BDM Manager</p>
                                    <Dropdown
                                        placement="top"
                                        :dark-mode="darkMode"
                                        :disabled="!company.can_edit_business_development_manager"
                                        :options="businessDevelopmentManagerOptions"
                                        v-model="editing.business_development_manager"
                                        :selected="businessDevelopmentManagerOptions.find(i => i.id === editing.business_development_manager) || 0"
                                    ></Dropdown>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold pb-1">Account Manager</p>
                                    <Dropdown
                                        placement="top"
                                        :dark-mode="darkMode"
                                        :disabled="!company.can_edit_account_manager"
                                        :options="accountManagerOptions"
                                        v-model="editing.account_manager"
                                        :selected="accountManagerOptions.find(i => i.id === editing.account_manager) || 0"
                                    ></Dropdown>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold pb-1">Onboarding Manager</p>
                                    <Dropdown
                                        placement="top"
                                        :dark-mode="darkMode"
                                        :disabled="!company.can_edit_onboarding_manager"
                                        :options="onboardingManagerOptions"
                                        v-model="editing.onboarding_manager"
                                        :selected="onboardingManagerOptions.find(i => i.id === editing.onboarding_manager) || 0"
                                    ></Dropdown>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold pb-1">Sales Development Representative</p>
                                    <Dropdown
                                        placement="top"
                                        :dark-mode="darkMode"
                                        :disabled="!company.can_edit_sales_development_representative"
                                        :options="salesDevelopmentRepresentativeOptions"
                                        v-model="editing.sales_development_representative"
                                        :selected="salesDevelopmentRepresentativeOptions.find(i => i.id === editing.sales_development_representative) || 0"
                                    ></Dropdown>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold pb-1">Employees</p>
                                    <CustomInput :dark-mode="darkMode" type="text" :name="company.employees" v-model="editing.employees"/>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold pb-1">Revenue</p>
                                    <CustomInput :dark-mode="darkMode" type="text" :name="company.revenue" v-model="editing.revenue"/>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold pb-1">Office Name</p>
                                    <CustomInput :dark-mode="darkMode" type="text" :name="company.address.name" v-model="editing.address.name"/>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold pb-1">Address 1</p>
                                    <CustomInput :dark-mode="darkMode" type="text" :name="company.address.address_1"
                                                 v-model="editing.address.address_1"/>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold pb-1">Address 2</p>
                                    <CustomInput :dark-mode="darkMode" type="text" :name="company.address.address_2"
                                                 v-model="editing.address.address_2"/>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold pb-1">City</p>
                                    <CustomInput :dark-mode="darkMode" type="text" :name="company.address.city" v-model="editing.address.city"/>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold pb-1">State</p>
                                    <dropdown placement="top"
                                              :dark-mode="darkMode" :placeholder="'Select State'" :options="states"
                                              :name="company.address.state" v-model="editing.address.state"
                                              :key="states.length"></dropdown>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold pb-1">Zip Code</p>
                                    <CustomInput :dark-mode="darkMode" type="text" :name="company.address.zip_code"
                                                 v-model="editing.address.zip_code"/>
                                </div>

                                <div v-if="adminStatusOptions.length">
                                    <p class="text-sm font-semibold pb-1">Company Admin Status: <span class="text-amber-500">{{ company.admin_status_label }}</span></p>
                                    <dropdown
                                        placeholder="Select"
                                        :dark-mode="darkMode"
                                        :options="adminStatusOptions"
                                        v-model="editing.admin_status"
                                    ></dropdown>
                                    <div class="mt-2" v-if="changingAdminStatus">
                                        <CustomInput :dark-mode="darkMode" placeholder="Enter Reason" v-model="adminStatusChangeReason"></CustomInput>
                                    </div>
                                </div>

                                <labeled-value class="col-span-2" label="QR Top 500 Company">
                                    <toggle-switch :dark-mode="darkMode" v-model="editing.qr_top_500_company"></toggle-switch>
                                </labeled-value>
                            </div>
                            <div class="absolute w-full h-full flex top-[-10%]" v-if="saving">
                                <LoadingSpinner :dark-mode="darkMode" />
                            </div>
                        </div>
                    </template>
                </Modal>
                <RequestOwnershipModal
                    v-if="requestOwnershipModal"
                    :dark-mode="darkMode"
                    @close="toggleRequestOwnershipModal"
                    :company-id="company.id"
                    :company-name="company.name"
                >
                </RequestOwnershipModal>
            </div>
        </div>
    </div>
</template>

<script>
import Tab from "../Shared/components/Tab.vue";
import Overview from "./components/Overview.vue";
import UsersAndContacts from "./components/UsersAndContacts.vue";
import CompanyProfile from "./components/CompanyProfile.vue";
import LeadsPage from "./components/LeadsPage.vue";
import CompanyMissedLeadsPage from "./components/CompanyMissedLeadsPage.vue";
import CampaignsPage from "./components/CampaignsPage.vue";
import Revenue from "./components/Revenue.vue";
import InvoicesAndBilling from "./components/InvoicesAndBilling.vue";
import CompanyTasks from "./components/CompanyTasks.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import SharedApiService from "../Shared/services/api";
import LoadingSpinner from "../LeadProcessing/components/LoadingSpinner.vue";
import AlertsContainer from "../LeadProcessing/components/AlertsContainer.vue";
import HasAlertsMixin, {AlertTypes} from "../Shared/mixins/has-alerts-mixin";
import ActivityPage from "./components/ActivityPage.vue";
import LegacyAdminMixin from "../Shared/mixins/legacy-admin-mixin";
import MultiSelect from "../Shared/components/MultiSelect.vue";
import RulesetScore from "./components/RulesetScore.vue";
import DispatchesGlobalEventsMixin from "../../mixins/dispatches-global-events-mixin";
import BestRevenueScenarioLogs from "./components/BestRevenueScenarioLogs.vue";
import CustomInlineSelect from "../Shared/components/CustomInlineSelect.vue";
import {CommunicationRelationTypes} from "../Communications/enums/communication";
import {PERMISSIONS, ROLES, useRolesPermissions} from "../../../stores/roles-permissions.store";
import Modal from "../Shared/components/Modal.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import StarRating from '../Shared/components/StarRating.vue'
import ButtonDropdown from "../Shared/components/ButtonDropdown.vue";
import CompanyConfigurations from "./components/CompanyConfigurations.vue";
import SpanHoverTooltip from '../Shared/components/SpanHoverTooltip.vue'
import CompanyContractsPage from "./components/CompanyContractsPage.vue";
import Tooltip from "../Shared/components/Tooltip.vue";
import SalesRole from "./components/SalesRole/SalesRole.vue";
import History from "./components/Territory/History.vue";
import {useFutureCampaignStore} from "../Campaigns/Wizard/stores/future-campaigns.js";
import useQueryParams from "../../../composables/useQueryParams.js";
import SocialMediaLinks from "../Shared/components/SocialMediaLinks.vue";
import CompanyOptInNames from "./CompanyOptInNames.vue";
import LabeledValue from "../Shared/components/LabeledValue.vue";
import ToggleSwitch from "../Shared/components/ToggleSwitch.vue";
import {useUserStore} from "../../../stores/user-store.js";
import Badge from "../Shared/components/Badge.vue";
import CompanyStatusSelector from "../Shared/components/Company/CompanyStatusSelector.vue";
import RequestOwnershipModal from "./components/RequestOwnershipModal.vue";
import CompanyReviews from "../Companies/components/CompanyReviews.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import CommissionManagement from "./components/Commission/CommissionManagement.vue";
import {useCompanyStore} from "../../../stores/company/company.store.js";
import {useCompanyManagersStore} from "../../../stores/company/company-managers.store.js";
import PreAssignedAccountManager from "./components/PreAssignedAccountManager.vue";
const DEFAULT_SELECTED_TAB = 'Overview'
export default {
    name: "CompanyPage",
    components: {
        PreAssignedAccountManager,
        CommissionManagement,
        SimpleIcon,
        CompanyStatusSelector,
        RequestOwnershipModal,
        CompanyReviews,
        Badge,
        ToggleSwitch,
        LabeledValue,
        CompanyContractsPage,
        CompanyOptInNames,
        History,
        SalesRole,
        Tooltip,
        SpanHoverTooltip,
        CompanyConfigurations,
        StarRating,
        ButtonDropdown,
        CustomButton,
        CustomInput,
        Modal,
        CustomInlineSelect,
        RulesetScore,
        BestRevenueScenarioLogs,
        AlertsContainer,
        LoadingSpinner,
        MultiSelect,
        Dropdown,
        CompanyTasks,
        InvoicesAndBilling,
        Revenue,
        CampaignsPage,
        LeadsPage,
        CompanyProfile,
        UsersAndContacts,
        Overview,
        Tab,
        ActivityPage,
        CompanyMissedLeadsPage,
        SocialMediaLinks,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        initCompany: {
            type: Object,
            default: {}
        },
        accountManagers: {
            type: Array,
            default: ([]),
        },
        successManagers: {
            type: Array,
            default: ([])
        },
        businessDevelopmentManagers: {
            type: Array,
            default: ([])
        },
        onboardingManagers: {
            type: Array,
            default: []
        },
        salesDevelopmentRepresentatives: {
            type: Array,
            default: []
        },
        tab: {
            type: String,
            default: null,
        },
        industries: {
            type: Array,
            default: []
        },
        currentUserId: {
            type: Number,
            default: null,
        },
        campaignActivityLogRelationClass: {
            type: String,
            default: null,
        },
        actionCategories: {
            type: Array,
            default: []
        },
        adminStatuses: {
            type: Array,
            default: []
        }
    },
    mixins: [HasAlertsMixin, LegacyAdminMixin, DispatchesGlobalEventsMixin],
    data() {
        return {
            permissionStore: useRolesPermissions(),
            campaignStore: useFutureCampaignStore(),
            userStore: useUserStore(),
            companyStore: useCompanyStore(),
            companyManagerStore: useCompanyManagersStore(),
            api: SharedApiService.make(),
            company: this.initCompany,
            tabs : [
              {name: 'Overview', current: true},
              {name: 'Users & Contacts', current: false},
              {name: 'Company Profile', current: false},
              {name: 'Leads', current: false},
              {name: 'Campaigns', current: false},
              {name: 'Revenue', current: false},
              {name: 'Invoices & Billing', current: false},
              {name: 'Tasks', current: false, redCount: null, greenCount: null, yellowCount: null},
              {name: 'Activity Feed', current: false},
              {name: 'BRS Logs', current: false},
              {name: 'Reviews', current: false},
              {name: 'Role History', current: false},
              {name: 'Commissions', current: false, permission: PERMISSIONS.COMPANY_COMMISSION_VIEW},
              {name: 'Missed Leads', current: false},
            ],
            loadingCounts: true,
          defaultTab: 0,
          selectedTab: 'Overview',
          editingBasicInfo: false,
          states: [],
          editing: {
            name: null,
            website: null,
            sourced_from: null,
            phone: null,
            status: null,
            consolidated_status: null,
            admin_approved: null,
            admin_locked: null,
            sales_status: null,
            industry: null,
            prescreened: null,
            employees: null,
            revenue: null,
            account_manager: null,
            onboarding_manager: null,
            sales_development_representative: null,
            success_manager: null,
            business_development_manager: null,
            industries: [],
            address: {
              name: null,
              address_1: null,
              address_2: null,
              city: null,
              state: null,
              zip_code: null,
            },
              basic_status: null,
            admin_status: undefined,
            qr_top_500_company: null,
          },
          editingSalesStatus: false,
          accountManagerOptions: [
            {id: 0, name: 'None assigned'}
          ],
          salesDevelopmentRepresentativeOptions: [
            {id: 0, name: 'None assigned'}
          ],
          onboardingManagerOptions: [
            {id: 0, name: 'None assigned'}
          ],
          successManagerOptions: [
            {id: 0, name: 'None assigned'}
          ],
          businessDevelopmentManagerOptions: [
            {id: 0, name: 'None assigned'}
          ],
          adminStatusOptions: [],
          typeOptions: [
            {id: 'installer', name: "Installer"},
            {id: 'roofer', name: "Roofer"},
            {id: 'manufacturer', name: "Manufacturer"},
            {id: 'affiliate', name: "Affiliate"},
            {id: 'aggregator', name: "Aggregator"},
            {id: 'pp_agg', name: "Ping Post Aggregator"},
          ],
          salesStatusOptions: [],
          saving: false,
          budgetUsage: null,
          loadingBudgetUsage: false,
          loadingSaleStatus: false,
          selectedDashboard: 'fixr',
          companyMetrics: null,
          showInvoiceModal: false,
          queryParamsHelper: useQueryParams(),
          requestOwnershipModal:false,
          adminStatusChangeReason: ''
        }
    },
    provide() {
        // use function syntax so that we can access `this`
        return {
            company: {
                id: this.initCompany.id,
                name: this.initCompany.name
            }
        }
    },
    computed: {
        filteredTabs() {
            return this.tabs.filter((tab) => !(tab?.permission) || this.permissionStore.hasPermission(tab.permission))
        },
        hasEditRights() {
            //admin, BDM, SM can edit
            return this.permissionStore.hasRole(ROLES.ADMIN)
                || this.permissionStore.hasRole(ROLES.BUSINESS_DEVELOPMENT_MANAGER)
                || this.permissionStore.hasRole(ROLES.SALES_MANAGER)
                || this.permissionStore.hasRole(ROLES.ONBOARDING_MANAGER)
                // AM, SM, SDR, BDM or OM for this company can edit
                || this.userStore.user?.id === this.company.account_manager?.user_id
                || this.userStore.user?.id === this.company.success_manager?.user_id
                || this.userStore.user?.id === this.company.sales_development_representative?.user_id
                || this.userStore.user?.id === this.company.business_development_manager?.user_id
                || this.userStore.user?.id === this.company.onboarding_manager?.user_id
        },
        canViewCompanyEmails() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_MAILBOX_LIST_EMAILS)
        },
        canEditCompanyBasicStatus() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_COMPANY_BASIC_STATUS_EDIT)
        },
        canEditCompanyAdminStatus() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_COMPANY_ADMIN_STATUS_EDIT)
        },
        canEditAdminApprovedLocked() {
            /**
             * BDMs, AMs and Sales Managers may change Admin statuses
             */
            if (this.userStore.user?.id === this.company.business_development_manager?.user_id) {
                return true;
            }

            if (this.userStore.user?.id === this.company.account_manager?.user_id) {
                return true;
            }

            if (this.userStore.user?.id === this.company.onboarding_manager?.user_id) {
                return true;
            }

            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_COMPANY_ADMIN_STATUS_EDIT);
        },
        companyDashboardType() {
            const industrySlugs = this.company.industries.map(industry => industry.slug);
            return industrySlugs.length === 1 && (industrySlugs[0] === 'solar' || industrySlugs[0] === 'roofing')
                ? industrySlugs[0]
                : 'multiIndustry';
        },
        canAccessCompanyConfigurations(){
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_COMPANY_CONFIGURE)
        },
        canResetCrmRejections() {
            return this.permissionStore.hasRole(ROLES.ADMIN)
        },
        canAccessCompanyContracts() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_CONTRACT_MANAGEMENT_VIEW)
        },
        mostRecentAdSpendEstSpent() {
            const breakdown = this.companyMetrics?.breakdown;

            if (!breakdown || breakdown.length === 0) {
                return null;
            }

            return breakdown[breakdown.length - 1].spent;
        },
        mostRecentAdSpendEstPeriod() {
            const breakdown = this.companyMetrics?.breakdown;

            if (!breakdown || breakdown.length === 0) {
                return null;
            }

            return breakdown[breakdown.length - 1].period;
        },
        changingAdminStatus() {
            return this.editing.admin_status && this.company.admin_status !== this.editing.admin_status;
        }
    },
    mounted() {
        this.accountManagerOptions.push(...this.accountManagers);
        this.successManagerOptions.push(...this.successManagers);
        this.businessDevelopmentManagerOptions.push(...this.businessDevelopmentManagers);
        this.onboardingManagerOptions.push(...this.onboardingManagers);
        this.salesDevelopmentRepresentativeOptions.push(...this.salesDevelopmentRepresentatives)
        this.adminStatusOptions = this.adminStatuses;
        this.getSaleStatusTypes();
        this.getCompanyMetrics();
        this.getBudgetUsage();
        this.getTabBadgeCounts();
        this.parseUrlParameters();
        this.initializeStores();
    },
    created() {
      this.getStates();
        if(this.tab) {
            this.selectTab(this.tab)
            this.tabs.find(item => item.current === true).current = false;
            this.tabs.find(item => item.name === this.tab).current = true;
            this.defaultTab = this.tabs.findIndex(item => {
                return item.current === true;
            });
        }

        const {tab = DEFAULT_SELECTED_TAB} = this.queryParamsHelper.getCurrentParams();

        this.selectTab(tab)
    },
    methods: {
        initializeStores() {
            this.companyStore.initialize(JSON.parse(JSON.stringify(this.initCompany)));
            this.companyManagerStore.initialize(
                JSON.parse(JSON.stringify(this.accountManagers)),
                JSON.parse(JSON.stringify(this.businessDevelopmentManagers)),
                JSON.parse(JSON.stringify(this.onboardingManagers)),
                JSON.parse(JSON.stringify(this.successManagers))
            )
        },
        toggleRequestOwnershipModal() {
            this.requestOwnershipModal = ! this.requestOwnershipModal
        },
        getTabBadgeCounts() {
            this.api.getMinimalTaskCounts(this.company.id).then(resp => {
                const counts = resp.data.data.counts;

                this.tabs.find(item => item.name === "Tasks").redCount = counts.overdue;
                this.tabs.find(item => item.name === "Tasks").greenCount = counts.due_today;
                this.loadingCounts = false;
            });
        },
        getSaleStatusTypes() {
            this.loadingSaleStatus = true;
            this.api.getSaleStatusTypes().then(resp => {
                const statuses = resp.data.data.statuses;
                this.salesStatusOptions = Object.keys(statuses).map((status) => ({id: statuses[status], name: status}));
                this.assignEditData();
            }).catch(e => console.error("Get Sales Status Options Error: ", e))
                .finally(() => {
                    this.loadingSaleStatus = false;
                });
        },
        getCompanyMetrics() {
            this.api.getCompanyMetrics(this.company.id).then(resp => {
                if (resp.data.data.status) {
                    this.companyMetrics = resp.data.data
                }
            });
        },
        getBudgetUsage() {
            this.loadingBudgetUsage = true;
            this.api.getBudgetUsage(this.company.id).then(resp => {
                this.budgetUsage = resp.data.data.data
            }).finally(() => this.loadingBudgetUsage = false);
        },
        toggleEditStatus() {
            this.editingSalesStatus = !this.editingSalesStatus
        },
        selectTab(selected) {
            this.queryParamsHelper.setQueryParamsOnCurrentUrl({tab: decodeURI(selected)})
            this.selectedTab = selected;
        },
        dialCompanyPhone() {
            // Ensure that it does not call empty numbers
            if (!this.company.phone) return;

            this.dispatchGlobalEvent('call', {phone: this.company.phone, name: this.company.name, id: this.company.phone_location_id, relType: CommunicationRelationTypes.COMPANY_LOCATIONS, relId: this.company.phone_location_id})
        },
        sendToTab(value) {
            this.selectTab(value);
        },
        parseUrlParameters() {
            const params = new URLSearchParams(window.location.search);

            if (params.has('campaign')) {
                this.sendToTab('Campaigns');
                this.campaignStore.editingCampaign = params.get('campaign');
            }
        },
        getPayload() {
            return {
              companyname: this.editing.name,
              website: this.editing.website,
              sourced_from: this.editing.sourced_from,
              type: this.editing.industry,
              prescreened: this.editing.prescreened === 'yes',
              employee_count: this.editing.employees,
              revenue_in_thousands: (`${this.editing.revenue}`.replace(/\D/g, '')) / 1000,
              account_manager_id: this.editing.account_manager,
              onboarding_manager_id:  this.editing.onboarding_manager,
              success_manager_id: this.editing.success_manager,
              relationship_manager_id: this.editing.business_development_manager,
              sales_development_representative_id: this.editing.sales_development_representative,
              phone: this.editing.phone,
              address: {
                location_id: this.company.address.location_id,
                name: this.editing.address.name,
                address_id: this.company.address.address_id,
                address_1: this.editing.address.address_1,
                address_2: this.editing.address.address_2,
                state: this.editing.address.state,
                city: this.editing.address.city,
                zip_code: this.editing.address.zip_code,
              },

              // Front End Payload Requirements.
              name: this.editing.name,
              employees: this.editing.employees,
              revenue: this.editing.revenue,
              account_manager: {
                id: this.editing.account_manager,
                name: this.accountManagers.find(item => item.id === this.editing.account_manager)?.name ?? null
              },
              success_manager: {
                id: this.editing.success_manager,
                name: this.successManagers.find(item => item.id === this.editing.success_manager)?.name ?? null
              },
              business_development_manager: {
                id: this.editing.business_development_manager,
                name: this.businessDevelopmentManagers.find(item => item.id === this.editing.business_development_manager)?.name ?? null
              },
              industries: this.editing.industries,
              admin_status: this.editing.admin_status || undefined,
              qr_top_500_company: this.editing.qr_top_500_company,
              admin_status_change_reason: this.adminStatusChangeReason,
            }
        },
      getStates() {
        this.api.getStates().then(resp => this.states = resp.data.data.map(state => ({...state, id: state.stateAbbr})));
      },
      saveBasicInfo() {
        if (!this.validate()) {
            return;
        }

        const payload = this.getPayload();
        this.saving = true;

        this.api.updateCompanyBasicInfo(this.company.id, payload).then(resp => {
          if (resp.data.data.status) {
            const companyRefresh = resp.data.data.company;
            if (companyRefresh) {
              Object.assign(this.company, companyRefresh);
            }
          } else {
            throw new Error("Status was not successful");
          }
        }).catch((e) => {
          console.error("Saving Company Error:", e); // Log to console for bug reports if needed.
          this.showAlert(AlertTypes.error, e.response.data.message);
        }).finally(() => {
          this.stopEditing();
          this.saving = false;
            });
        },
        startEditing() {
            this.assignEditData();
            this.editingBasicInfo  = true;
        },
        stopEditing() {
            this.clearEditData();
            this.editingBasicInfo = false;
        },
        editBasicInfo() {
            if(this.editingBasicInfo)
                this.stopEditing();
            else
                this.startEditing();
        },
        validate() {
            if (this.changingAdminStatus && !this.adminStatusChangeReason) {
                this.showAlert(AlertTypes.error, 'Please enter a reason for admin status change')
                return false;
            }

            return true;
        },
        recalculateStatus() {
            this.api.recalculateStatus(this.company.id).then(resp => {
                if(resp.data.data.status) {
                    const companyRefresh = resp.data.data.company;
                    if (companyRefresh) {
                        Object.assign(this.company, companyRefresh);
                    }
                }
                else {
                    throw new Error("Status was not successful");
                }
            }).catch((e) => {
                console.error("Saving Company Error:", e); // Log to console for bug reports if needed.
                this.showAlert(AlertTypes.error, e.response.data.message);
            });
        },
        resetCrmRejections() {
            this.api.resetCrmRejections(this.company.id);
        },
        saveSalesStatus() {
            this.saving = true;
            const payload = {
                sales_status: this.editing.sales_status,
                company_id: this.company.id
            };

            this.api.updateSalesStatus(this.company.id, payload).then(resp => {
                if(resp.data.data.status) {
                    const companyRefresh = resp.data.data.company;
                    if (companyRefresh) {
                        Object.assign(this.company, companyRefresh);
                    }
                }
                else {
                    throw new Error("Status was not successful");
                }
            }).catch((e) => {
                console.error("Saving Company Error:", e); // Log to console for bug reports if needed.
                this.showAlert(AlertTypes.error, e.response.data.message);
            }).finally(() => {
                this.saving = false;
                this.editingSalesStatus = false;
            });
        },
        assignEditData() {
          this.editing.name = this.company.name;
          this.editing.website = this.company.website;
          this.editing.sourced_from = this.company.sourced_from;
          this.editing.phone = this.company.phone;
          this.editing.address.name = this.company.address?.name;
          this.editing.address.address_1 = this.company.address?.address_1;
          this.editing.address.address_2 = this.company.address?.address_2;
          this.editing.address.city = this.company.address?.city;
          this.editing.address.state = this.company.address?.state;
          this.editing.address.zip_code = this.company.address?.zip_code;

          this.editing.status = this.company.status;
          this.editing.sales_status = this.salesStatusOptions.find(item => item.name === this.company.sales_status);
          this.editing.consolidated_status = this.company.consolidated_status_object.id;
          this.editing.admin_approved = this.company.admin_approved;
          this.editing.admin_locked = this.company.admin_locked;
          this.editing.industry = this.company.type;
          this.editing.prescreened = this.company.prescreened ? 'yes' : 'no';
          this.editing.employees = this.company.employees;
          this.editing.revenue = this.company.revenue;
          this.editing.account_manager = this.company.account_manager.id;
          this.editing.onboarding_manager = this.company.onboarding_manager.id;
          this.editing.sales_development_representative = this.company.sales_development_representative.id;
          this.editing.success_manager = this.company.success_manager.id;
          this.editing.business_development_manager = this.company.business_development_manager.id;
          this.editing.industries = this.company.industries.map(industry => industry.id)
            this.editing.basic_status = this.company.basic_status ?? '';
          this.editing.qr_top_500_company = this.company.qr_top_500_company;
          this.adminStatusChangeReason = '';
        },
        clearEditData() {
          for (const key in this.editing) {
            if (key !== 'address') {
              this.editing[key] = null;
            }
          }

          for (const key in this.editing.address) {
            this.editing.address[key] = null;
          }

        },
        dashboardSelected() {
            this.api.getShadowToken(this.company.id, true).then(resp => {
                if (resp.data?.data?.token) {
                    window.open(this.company.shadow_urls['fixr'].url + '?token=' + resp.data.data.token, '_blank');
                }
                else
                    this.showAlert('error', "Error generating shadow token.");
            }).catch(e => {
                this.showAlert('error', "Error generating shadow token.");
            });
        },
        handleActivateAlert(alertData) {
            this.showAlert(alertData.type, alertData.text);
        }
    },
    watch: {
        canAccessCompanyConfigurations(newVal){
            if (newVal && !this.tabs.find(t => t.name ==='Configurations')) {
                this.tabs.push(
                    {name: 'Configurations', current: false},
                )
            }
        },
        canAccessCompanyContracts(newVal){
            if (newVal && !this.tabs.find(t => t.name ==='Contracts')) {
                const index = this.tabs.findIndex(item => item.name === 'Invoices & Billing');

                const tab = {name: 'Contracts', current: false}

                if (index === -1) {
                    this.tabs.push(tab);
                } else {
                    this.tabs.splice(index + 1, 0, tab);
                }
            }
        },
    }
}
</script>

<style scoped>

</style>
