<template>
    <div
        class="border-b p-8"
        :class="{'bg-light-background ': !darkMode, 'bg-dark-background': darkMode}"
    >
        <div class="grid grid-cols-2 gap-2">
            <div class="flex flex-col">
                <div class="grid grid-cols-2 gap-3 text-xs items-center">
                    <div v-if="disabledCompany || !invoiceStore.isEditable" class="col-span-2 text-lg font-bold">
                        <entity-hyperlink
                            type="company"
                            :dark-mode="darkMode"
                            :entity-id="invoiceStore.company"
                            :suffix="invoiceStore.companyName"
                            delimiter=" "
                            :prefix="invoiceStore.company"
                        />
                    </div>
                    <div v-else class="col-span-2 grid grid-cols-2 gap-3 items-center">
                        <div class="uppercase text-slate-500">Company</div>
                        <autocomplete
                            :dark-mode="darkMode"
                            v-model="invoiceStore.company"
                            :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}"
                            :options="companies"
                            :placeholder="invoiceStore.companyName ?? 'Company name'"
                            create-user-input-option
                            @update:modelValue="handleCompanySelected"
                            @search="searchCompanies('companyname', $event)">
                        </autocomplete>
                    </div>
                    <div class="uppercase text-slate-500">Status</div>
                    <div class="flex gap-1 items-center">
                        <invoice-status-badge :status="invoiceStore.status?.id" />
                        <div v-if="futureInvoiceData?.status" class="font-medium text-sm">to</div>
                        <invoice-status-badge v-if="futureInvoiceData?.status" :status="futureInvoiceData.status"/>
                    </div>
                    <div v-if="invoiceStore?.billingProfile?.payment_method" class="uppercase text-slate-500">Payment Method</div>
                    <div v-if="invoiceStore?.billingProfile?.payment_method" class="flex gap-1 items-center">
                        <payment-method-badge :type="invoiceStore?.billingProfile?.payment_method" />
                    </div>
                    <div class="uppercase text-slate-500" v-if="invoiceStore.invoiceTransactionStatuses?.length > 0">Traits</div>
                    <div class="flex gap-1 items-center flex-wrap" v-if="invoiceStore.invoiceTransactionStatuses?.length > 0">
                        <DisplayInvoiceTransactionStatuses :dark-mode="darkMode" :transaction-statuses="invoiceStore.invoiceTransactionStatuses"/>
                    </div>
                    <div class="uppercase text-slate-500">Issue Date</div>
                    <Datepicker
                        :disabled="!invoiceStore.isEditable"
                        :model-value="invoiceStore.issueDate"
                        @update:model-value="(date) => {invoiceStore.issueDate = date}"
                        :dark="darkMode"
                        :format="(date) => $filters.dateFromTimestamp(date,'MMMM D, YYYY')"
                        timezone="America/Denver"
                    />
                    <div class="uppercase text-slate-500">Due Date</div>
                    <Datepicker
                        :disabled="!invoiceStore.isEditable"
                        :model-value="invoiceStore.dueDate"
                        @update:model-value="(date) => {invoiceStore.dueDate = date}"
                        :dark="darkMode"
                        :format="(date) => $filters.dateFromTimestamp(date,'MMMM D, YYYY')"
                        timezone="America/Denver"
                    />

                    <div v-if="defaultBillingProfile?.id" class="uppercase text-slate-500">Billing Profile</div>
                    <payment-method-badge
                        v-if="defaultBillingProfile?.id"
                        :type="defaultBillingProfile.payment_method"
                        :reference="defaultBillingProfile.payment_method_number ? `${defaultBillingProfile.payment_method_number} ${defaultBillingProfile.payment_method_expiry_month}/${defaultBillingProfile.payment_method_expiry_year}` : null"
                    />
                </div>
            </div>
            <div class="flex flex-col text-xs">
                <div class="flex flex-col">
<!--                    <div class="flex gap-2">-->
<!--                        <div class="flex flex-col">-->
<!--                            <div class="text-slate-500 mb-4">Process Automatically</div>-->
<!--                            <toggle-switch :disabled="!invoiceStore.isEditable" @click="invoiceStore.processAuto = !invoiceStore.processAuto" :model-value="invoiceStore.processAuto" class="flex justify-end" :dark-mode="darkMode"></toggle-switch>-->
<!--                        </div>-->
<!--                        <div class="flex flex-col">-->
<!--                            <div class=" text-slate-500 mb-4">Prepayment</div>-->
<!--                            <toggle-switch :disabled="!invoiceStore.isEditable" @click="invoiceStore.prepayment = !invoiceStore.prepayment" :model-value="invoiceStore.prepayment" class="flex justify-end" :dark-mode="darkMode"></toggle-switch>-->
<!--                        </div>-->
<!--                    </div>-->
                    <div class="flex justify-around">
                        <div v-if="invoiceStore.invoiceTotals" class="flex flex-col gap-2">
                            <p class="font-semibold text-lg">Invoice Balance</p>
                            <div class="flex gap-1" v-for="(value, key) in invoiceStore.nonEmptyInvoiceTotals">
                                <badge class="w-full" :color="invoiceBalanceColor[key]">
                                    <div class="flex flex-col gap-1">
                                        <p class="text-slate-500 capitalize">{{ toTitleCase(key) }}</p>
                                        <p>{{ $filters.centsToFormattedDollars(value)  }}</p>
                                    </div>
                                </badge>
                            </div>
                        </div>
                        <div v-if="creditStore.balances?.length > 0" class="flex flex-col gap-2">
                            <p class="font-semibold text-lg">Credits</p>
                            <div class="flex gap-1" v-for="creditType in creditStore.balances">
                                <badge class="w-full" color="cyan">
                                    <div class="flex flex-col gap-1">
                                        <p class="text-slate-500 capitalize">{{ toTitleCase(creditType.name) }}</p>
                                        <p>{{ $filters.centsToFormattedDollars(creditType.balance) }}</p>
                                    </div>
                                </badge>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>

import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import {useInvoiceModalStore} from "../../../../stores/invoice/invoice-modal.store.js";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import SharedApiService from "../../Shared/services/api.js";
import Badge from "../../Shared/components/Badge.vue";
import {useCompanyCreditManagementStore} from "../../../../stores/credit/company-credit-management.store.js";
import useInvoiceHelper from "../../../../composables/useInvoiceHelper.js";
import InvoiceStatusBadge from "../../BillingManagement/components/InvoiceStatusBadge.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import DisplayInvoiceTransactionStatuses from "./components/DisplayInvoiceTransactionStatuses.vue";
import PaymentMethodBadge from "../InvoicePaymentsModule/PaymentMethodBadge.vue";
import EntityHyperlink from "../../BillingManagement/components/EntityHyperlink.vue";
import {toTitleCase} from "../../../../modules/helpers.js";
import {DateTime} from "luxon";
const invoiceHelper = useInvoiceHelper()

export default {
    name: "CreateInvoiceModalContentCompanyData",
    components: {
        EntityHyperlink,
        PaymentMethodBadge,
        DisplayInvoiceTransactionStatuses,
        SimpleIcon, InvoiceStatusBadge, Badge, Autocomplete, Dropdown, ToggleSwitch},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: [Number, String],
            default: null,
        },
        companyName: {
            type: String,
            default: null,
        },
        futureInvoiceData: {
            type: Object,
            default: {}
        }
    },
    data() {
        return {
            simpleIcon: useSimpleIcon(),
            invoiceHelper,
            sharedApi: SharedApiService.make(),
            invoiceStore: useInvoiceModalStore(),
            creditStore: useCompanyCreditManagementStore(),
            companies: [],
            disabledCompany: false,
            defaultBillingProfile: {}
        }
    },
    created() {
        if (this.companyId && this.companyName) {
            this.invoiceStore.setCompanyDetails(this.companyId, this.companyName)
            this.creditStore.getBalances(this.invoiceStore.company);
            this.getCompanyDefaultBillingProfile(this.companyId)

            this.disabledCompany = true;
        }
    },
    beforeUnmount() {
        this.creditStore.$reset()
        this.invoiceStore.$reset()
    },
    methods: {
        toTitleCase,
        async searchCompanies(nameType, query) {
            this.sharedApi.searchCompanyNamesAndId(query).then(res => {
                if (res.data.data.status === true) {
                    this.companies = res.data.data.companies;
                }
            })
        },
        async getCompanyDefaultBillingProfile(companyId) {
            if (!this.invoiceStore.invoiceId) {
                const response = await this.sharedApi.getCompanyDefaultBillingProfile(companyId)
                this.defaultBillingProfile = response.data.data.default_billing_profile

                this.invoiceStore.issueDate = DateTime.now().setZone('America/Denver').plus({days: 0}).toJSDate()
                this.invoiceStore.dueDate = DateTime.now().setZone('America/Denver').plus({days: this.defaultBillingProfile.due_in_days}).toJSDate()
            }
        },

        async handleCompanySelected(companyId) {
            if (typeof companyId === 'number') {
                this.loading = true;

                this.invoiceStore.companyChanged();

                await Promise.all([
                    this.creditStore.getBalances(this.invoiceStore.company),
                    this.invoiceStore.getUninvoicedProducts(),
                    this.getCompanyDefaultBillingProfile(companyId),
                ])

                this.loading = false;
            }
        },
    },
    watch: {
        async 'invoiceStore.companyName'() {
            if (this.invoiceStore.companyName !== "") {
                this.companies = [{
                    id: this.invoiceStore.company,
                    name: `${this.invoiceStore.company}: ${this.invoiceStore.companyName}`
                }];
                await this.creditStore.getBalances(this.invoiceStore.company);
                if (this.invoiceStore.isEditable) {
                    await this.invoiceStore.getUninvoicedProducts()
                }
            }
        }
    },
    computed: {
        invoiceBalanceColor(){
            return {
                'paid': 'green',
                'outstanding': 'amber',
            }
        }
    }
}
</script>
