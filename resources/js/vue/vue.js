import {createApp} from 'vue'
import { createPinia } from 'pinia';
import {dateFromTimestamp, filterConfig} from "../modules/helpers"

// Available Budget Report
import HistoricalAvailableBudgetsReport from "./components/Reports/HistoricalAvailableBudgetsReport/HistoricalAvailableBudgetsReport.vue";

// ProfitabilitySimulator
import ProfitabilitySimulator from "./components/Reports/ProfitabilitySimulator/ProfitabilitySimulator.vue";

// Estimated Revenue Per Lead Report
import EstimatedRevenuePerLeadReport from "./components/Reports/EstimatedRevenuePerLeadReport/EstimatedRevenuePerLeadReport.vue";

// Lead Processing
import LeadProcessing from "./components/LeadProcessing/LeadProcessing.vue";

// Leads Search
import ConsumerSearch from "./components/ConsumerSearch/ConsumerSearch.vue";

import TwoFactorAuthenticationSetup from "./components/TwoFactorAuthentication/TwoFactorAuthenticationSetup.vue";
import TwoFactorAuthenticationVerify from "./components/TwoFactorAuthentication/TwoFactorAuthenticationVerify.vue";

// Lead Processing Management
import LeadProcessingManagement from "./components/LeadProcessingManagement/LeadProcessingManagement.vue";

// Communication
import Communications from "./components/Communications/Communications.vue";
import CommunicationPortal from "./components/Communications/CommunicationPortal.vue";

// Invoice pdf renderer
import InvoiceContentRenderer from "./components/Billing/invoice/InvoiceContentRenderer.vue";

// Lead Processing Notifications
import LeadProcessingNotifications from "./components/LeadProcessingNotifications/LeadProcessingNotifications.vue";
import UserManagement from "./components/UserManagement/UserManagement.vue";
import UserSettings from "./components/UserSettings/UserSettings.vue";
import AdminDashboard from "./components/AdminDashboard/AdminDashboard.vue";
import SalesBaitManagement from "./components/SalesBaitManagement/SalesBaitManagement.vue";

import TaskManagement from "./components/TaskManagement/TaskManagement.vue"
import TaskTypeEditor from "./components/TaskTypeEditor/TaskTypeEditor.vue"
import Tasks from "./components/Tasks/Tasks.vue"
import TasksList from "./components/TasksList/TasksList.vue";
import CompanyPage from "./components/Companies/CompanyPage.vue";

import AppLogo from "./components/Shared/components/AppLogo.vue";
import RoofingAppLogo from "./components/Shared/components/RoofingAppLogo.vue";

// Sales Management
import SalesManagement from "./components/SalesManagement/SalesManagement.vue";
import SalesOverview from "./components/SalesOverview/SalesOverview.vue";

//Company Activity
import ActivityItem from "./components/Companies/components/activity/activityItem/ActivityItem.vue";

//Email Templates
import EmailTemplates from "./components/EmailTemplates/EmailTemplates.vue";

import GlobalCompanyWrapper from "./components/Companies/GlobalCompanyWrapper.vue";

//Roles & Permissions
import RolesPermissionsManagement from "./components/RolesPermissionsManagement/RolesPermissionsManagement.vue";

import GlobalCommandSearch from "./components/Shared/components/GlobalCommandSearch.vue";

//Industry Management
import IndustryManagement from "./components/IndustryManagement/IndustryManagement.vue";
import ConfigurableFields from "./components/IndustryManagement/ConfigurableFields/ConfigurableFields.vue";

// Date Picker
import Datepicker from "@vuepic/vue-datepicker";
import '@vuepic/vue-datepicker/dist/main.css';
import ManagesGlobalShortcuts from "./mixins/manages-global-shortcuts";

//Advertising
import AdvertisingManagement from "./components/Advertising/AdvertisingManagement.vue";
import {HtmlScrubber} from "../utilities/HtmlScrubber";
import {SimpleRelativeTime} from "../utilities/SimpleRelativeTime";

//Filter Presets Config
import FilterPresetConfig from "./components/FilterPresetConfig/FilterPresetConfig.vue";

//Target Location By Missed Revenue
import TargetLocationByMissedRevenue from "./components/TargetLocationByMissedRevenue/TargetLocationByMissedRevenue.vue";

// Date, Time & Currency formatting
import currency from "currency.js";
import dayjs from "dayjs";
import relativeTime from 'dayjs/plugin/relativeTime.js';
import utc from 'dayjs/plugin/utc.js';
import Timezone from 'dayjs/plugin/timezone.js';
import advancedFormat from 'dayjs/plugin/advancedFormat.js';
[relativeTime, utc, Timezone, advancedFormat].forEach(extension => dayjs.extend(extension));
import FloorPriceManagement from "./components/FloorPriceManagement/FloorPriceManagement.vue";

//external website authentication
import External from "./components/Shared/Auth/External.vue";
import DispatchesGlobalEventsMixin from "./mixins/dispatches-global-events-mixin";
import CrmBounceLeadReport from "./components/AdminDashboard/LeadIssueReports/CrmBounceLeadReport.vue";
import OverSoldLeadReport from "./components/AdminDashboard/LeadIssueReports/OverSoldLeadReport.vue";
import DeliveredLeadReport from "./components/AdminDashboard/LeadIssueReports/DeliveredLeadReport.vue";
import AllocatedLeadReport from "./components/AdminDashboard/LeadIssueReports/AllocatedLeadReport.vue";
import NoChargedLeadReport from "./components/AdminDashboard/LeadIssueReports/NoChargedLeadReport.vue";
import TableLeadReport from "./components/AdminDashboard/LeadIssueReports/components/TableLeadReport.vue";

//Appointments
import CancelAppointment from "./components/Appointments/Consumer/CancelAppointment.vue";

// Bundles
import BundleManagement from "./components/BundleManagement/BundleManagement.vue";

//Calculator Flows
import FlowManagement from "./components/FlowManagement/FlowManagement.vue";


// CompanyQualityScoreManagement
import CompanyQualityScoreManagement from "./components/CompanyQualityScoreManagement/CompanyQualityScoreManagement.vue";

// RulesetManagement
import RulesetManagement from "./components/RulesetManagement/RulesetManagement.vue";

import piniaPluginPersistedState from "pinia-plugin-persistedstate"
import CommunicationLogsOverview
    from "./components/DashboardModules/CommunicationLogsOverview.vue";

//Add Company
import AddCompany from "./components/Shared/modules/AddCompany.vue";

import OpportunityNotificationsContainer
    from "./components/OpportunityNotifications/OpportunityNotificationsContainer.vue";

import OutreachCadence from "./components/OutreachCadence/OutreachCadence.vue";

import GlobalConfigurationsManagement from "./components/GlobalConfigurationsManagement/GlobalConfigurationsManagement.vue";

import SiloManagement from "./components/SiloManagement/SiloManagement.vue";
import {useRolesPermissions} from "../stores/roles-permissions.store";

import CompanySearch from './components/CompanySearch/CompanySearch.vue'

import FileUpload from "./components/Inputs/FileUpload.vue";

import CompanyLogo from "./components/Shared/modules/CompanyLogo.vue";
import CompanyMediaAssets from "./components/Shared/modules/CompanyMediaAssets.vue";

import UserActivity from "./components/UserActivity/UserActivity.vue";

// Consumer Product
import ConsumerProduct from './components/ConsumerProduct/ConsumerProduct.vue';

import ContractManagement from "./components/ContractManagement/ContractManagement.vue";
import TestLeads from "./components/TestLeads/TestLeads.vue";
import Mailbox from "./components/Mailbox/Mailbox.vue";
import MailboxSyncCard from "./components/Mailbox/MailboxSyncCard.vue";
import GoogleServicesIntegrationCard from "./components/Google/GoogleServicesIntegrationCard.vue";
import ModalReport from "./components/AdminDashboard/LeadIssueReports/components/ModalReport.vue";
import ComposeEmail from "./components/Mailbox/components/ComposeEmail.vue";

import LeadRefunds from './components/LeadRefunds/LeadRefunds.vue'

export const ADMIN_MOUNT_SELECTOR = '#admin-app';
export const GUEST_MOUNT_SELECTOR = '#guest-app';
export const PDF_MOUNT_SELECTOR = '#pdf-app';

import EditUserProfile from "./components/UserProfile/EditUserProfile.vue";
import CompanyUser from "./components/Companies/components/CompanyUser.vue";
import CompanyUsers from "./components/CompanyUsers/CompanyUsers.vue";
import PrivacyManagement from "./components/PrivacyManagement/PrivacyManagement.vue";
import BillingManagement from "./components/BillingManagement/BillingManagement.vue";
import {useUserStore} from "../stores/user-store";
import * as Sentry from "@sentry/browser";
import CompanyCampaignDeliveryLogs from "./components/CompanyCampaignDeliveryLogs/CompanyCampaignDeliveryLogs.vue";
import LeadsReport from "./components/Reports/LeadsReport/LeadsReport.vue";
import FloorPriceSuggestions from "./components/FloorPriceSuggestions/FloorPriceSuggestions.vue";
import CompanyCampaignCustomPricingLogs from "./components/CompanyCampaignCustomPricingLogs/CompanyCampaignCustomPricingLogs.vue";
import CountyCoverageReport from "./components/Reports/CountyCoverageReport/CountyCoverageReport.vue";
import RecyclableLeadSearch from "./components/RecycledLeads/RecyclableLeadSearch.vue";
import Marketing from "./components/MarketingCampaign/Marketing.vue";

import Affiliates from './components/Affiliates/Index.vue';
import TemplateManagement from "./components/TemplateManagement/TemplateManagement.vue";
import ProspectHunter from "./components/Prospects/ProspectHunter.vue";
import BDMDashboard from "./components/BDMDashboard/BDMDashboard.vue";
import ProspectConfigurations from "./components/ProspectConfigurations/ProspectConfigurations.vue";
import Reviews from "./components/Reviews/Reviews.vue";
import RegistrationsLog from "./components/DashboardModules/RegistrationsLog.vue";
import InactiveCampaigns from "./components/DashboardModules/InactiveCampaigns.vue";

/**
 * Handles the registration of components.
 *
 * Currently, we explicitly register our vue components.
 * We could automatically register components in the `./components` directory.
 *
 * @param app
 */
function registerComponents(app) {
    app.component("AdminDashboard", AdminDashboard);
    app.component('LeadProcessing', LeadProcessing);
    app.component('LeadProcessingManagement', LeadProcessingManagement);
    app.component('ConsumerSearch', ConsumerSearch);
    app.component('Communications', Communications)
    app.component('CommunicationPortal', CommunicationPortal)
    app.component('LeadProcessingNotifications', LeadProcessingNotifications);
    app.component("SalesBaitManagement", SalesBaitManagement);
    app.component('UserManagement', UserManagement);
    app.component('UserSettings', UserSettings);
    app.component('FilterPresetConfig', FilterPresetConfig);
    app.component('Datepicker', Datepicker);
    app.component('HistoricalAvailableBudgetsReport', HistoricalAvailableBudgetsReport);
    app.component('ProfitabilitySimulator', ProfitabilitySimulator);
    app.component('EstimatedRevenuePerLeadReport', EstimatedRevenuePerLeadReport);
    app.component('LeadsReport', LeadsReport);
    app.component('CountyCoverageReport', CountyCoverageReport);
    app.component('SalesManagement', SalesManagement);
    app.component('SalesOverview', SalesOverview);
    app.component('TaskManagement', TaskManagement);
    app.component('Tasks', Tasks);
    app.component('TasksList', TasksList);
    app.component('TaskTypeEditor', TaskTypeEditor);
    app.component('EditUserProfile', EditUserProfile);
    app.component('ContractManagement', ContractManagement);
    app.component('CompanyUser', CompanyUser);
    app.component('CompaniesSearch', CompanySearch);
    app.component('EmailTemplates', EmailTemplates);
    app.component('RolesPermissionsManagement', RolesPermissionsManagement);
    app.component('GlobalCommandSearch', GlobalCommandSearch);
    app.component('CompanyPage', CompanyPage);
    app.component('AppLogo', AppLogo);
    app.component('RoofingAppLogo', RoofingAppLogo);
    app.component('IndustryManagement', IndustryManagement);
    app.component('Reviews', Reviews);
    app.component('ConfigurableFields', ConfigurableFields);
    app.component('FloorPriceManagement', FloorPriceManagement);
    app.component('ActivityItem', ActivityItem);
    app.component('ExternalAuth', External);
    app.component('GlobalCompanyWrapper', GlobalCompanyWrapper);
    app.component('CrmBounceLeadReport', CrmBounceLeadReport);
    app.component('OverSoldLeadReport', OverSoldLeadReport);
    app.component('DeliveredLeadReport', DeliveredLeadReport);
    app.component('AllocatedLeadReport', AllocatedLeadReport);
    app.component('NoChargedLeadReport', NoChargedLeadReport);
    app.component('TableLeadReport', TableLeadReport);
    app.component('TargetLocationByMissedRevenue', TargetLocationByMissedRevenue);
    app.component('CommunicationLogsOverview', CommunicationLogsOverview);
    app.component('TwoFactorAuthenticationSetup', TwoFactorAuthenticationSetup);
    app.component('TwoFactorAuthenticationVerify', TwoFactorAuthenticationVerify);
    app.component('BundleManagement', BundleManagement);
    app.component('BillingManagement', BillingManagement);
    app.component('FlowManagement', FlowManagement);
    app.component('AddCompany', AddCompany);
    app.component('CompanyQualityScoreManagement', CompanyQualityScoreManagement);
    app.component('OpportunityNotificationsContainer', OpportunityNotificationsContainer);
    app.component('RulesetManagement', RulesetManagement);
    app.component('OutreachCadence', OutreachCadence);
    app.component('Mailbox', Mailbox);
    app.component('GlobalConfigurationsManagement', GlobalConfigurationsManagement);
    app.component('SiloManagement', SiloManagement);
    app.component('FileUpload', FileUpload);
    app.component('CompanyLogo', CompanyLogo);
    app.component('CompanyMediaAssets', CompanyMediaAssets);
    app.component('ConsumerProduct', ConsumerProduct);
    app.component('UserActivity', UserActivity);
    app.component('TestLeads', TestLeads);
    app.component('ModalReport', ModalReport);
    app.component('CompanyUsers', CompanyUsers);
    app.component('ComposeEmail', ComposeEmail);
    app.component('PrivacyManagement', PrivacyManagement);
    app.component('CompanyCampaignDeliveryLogs', CompanyCampaignDeliveryLogs);
    app.component('LeadRefunds', LeadRefunds);
    app.component('FloorPriceSuggestions', FloorPriceSuggestions);
    app.component('RecyclableLeadSearch', RecyclableLeadSearch);
    app.component('AdvertisingManagement', AdvertisingManagement);
    app.component('CompanyCampaignCustomPricingLogs', CompanyCampaignCustomPricingLogs);
    app.component('Affiliates', Affiliates);
    app.component('Marketing', Marketing);
    app.component('TemplateManagement', TemplateManagement);
    app.component('ProspectHunter', ProspectHunter);
    app.component('MailboxSyncCard', MailboxSyncCard);
    app.component('GoogleServicesIntegrationCard', GoogleServicesIntegrationCard);
    app.component('BDMDashboard', BDMDashboard);
    app.component('ProspectConfigurations', ProspectConfigurations);
    app.component('RegistrationsLog', RegistrationsLog);
    app.component('InactiveCampaigns', InactiveCampaigns);
}

function registerGuestComponents(app) {
    app.component('CancelAppointment', CancelAppointment);
}

function registerPdfComponents(app) {
    app.component('InvoiceContentRenderer', InvoiceContentRenderer);
}

const clickOutside = {
    beforeMount: (el, binding) => {
        el.clickOutsideEvent = event => {
            if (!(el == event.target || el.contains(event.target))) {
                binding.value();
            }
        };
        document.addEventListener("click", el.clickOutsideEvent);
    },
    unmounted: el => {
        document.removeEventListener("click", el.clickOutsideEvent);
    },
};

/**
 * Handles initializing the top level application for our project.
 */
export function initializeApp() {
    const app = createApp({
        data() {
            return {
                darkMode: false,
                navShowing: false,
                globalCommandSearchShowing: false,
                hideMainContent: false,
                activeMenu: null,
                userStore: useUserStore()
            }
        },
        created() {
            this.darkMode = window.localStorage.getItem("admin-dark-mode") === "dark";
            this.registerShortcut("Command+K,Command+/,Ctrl+k,Ctrl+/", this.searchShortcut.bind(this));
            this.listenForGlobalEvent('load-company-page', () => {
                this.hideMainContent = true;
            });
            this.userStore.getLoggedUser();
        },
        mixins: [ManagesGlobalShortcuts, DispatchesGlobalEventsMixin],
        methods: {
            toggleDarkMode() {
                this.darkMode = !this.darkMode;
                window.localStorage.setItem("admin-dark-mode", this.darkMode ? "dark" : "light");

                // Allow scrollbars to change to dark or light based on current theme.
                if(this.darkMode) {
                    document.querySelector('body').classList.add("bg-dark-background");
                }
                else {
                    document.querySelector('body').classList.remove("bg-dark-background");
                }
            },
            toggleGlobalCommandSearch() {
                this.globalCommandSearchShowing ? this.closeGlobalSearch() : this.openGlobalSearch();
            },
            openGlobalSearch() {
                this.globalCommandSearchShowing = true;
            },
            closeGlobalSearch() {
                this.globalCommandSearchShowing = false;
            },
            toggleUserMenu(activeMenu) {
                this.activeMenu = !(this.activeMenu === activeMenu) ? this.activeMenu = activeMenu : this.activeMenu = null;
            },
            toggleNavigationMenu() {
                this.navShowing = ! this.navShowing;
            },
            searchShortcut() {
                console.log("search");

                if(!this.globalCommandSearchShowing)
                    this.openGlobalSearch();
            }
        },
        mounted() {
            // Allow scrollbars to change to dark or light based on current theme.
            if(this.darkMode) {
                document.querySelector('body').classList.add("bg-dark-background");
            }
            else {
                document.querySelector('body').classList.remove("bg-dark-background");
            }
        }
    }).directive("click-outside", clickOutside);

    if(import.meta && import.meta.env && import.meta.env.MIX_APP_ENV === "local")
        app.config.devtools = true;

    registerComponents(app);

    const pinia = createPinia();
    app.use(pinia);

    pinia.use(piniaPluginPersistedState)

    const htmlScrubber = new HtmlScrubber();
    const simpleRelativeTime = new SimpleRelativeTime();

    app.config.globalProperties.$filters = {
        currency(value, options) {
            return currency(value, options ?? {}).format();
        },
        centsToFormattedDollars(value, options = {}) {
            if (!value) {
                return currency(0, options).format()
            }
            return currency(value / 100, options).format();
        },
        /**
         * Global Vue date formatter. Accepts unix epoch/ISO 8601 input and uses dayjs library
         * @param value {string | number} - unix seconds / unix milliseconds / ISO 8601
         * @param dateFormat {string} - format string: use a key name shortcut from filtersConfig{} presets,
         *  otherwise see https://day.js.org/docs/en/display/format for all formatting options
         * @param forceTimezone {?string} - convert to specified IANA-valid timezone before formatting.
         *
         * @returns {string | string}
         */
        dateFromTimestamp,
        /**
         * Display a phone number in regional format
         * Any phone number with invalid characters will be returned intact so the error is visible to the User
         * Default (and currently only) region is North America
         *
         * @param input {string|number}
         * @param region {string} - must match a region in the above filterConfig{} object, fallback to NA
         * @returns {string}
         */
        formatPhoneNumber(input, region = 'na') {
            const regexInvalidCharacters = filterConfig.phoneNumbers[region].invalidInputCharacters ?? filterConfig.phoneNumbers.na.invalidInputCharacters,
                regionFormatter = filterConfig.phoneNumbers[region].formatOutput ?? filterConfig.phoneNumbers.na.formatOutput;
            input = typeof(input) === 'number'
                ? `${input}`
                : typeof(input) === 'string'
                    ? input
                    : '';
            if (!input || regexInvalidCharacters.test(input)) return input; // Return invalid 'numbers' without formatting
            const digits = input.replace(/\D/g, '');
            return regionFormatter(digits);
        },
        scrubHtml(htmlString) {
            return typeof(htmlString) === 'string'
                ? htmlScrubber.clean(htmlString)
                : '';
        },
        /**
         * Handles formatting the price value for the given fraction (number of digits).
         *
         * @param {number|string} price
         * @param {number} fraction - performs fraction for 2 digits if not specified
         * @return {string}
         */
        formatPrice(price, fraction = 2) {
            let val = (price/1)
                .toFixed(fraction)
                .replace(',', '.');

            return val
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },
        /** @param {number|string} timestamp - unix epoch milliseconds, or valid datetime string
         *  @param {boolean} UTC - assume UTC if no timezone provided
         *  @returns {string} - relative time from Now as a natural language string */
        simpleRelativeTimeFromTimestamp(timestamp, UTC = true) {
            return simpleRelativeTime.getRelativeTimeString(timestamp);
        },
        // TODO: replace with existing formatter once merged with newer branch
        absoluteDatetimeFromTimestamp(datetimeString) {
            datetimeString = /(T|\s)\d+:\d+:\d+(\.\d+)?$/.test(datetimeString)
                ? `${datetimeString}Z`
                : datetimeString;
            const date = new Date(datetimeString);
            return date
                ? date.toLocaleString()
                : 'Unknown comment date';
        },
        /**
         * Convert a string with camel, snake, kebab or spaces to Proper Case
         * @param {string} inputString
         * @returns {string}
         */
        toProperCase(inputString) {
            if (typeof(inputString) !== 'string') return '';
            return inputString.trim()
                .replace(/([a-z])([A-Z])/g, '$1_$2')
                .replace(/([A-Z])([A-Z])/, '$1_$2')
                .replace(/-+/g, '_')
                .replace(/\s+/g, '_')
                .split(/_+/)
                .map(word => word ? `${word[0].toUpperCase()}${word.slice(1)}` : '')
                .join(' ');
        },
    }

    useRolesPermissions().initialize().then(resp => {
            // Sentry User Feedback Widget
            // Using vite mode instead of APP_ENV because env variables are not currently available in prod front-end
            if ( useRolesPermissions().hasPermission('sentry-feedback') && ( import.meta.env.MODE === 'production' || import.meta.env.MIX_SENTRY_DSN ) ) {
                Sentry.init({
                    dsn: import.meta.env.MIX_SENTRY_DSN ?? 'https://<EMAIL>/6384487',
                    environment: import.meta.env.MIX_APP_ENV ?? import.meta.env.MODE,

                    // This sets the sample rate to be 10%. You may want this to be 100% while
                    // in development and sample at a lower rate in production
                    replaysSessionSampleRate: 0.1,

                    // If the entire session is not sampled, use the below sample rate to sample
                    // sessions when an error occurs.
                    replaysOnErrorSampleRate: 1.0,

                    integrations: [
                        Sentry.feedbackIntegration({
                            autoInject: false,
                            colorScheme: "dark",
                            isNameRequired: true,
                            isEmailRequired: true,
                            enableScreenshot: false,
                        }).attachTo(document.querySelector("#sentry-report-button"), {
                            formTitle: "Report a Bug!",
                        }),
                        Sentry.replayIntegration({
                            maskAllText: false,
                            blockAllMedia: false,
                        })
                    ],
                });
            }
        }

    );

    app.mount(ADMIN_MOUNT_SELECTOR);
}

export function initializeGuestApp() {
    const app = createApp({
        data() {
            return {

            };
        }
    }).directive("click-outside", clickOutside);

    if(import.meta && import.meta.env && import.meta.env.MIX_APP_ENV === "local")
        app.config.devtools = true;

    registerGuestComponents(app);

    /**
     * Presets for global Date/Currency formatters
     */
    const filterConfig = {
        dateTime: {
            format: {
                us: (dayjsDateTime) => dayjsDateTime.format('MM/DD/YYYY'),
                usWithTime: (dayjsDateTime) => dayjsDateTime.format('MM/DD/YYYY[,] h:mm a'),
                long: (dayjsDateTime) => dayjsDateTime.format('DD MMMM YYYY [at] HH:MM a ([UTC]ZZ)'),
                relative: (dayjsDateTime) => dayjsDateTime.fromNow(),
                weekdayWithDateTimeShort: (dayjsDateTime) => dayjsDateTime.format(`ddd - M/D/YYYY[,] h:mm A z`),
            }
        },
        phoneNumbers: {
            na: { // North America
                invalidInputCharacters: /[^-\d\s]/,
                formatOutput: (digitString) => {
                    let prefix = '';
                    if (digitString.length > 10 && /^1/.test(digitString)) {
                        prefix = '+1 ';
                        digitString = digitString.slice(1);
                    }
                    const parts = digitString.match(/(\d{0,3})(\d{0,3})(\d{0,4})(\d*)?/);
                    parts.shift();
                    return `${prefix}${parts.filter(v=>v).join('-')}`;
                }
            }
        }
    }

    const htmlScrubber = new HtmlScrubber();
    const simpleRelativeTime = new SimpleRelativeTime();

    app.config.globalProperties.$filters = {
        currency(value) {
            return currency(value).format();
        },
        /**
         * Global Vue date formatter. Accepts unix epoch/ISO 8601 input and uses dayjs library
         * @param value {string | number} - unix seconds / unix milliseconds / ISO 8601
         * @param dateFormat {string} - format string: use a key name shortcut from filtersConfig{} presets,
         *  otherwise see https://day.js.org/docs/en/display/format for all formatting options
         * @param forceTimezone {?string} - convert to specified IANA-valid timezone before formatting.
         *
         * @returns {string | string}
         */
        dateFromTimestamp(value, dateFormat = 'us', forceTimezone = null) {
            let newDate = /\D/.test(value) || `${value}`.length === 13 ? // ISO and unix milliseconds
                dayjs(value)
                : dayjs.unix(value); // unix seconds
            if (!newDate || !newDate.$y) return '';
            if (forceTimezone) {
                try { newDate = newDate.tz(forceTimezone); }
                catch { console.warn(`Bad timezone passed to global formatter: "${forceTimezone}". Date was not converted.`) }
            }
            return dateFormat ?
                filterConfig.dateTime.format[dateFormat] ?
                    filterConfig.dateTime.format[dateFormat](newDate) // preset format from above config object
                    : newDate.format(dateFormat) // custom format string passed to dayjs
                : filterConfig.dateTime.format.us(newDate); // default to US 'MM/DD/YYYY' format
        },
        /**
         * Display a phone number in regional format
         * Any phone number with invalid characters will be returned intact so the error is visible to the User
         * Default (and currently only) region is North America
         *
         * @param input {string|number}
         * @param region {string} - must match a region in the above filterConfig{} object, fallback to NA
         * @returns {string}
         */
        formatPhoneNumber(input, region = 'na') {
            const regexInvalidCharacters = filterConfig.phoneNumbers[region].invalidInputCharacters ?? filterConfig.phoneNumbers.na.invalidInputCharacters,
                regionFormatter = filterConfig.phoneNumbers[region].formatOutput ?? filterConfig.phoneNumbers.na.formatOutput;
            input = typeof(input) === 'number'
                ? `${input}`
                : typeof(input) === 'string'
                    ? input
                    : '';
            if (!input || regexInvalidCharacters.test(input)) return input; // Return invalid 'numbers' without formatting
            const digits = input.replace(/\D/g, '');
            return regionFormatter(digits);
        },
        scrubHtml(htmlString) {
            return typeof(htmlString) === 'string'
                ? htmlScrubber.clean(htmlString)
                : '';
        },
        /** @param {number|string} timestamp - unix epoch milliseconds, or valid datetime string
         *  @param {boolean} UTC - assume UTC if no timezone provided
         *  @returns {string} - relative time from Now as a natural language string */
        simpleRelativeTimeFromTimestamp(timestamp, UTC = true) {
            return simpleRelativeTime.getRelativeTimeString(timestamp);
        },
        // TODO: replace with existing formatter once merged with newer branch
        absoluteDatetimeFromTimestamp(datetimeString) {
            datetimeString = /(T|\s)\d+:\d+:\d+(\.\d+)?$/.test(datetimeString)
                ? `${datetimeString}Z`
                : datetimeString;
            const date = new Date(datetimeString);
            return date
                ? date.toLocaleString()
                : 'Unknown comment date';
        },
        /**
         * Convert a string with camel, snake, kebab or spaces to Proper Case
         * @param {string} inputString
         * @returns {string}
         */
        toProperCase(inputString) {
            if (typeof(inputString) !== 'string') return '';
            return inputString.trim()
                .replace(/([a-z])([A-Z])/g, '$1_$2')
                .replace(/([A-Z])([A-Z])/, '$1_$2')
                .replace(/-+/g, '_')
                .replace(/\s+/g, '_')
                .split(/_+/)
                .map(word => word ? `${word[0].toUpperCase()}${word.slice(1)}` : '')
                .join(' ');
        },
    }

    app.mount(GUEST_MOUNT_SELECTOR);
}

export function initializePdfApp() {
    const app = createApp();

    registerPdfComponents(app);

    app.mount(PDF_MOUNT_SELECTOR);
}
