<?php

namespace App\Console;

use App\Console\Commands\SalesIntel\QueueCompanyImports;
use App\Console\Commands\SalesIntel\QueueContactImports;
use App\Console\Commands\SalesIntel\QueueUserImports;
use App\Enums\Advertising\Advertiser as AdvertiserEnum;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\QueueSortable\QueueName;
use App\Jobs\Advertising\UpdateAdvertisingCampaignLocationsJob;
use App\Jobs\Advertising\UpdateGoogleAdsGeoTargetsJob;
use App\Jobs\Advertising\UpdateMetaLocationsJob;
use App\Jobs\Advertising\UpdateTieredAdvertisingCampaignLocationsJob;
use App\Jobs\Advertising\UploadConversionDataJob;
use App\Jobs\Alert\SendUnprocessedInitialLeadsAlertJob;
use App\Jobs\AgedQueue\CalculateAgedQueueRecencyPointsJob;
use App\Jobs\AgedQueue\PopulateAgedQueueJob;
use App\Jobs\AssignCompaniesToDemosJob;
use App\Jobs\Billing\CalculateEligibleProfilesToBillByThresholdJob;
use App\Jobs\Billing\CalculateEligibleProfilesToBillJobByFrequency;
use App\Jobs\Billing\SuspendCompaniesForUnpaidInvoicesJob;
use App\Jobs\CalculateAvailableBudgetByZipCodeJob;
use App\Jobs\CalculateAverageProductRevenueByLocationJob;
use App\Jobs\CalculateCompanyCampaignLowBidFlagJob;
use App\Jobs\CalculateEstimatedRevenuePerLeadByLocationJob;
use App\Jobs\CalculateHistoricalAvailableBudgetsJob;
use App\Jobs\CalculateMeetingsToSyncConferenceData;
use App\Jobs\CalculateRejectionStatisticsJob;
use App\Jobs\CalculateTieredAdvertisingCountiesJob;
use App\Jobs\Calendar\RenewCalendarEventListener;
use App\Jobs\ClearOutdatedAppLogs;
use App\Jobs\ClearOutdatedMarketingLogs;
use App\Jobs\Company\ProcessCompanyManagerAssignmentJob;
use App\Jobs\ConsumerProductLifecycleTracking\PruneConsumerProductLifecycleTrackers;
use App\Jobs\ConsumerRevalidation\RevalidateUnverifiedConsumersJob;
use App\Jobs\DeleteOldProcessorLockedProductsJob;
use App\Jobs\DeleteOldSystemLockedProductsJob;
use App\Jobs\DispatchReviewRequestSMS;
use App\Jobs\DynamicPriorityJob;
use App\Jobs\ExpireTestLeadsJob;
use App\Jobs\GetAvailableNumbers;
use App\Jobs\IdentifyUpdatedCampaigns;
use App\Jobs\Mailbox\RenewUserEmailListenerJob;
use App\Jobs\Maintenance\CleanUpDashboardLoginTokensJob;
use App\Jobs\MarketingCampaign\DispatchDripCampaigns;
use App\Jobs\MarketingCampaign\DispatchMarketingCampaigns;
use App\Jobs\MoveOldPendingReviewLeadsToUnderReviewJob;
use App\Jobs\OpportunityNotifications\DeleteExpiredMissedProductReasonEventsJob;
use App\Jobs\OpportunityNotifications\DeleteExpiredMissedProductsJob;
use App\Jobs\OpportunityNotifications\ProcessCampaignSummaryOpportunityNotificationsQueueJob;
use App\Jobs\PopulateCalculateCompanyQualityScoreQueueJob;
use App\Jobs\PreCacheCampaignStatisticsJob;
use App\Jobs\Prospects\DispatchUpcomingCloserDemoNotifications;
use App\Jobs\Prospects\ProspectingQueuePrioritizationJob;
use App\Jobs\Prospects\ReleaseProspectsBackToQueue;
use App\Jobs\Reports\CalculateDailyReportDataJob;
use App\Jobs\SellOldUnderReviewLeadsAsUnverifiedJob;
use App\Jobs\SmsAutomationForConsumersReviews;
use App\Jobs\UpdateMostRecentLeadProcessingCommunications;
use App\Jobs\UpdateQueueConstraintTimeframesJob;
use App\Jobs\UpdateTopCompaniesByCountiesJob;
use App\Jobs\UpsellAutomationJob;
use App\Jobs\VerifyCompaniesWebsiteJob;
use App\Models\Odin\Company;
use App\Repositories\HistoricalCompanyRejectionPercentageRepository;
use App\Repositories\HistoricalCompanyRevenueRepository;
use App\Repositories\HistoricalCompanySalesStatusRepository;
use App\Repositories\HistoricalCompanyStatusesRepository;
use App\Repositories\Odin\ProductProcessing\ProductProcessingRepository;
use App\Services\Advertising\AdvertisingCampaignService;
use App\Services\Advertising\Campaigns\AdvertisingServiceFactory;
use App\Services\Billing\CompanyPaymentMethodValidationService;
use Carbon\CarbonImmutable;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule): void
    {
        $this->cron($schedule);

        $this->everyMinute($schedule);
        $this->everyFiveMinutes($schedule);
        $this->everyTenMinutes($schedule);
        $this->everyThirtyMinutes($schedule);
        $this->hourly($schedule);
        $this->everyTwoHours($schedule);
        $this->daily($schedule);
        $this->weekly($schedule);
        $this->monthly($schedule);
    }

    /**
     * @param Schedule $schedule
     * @return void
     */
    private function cron(Schedule $schedule): void
    {
    }

    /**
     * @param Schedule $schedule
     * @return void
     */
    private function everyMinute(Schedule $schedule): void
    {
        $schedule->job(new CalculateAvailableBudgetByZipCodeJob(), 'default')->everyMinute();
    }

    /**
     * @param Schedule $schedule
     * @return void
     */
    private function everyFiveMinutes(Schedule $schedule): void
    {
        $schedule->call(function() {
            foreach(AdvertisingServiceFactory::PLATFORM_SERVICE as $platformService) {
                UpdateAdvertisingCampaignLocationsJob::dispatch($platformService['platform']->value);
            }
        })->name("DispatchAdvertisingLocationUpdateJobs")->everyFiveMinutes()->timezone("America/New_York")->unlessBetween('3:00', '8:00');

        $schedule->call(function() {
            foreach(AdvertisingServiceFactory::PLATFORM_SERVICE as $platformService) {
                UpdateTieredAdvertisingCampaignLocationsJob::dispatch($platformService['platform']->value);
            }
        })->name("DispatchTieredAdvertisingLocationUpdateJobs")->everyFiveMinutes()->timezone("America/New_York")->unlessBetween('3:00', '8:00');

        $schedule->command('horizon:snapshot')->everyFiveMinutes()->withoutOverlapping();

        if(filled(config('services.salesintel.api.key'))) {
            $schedule->command(QueueUserImports::class)->everyFiveMinutes();
            // $schedule->command(QueueContactImports::class)->everyFiveMinutes();
        }
    }

    /**
     * @param Schedule $schedule
     * @return void
     */
    private function everyTenMinutes(Schedule $schedule): void
    {
        $schedule->job(new CalculateEstimatedRevenuePerLeadByLocationJob(), config('queue.named_queues.long_running'))->everyTenMinutes();
        $schedule->job(new CalculateTieredAdvertisingCountiesJob(), config('queue.named_queues.long_running'))->everyTenMinutes();
        $schedule->job(new DeleteOldProcessorLockedProductsJob(), 'default')->everyTenMinutes();
        $schedule->job(new DispatchUpcomingCloserDemoNotifications())->everyTenMinutes();

        $schedule->command('auth:clear-resets')->everyTenMinutes();

        if(filled(config('services.salesintel.api.key'))) {
            $schedule->command(QueueCompanyImports::class, [
                '--states' => 'AZ',
                '--naics' => '238220'
            ])->everyTenMinutes();
        }
    }

    /**
     * @param Schedule $schedule
     * @return void
     */
    private function everyThirtyMinutes(Schedule $schedule): void
    {
        $schedule->call(function() {
            app(ProductProcessingRepository::class)->deleteStragglingReservedLeads();
        })->everyThirtyMinutes()->name("DeleteStragglingReservedLeads");

        $schedule->job(new DispatchMarketingCampaigns())->everyThirtyMinutes();
        $schedule->job(new UpsellAutomationJob())->everyThirtyMinutes();
        $schedule->job(new CalculateAgedQueueRecencyPointsJob())->everyThirtyMinutes()->timezone('America/Denver')->unlessBetween('20:30', '07:30');
    }

    /**
     * @param Schedule $schedule
     * @return void
     */
    private function hourly(Schedule $schedule): void
    {
        // This job uses legacy models, performs slowly, and is consistently failing.
        // Re-enable it after optimizing with A2 models.
        // $schedule->job(new DynamicPriorityJob())->hourly();

//        $schedule->job(new SmsAutomationForConsumersReviews(), 'default')->hourlyAt(35);
        $schedule->job(new UpdateQueueConstraintTimeframesJob(), 'default')->hourlyAt(10);
        $schedule->job(new MoveOldPendingReviewLeadsToUnderReviewJob(), 'default')->hourlyAt(20);
        $schedule->job(new SellOldUnderReviewLeadsAsUnverifiedJob(), config('queue.named_queues.long_running'))->hourlyAt(30);
        $schedule->job(new UpdateMostRecentLeadProcessingCommunications(), 'default')->hourlyAt(40);
        $schedule->command('sanctum:prune-expired --hours=1')->hourly();
        $schedule->job(new CalculateEligibleProfilesToBillByThresholdJob())->hourly();
        $schedule->job(new SendUnprocessedInitialLeadsAlertJob())->hourlyAt(15)->timezone('America/Denver')->unlessBetween('20:30', '09:00');;

        if(config('services.ads.run_scheduled_jobs')) {
            $schedule->job(new UpdateGoogleAdsGeoTargetsJob(), 'default')->hourlyAt(40);

            $schedule->call(function() {
                app(AdvertisingCampaignService::class)->sendNotificationEmailForFailedAdvertisingCampaigns();
            })->hourlyAt(50)->name("SendNotificationEmailForFailedAdvertisingCampaigns");

            $schedule->job(new UploadConversionDataJob(AdvertisingPlatform::META, AdvertiserEnum::WADE, false), 'default')->hourlyAt(35)->name("Meta Upload Conversions Wade");
            $schedule->job(new UploadConversionDataJob(AdvertisingPlatform::MICROSOFT, AdvertiserEnum::GABE, false), 'default')->hourlyAt(45)->name("Microsoft Upload Conversions Gabe");
            $schedule->job(new UploadConversionDataJob(AdvertisingPlatform::GOOGLE, AdvertiserEnum::GABE, false), 'default')->hourlyAt(55)->name("Google Upload Conversions Gabe");
        }

        $schedule->job(new ReleaseProspectsBackToQueue())->hourly();
    }

    /**
     * @param Schedule $schedule
     *
     * @return void
     */
    private function everyTwoHours(Schedule $schedule): void
    {
        $schedule->job(new RevalidateUnverifiedConsumersJob())->everyTwoHours();
    }

    /**
     * @param Schedule $schedule
     * @return void
     */
    private function daily(Schedule $schedule): void
    {
        $schedule->job(new ProspectingQueuePrioritizationJob())->daily();
        $schedule->call(fn() => Company::sort(QueueName::EXISTING_COMPANIES))->daily();
        $schedule->job(new ExpireTestLeadsJob())->daily();
        $schedule->job(new GetAvailableNumbers(), 'default')->daily();
        $schedule->job(new IdentifyUpdatedCampaigns())->daily();
        $schedule->job(new DeleteOldSystemLockedProductsJob(), 'default')->dailyAt("03:10")->timezone("America/New_York");
        $schedule->job(new CalculateRejectionStatisticsJob(), config('queue.named_queues.long_running'))->dailyAt("05:00")->timezone("America/New_York");
        $schedule->job(new CalculateHistoricalAvailableBudgetsJob(), 'default')->dailyAt("07:00")->timezone("America/New_York");
        $schedule->job(new CalculateHistoricalAvailableBudgetsJob(), 'default')->dailyAt("23:30")->timezone("America/New_York");
        $schedule->job(new CalculateDailyReportDataJob(), config('queue.named_queues.long_running'))->dailyAt("01:00")->timezone("America/New_York");
        $schedule->job(new UpdateTopCompaniesByCountiesJob(), config('queue.named_queues.long_running'))->dailyAt("01:30")->timezone("America/New_York");
        $schedule->job(new ClearOutdatedMarketingLogs())->daily();
        $schedule->job(new ClearOutdatedAppLogs())->daily();
        $schedule->job(new ProcessCampaignSummaryOpportunityNotificationsQueueJob(null))->dailyAt("04:30")->timezone("America/New_York");
        $schedule->job(new RenewCalendarEventListener())->daily();
        $schedule->job(new DispatchDripCampaigns())->dailyAt("08:00");
        $schedule->job(new CalculateMeetingsToSyncConferenceData())->hourly();
        $schedule->job(new DispatchReviewRequestSMS())->daily();
        //BDM notification emails are sent + 4hrs from this job
        $schedule->job(new ProcessCompanyManagerAssignmentJob())->dailyAt("04:00")->timezone('America/Denver');
        $schedule->job(new SuspendCompaniesForUnpaidInvoicesJob())->dailyAt("20:00")->timezone("America/New_York");

        $schedule->job(new CalculateCompanyCampaignLowBidFlagJob(
            companyCampaign: null,
            industry: null,
            recentCampaigns: false,
            allCampaigns: true
        ), 'default')->dailyAt("05:00")->timezone("America/New_York");

        $schedule->job(new CalculateAverageProductRevenueByLocationJob(7), 'default')->dailyAt("02:00")->timezone("America/New_York");
        $schedule->command("CalculateAvailableLeads")->dailyAt("00:00")->timezone("America/New_York");
        $schedule->command("update:ad-cost-data -q")->dailyAt("07:00")->timezone("America/New_York");
        $schedule->command("app:calculate-company-ratings --all -q")->dailyAt("04:00")->timezone("America/New_York");

        $schedule->call(function() {
            $now = CarbonImmutable::now('UTC');

            /** @var HistoricalCompanyStatusesRepository $hcsr */
            $hcsr = app(HistoricalCompanyStatusesRepository::class);

            $hcsr->recordDayStatuses($now);
            $hcsr->recordMonthStatuses($now);
        })->twiceDailyAt(6, 23)->timezone("America/New_York")->name("RecordHistoricalCompanyStatuses");

        $schedule->call(function() {
            /** @var HistoricalCompanyRejectionPercentageRepository $hcrpr */
            $hcrpr = app(HistoricalCompanyRejectionPercentageRepository::class);

            $hcrpr->recordCurrentRejectionPercentages();
        })->twiceDailyAt(6, 23, 15)->timezone("America/New_York")->name("RecordHistoricalCompanyRejectionPercentages");

        $schedule->call(function() {
            $now = CarbonImmutable::now('UTC');

            /** @var HistoricalCompanyRevenueRepository $hcrr */
            $hcrr = app(HistoricalCompanyRevenueRepository::class);

            $hcrr->recordDayRevenue($now);
            $hcrr->recordMonthRevenue($now);
            $hcrr->recordYearRevenue($now);
        })->twiceDailyAt(6, 23, 30)->timezone("America/New_York")->name("RecordHistoricalCompanyRevenue");

        $schedule->job(new CalculateEligibleProfilesToBillJobByFrequency(), config('queue.named_queues.long_running'))->dailyAt("08:00")->timezone("America/New_York");

        $schedule->call(function() {
            /** @var HistoricalCompanySalesStatusRepository $hcssr */
            $hcssr = app(HistoricalCompanySalesStatusRepository::class);

            $hcssr->recordCurrentCompanySalesStatuses();
        })->twiceDailyAt(6, 23, 45)->timezone("America/New_York")->name("RecordHistoricalCompanySalesStatus");

        $schedule->job(new PreCacheCampaignStatisticsJob(), config('queue.named_queues.long_running'))->dailyAt("04:00")->timezone("America/New_York");
        $schedule->job(new AssignCompaniesToDemosJob(), config('queue.named_queues.default'))->dailyAt("23:30")->timezone("America/New_York");
    }

    /**
     * @param Schedule $schedule
     * @return void
     */
    private function weekly(Schedule $schedule): void
    {
        $schedule->job(new PopulateCalculateCompanyQualityScoreQueueJob())->weekly();

        if(config('services.ads.run_scheduled_jobs')) {
            $schedule->job(new UpdateMetaLocationsJob(), 'default')->weeklyOn(5, '3:00'); //Friday 3 AM

        }

        $schedule->job(new CleanUpDashboardLoginTokensJob(7))->weekly();

        $schedule->job(new DeleteExpiredMissedProductReasonEventsJob(), 'default')->weeklyOn(6,"04:10")->timezone("America/New_York");
        $schedule->job(new DeleteExpiredMissedProductsJob(), 'default')->weeklyOn(6,"04:15")->timezone("America/New_York");
    }

    /**
     * @param Schedule $schedule
     * @return void
     */
    private function monthly(Schedule $schedule): void
    {
        $schedule->job(new VerifyCompaniesWebsiteJob(), config('queue.named_queues.long_running'))->monthly();

        $schedule->job(new RenewUserEmailListenerJob())->daily();

        $schedule->command('app:update-company-metrics --current-month -q')->monthly();

        // Delete failed jobs that are more than a month old
        $schedule->command('queue:prune-failed --hours=720')->monthly();

        $schedule->job(new PruneConsumerProductLifecycleTrackers())->monthly();

        $schedule->call(function () {
            /** @var CompanyPaymentMethodValidationService $service */
            $service = app(CompanyPaymentMethodValidationService::class);
            $service->validate();
        })->monthlyOn(time: '01:00');

        $schedule->job(new PopulateAgedQueueJob())->twiceMonthly(1, 16, '00:00')->timezone('America/Denver');;
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');
        $this->load(__DIR__.'/Commands/dev');

        require base_path('routes/console.php');
    }
}
