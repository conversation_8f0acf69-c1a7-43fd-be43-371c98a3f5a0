<?php

namespace App\Builders\Billing\Report;

use App\Builders\Billing\BillingBuilder;
use App\Helpers\CarbonHelper;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\SuccessManager;
use App\Models\Territory\RelationshipManager;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use \Illuminate\Database\Query\Builder as QueryBuilder;

abstract class BaseInvoiceReportBuilder extends BillingBuilder
{
    /**
     * @param Builder|QueryBuilder $query
     */
    public function __construct(protected Builder|QueryBuilder $query)
    {
        parent::__construct($query);
    }

    /**
     * @param array|int|null $industryId
     * @return self
     */
    function forIndustry(array|int|null $industryId = null): self
    {
        if (filled($industryId)) {
            $this->query->when($industryId, function (Builder $builder) use ($industryId) {
                return $builder->whereHas(InvoiceSnapshot::RELATION_INVOICE . '.' . Invoice::RELATION_INVOICE_ITEMS, function (Builder $builder) use ($industryId) {
                    return $builder->whereHasMorph('billable', [ProductAssignment::class], function (Builder $builder) use ($industryId) {
                        return $builder->whereHas(ProductAssignment::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_INDUSTRY_SERVICE, function (Builder $builder) use ($industryId) {
                            $industryId = gettype($industryId) === 'array' ? $industryId : [$industryId];
                            return $builder->whereIn(IndustryService::FIELD_INDUSTRY_ID, $industryId);
                        });
                    });
                });
            });
        }

        return $this;
    }


    /**
     * @param int|null $companyId
     * @return InvoiceBalanceReportBuilder
     */
    protected function forCompany(?int $companyId = null): self
    {
        if (filled($companyId)) {
            $this->query->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_COMPANY_ID, $companyId);
        }

        return $this;
    }


    /**
     * @param int|null $successManagerUserId
     * @return $this
     */
    function forSuccessManagerUserId(?int $successManagerUserId = null): self
    {
        if (filled($successManagerUserId)) {
            $this->query->whereHas(InvoiceSnapshot::RELATION_SUCCESS_MANAGER, function ($query) use ($successManagerUserId) {
                return $query->where(SuccessManager::TABLE . '.' . SuccessManager::FIELD_USER_ID, $successManagerUserId);
            });
        }

        return $this;
    }

    /**
     * @param int|null $relationshipManagerUserId
     * @return InvoiceBalanceReportBuilder
     */
    function forRelationshipManagerUserId(?int $relationshipManagerUserId = null): self
    {
        if (filled($relationshipManagerUserId)) {
            $this->query->whereHas(InvoiceSnapshot::RELATION_RELATIONSHIP_MANAGER, function ($query) use ($relationshipManagerUserId) {
                return $query->where(RelationshipManager::TABLE . '.' . RelationshipManager::FIELD_USER_ID, $relationshipManagerUserId);
            });
        }

        return $this;
    }

    /**
     * @param int|null $companyId
     * @return $this
     */
    public function forCompanyId(?int $companyId = null): self
    {
        if (filled($companyId)) {
            $this->query->where(
                InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_COMPANY_ID,
                $companyId
            );
        }

        return $this;
    }


    /**
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @return AgedInvoicesBuilder
     */
    function forDateRange(?string $dateFrom = null, ?string $dateTo = null): self
    {
        if (filled($dateFrom)) {
            $this->query->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_DATE, '>=', CarbonHelper::parseWithTimezone($dateFrom)->startOfDayUTC());
        }

        if (filled($dateTo)) {
            $this->query->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_DATE, '<=', CarbonHelper::parseWithTimezone($dateTo)->endOfDayUTC());
        }

        return $this;
    }
}
