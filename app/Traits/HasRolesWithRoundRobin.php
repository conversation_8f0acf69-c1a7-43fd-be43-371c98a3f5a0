<?php

namespace App\Traits;

use App\Models\ModelHasRoles;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;
use Spatie\Permission\Models\Role;

/**
 * Trait to extend <PERSON><PERSON>'s HasRoles functionality with round robin capabilities.
 */
trait HasRolesWithRoundRobin
{
    /**
     * Get the model's role assignments with round robin information.
     */
    public function roleAssignments(): HasMany
    {
        return $this->hasMany(ModelHasRoles::class, 'model_id')
            ->where('model_type', static::class);
    }

    /**
     * Get roles that can participate in round robin.
     */
    public function roundRobinRoles(): Collection
    {
        return $this->roleAssignments()
            ->where('can_round_robin', true)
            ->with('role')
            ->get()
            ->pluck('role');
    }

    /**
     * Get roles that cannot participate in round robin.
     */
    public function nonRoundRobinRoles(): Collection
    {
        return $this->roleAssignments()
            ->where('can_round_robin', false)
            ->with('role')
            ->get()
            ->pluck('role');
    }

    /**
     * Check if a specific role can participate in round robin.
     */
    public function canRoleParticipateInRoundRobin(string|Role $role): bool
    {
        $roleName = $role instanceof Role ? $role->name : $role;
        $roleModel = $role instanceof Role ? $role : Role::findByName($roleName);

        if (!$roleModel) {
            return false;
        }

        $assignment = $this->roleAssignments()
            ->where('role_id', $roleModel->id)
            ->first();

        return $assignment ? $assignment->can_round_robin : true; // Default to true if no assignment found
    }

    /**
     * Enable round robin for a specific role.
     */
    public function enableRoundRobinForRole(string|Role $role): bool
    {
        $roleName = $role instanceof Role ? $role->name : $role;
        $roleModel = $role instanceof Role ? $role : Role::findByName($roleName);

        if (!$roleModel || !$this->hasRole($roleModel)) {
            return false;
        }

        $assignment = $this->roleAssignments()
            ->where('role_id', $roleModel->id)
            ->first();

        if ($assignment) {
            $assignment->can_round_robin = true;
            return $assignment->save();
        }

        return false;
    }

    /**
     * Disable round robin for a specific role.
     */
    public function disableRoundRobinForRole(string|Role $role): bool
    {
        $roleName = $role instanceof Role ? $role->name : $role;
        $roleModel = $role instanceof Role ? $role : Role::findByName($roleName);

        if (!$roleModel || !$this->hasRole($roleModel)) {
            return false;
        }

        $assignment = $this->roleAssignments()
            ->where('role_id', $roleModel->id)
            ->first();

        if ($assignment) {
            $assignment->can_round_robin = false;
            return $assignment->save();
        }

        return false;
    }

    /**
     * Assign a role with round robin capability setting.
     */
    public function assignRoleWithRoundRobin(string|Role $role, bool $canRoundRobin = true): static
    {
        $this->assignRole($role);

        if ($canRoundRobin) {
            $this->enableRoundRobinForRole($role);
        } else {
            $this->disableRoundRobinForRole($role);
        }

        return $this;
    }

    /**
     * Get users with a specific role that can participate in round robin.
     */
    public static function withRoleForRoundRobin(string|Role $role): Collection
    {
        $roleName = $role instanceof Role ? $role->name : $role;
        $roleModel = $role instanceof Role ? $role : Role::findByName($roleName);

        if (!$roleModel) {
            return collect();
        }

        return static::whereHas('roleAssignments', function ($query) use ($roleModel) {
            $query->where('role_id', $roleModel->id)
                  ->where('can_round_robin', true);
        })->get();
    }
}
