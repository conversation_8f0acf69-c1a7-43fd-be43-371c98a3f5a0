<?php

namespace App\Services\Billing\ReviewableAction;

use App\Enums\Billing\BillingProfileFrequenciesEnum;
use App\Enums\Billing\PaymentMethodServices;
use App\Models\Billing\BillingProfile;
use App\Services\Billing\BillingProfile\BillingProfileService;
use Illuminate\Support\Arr;

class SaveBillingProfileApprovableAction extends ReviewableAction
{
    const int DEFAULT_DUE_IN_DAYS = 0;

    public function __construct(protected BillingProfileService $billingProfileService)
    {

    }

    /**
     * @param array $arguments
     * @return void
     */
    public function onApproval(array $arguments): void
    {
        $paymentMethod = Arr::get($arguments, 'payment_method');
        $companyId = Arr::get($arguments, 'company_id');
        $default = Arr::get($arguments, 'default');
        $thresholdInDollars = Arr::get($arguments, 'threshold_in_dollars');
        $frequencyType = Arr::get($arguments, 'frequency_type');
        $contact = Arr::get($arguments, 'contact');
        $createdById = Arr::get($arguments, 'created_by_id');
        $maxAllowedChargeAttempts = Arr::get($arguments, 'max_allowed_charge_attempts');
        $frequencyData = Arr::get($arguments, 'frequency_data');
        $associatedCampaignIds = Arr::get($arguments, 'associated_campaign_ids');
        $createdByType = Arr::get($arguments, 'created_by_type');
        $processAuto = Arr::get($arguments, 'process_auto');
        $billingProfileId = Arr::get($arguments, 'billing_profile_id');
        $updatedById = Arr::get($arguments, 'updated_by_id');
        $paymentMethodId = Arr::get($arguments, 'payment_method_id');
        $dueInDays = Arr::get($arguments, 'due_in_days', self::DEFAULT_DUE_IN_DAYS);
        $invoiceTemplateId = Arr::get($arguments, 'invoice_template_id');

        if (!empty($billingProfileId)) {
            $this->billingProfileService->updateBillingProfile(
                billingProfile          : BillingProfile::query()->findOrFail($billingProfileId),
                frequencyType           : $frequencyType,
                frequencyData           : $frequencyData,
                thresholdInDollars      : $thresholdInDollars,
                maxAllowedChargeAttempts: $maxAllowedChargeAttempts,
                associatedCampaignIds   : $associatedCampaignIds,
                processAuto             : $processAuto,
                default                 : $default,
                updatedById             : $updatedById,
                paymentMethod           : $paymentMethod,
                dueInDays               : $dueInDays ?? self::DEFAULT_DUE_IN_DAYS,
                paymentMethodId         : $paymentMethodId,
                invoiceTemplateId       : $invoiceTemplateId
            );
        } else {
            $this->billingProfileService->createProfileWithDefaultConfiguration(
                paymentMethod           : PaymentMethodServices::tryFrom($paymentMethod),
                companyId               : $companyId,
                default                 : $default,
                processAuto             : $processAuto,
                thresholdInDollars      : $thresholdInDollars,
                frequency               : BillingProfileFrequenciesEnum::tryFrom($frequencyType),
                contact                 : $contact,
                createdById             : $createdById,
                maxAllowedChargeAttempts: $maxAllowedChargeAttempts,
                frequencyData           : $frequencyData,
                associatedCampaignIds   : $associatedCampaignIds,
                createdByType           : $createdByType,
                paymentMethodId         : $paymentMethodId,
                dueInDays               : $dueInDays ?? self::DEFAULT_DUE_IN_DAYS,
                invoiceTemplateId       : $invoiceTemplateId
            );
        }
    }
}
