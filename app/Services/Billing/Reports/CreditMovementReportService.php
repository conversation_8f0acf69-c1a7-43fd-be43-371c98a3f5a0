<?php

namespace App\Services\Billing\Reports;

use App\Builders\Billing\Report\CompanyCreditBuilder;
use App\Builders\Billing\Report\ExpiredCompanyCreditBuilder;
use App\Builders\Billing\Report\InvoiceCreditBuilder;
use App\Helpers\CarbonHelper;
use App\Models\Odin\ProductAssignment;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class CreditMovementReportService
{
    /**
     * TODO - Create DTO for filters?
     * @param array|null $filters
     * @return Builder
     */
    public function getCreditMovementReport(?array $filters = []): Builder
    {
        return InvoiceCreditBuilder::query()
            ->applyFilters($filters)
            ->getQuery()
            ->union(CompanyCreditBuilder::query()
                ->applyFilters($filters)
                ->getQuery()
            )
            ->union(ExpiredCompanyCreditBuilder::query()
                ->applyFilters($filters)
                ->getQuery()
            )
            ->orderByDesc('created_at');
    }

    /**
     * @param array $items
     * @return array
     */
    public function addLastLeadDeliveredAtToReportItems(array $items): array
    {
        $uniqueCompanyIds = collect($items)->pluck('company_id')->unique();

        $companiesLastLeadDeliveredAt = ProductAssignment::query()
            ->select([
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID,
                DB::raw('MAX(' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED_AT . ') as last_delivered_at')
            ])
            ->whereIn(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID, $uniqueCompanyIds)
            ->groupBy(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID)
            ->get()
            ->mapWithKeys(fn($item) => [
                $item->company_id => $item->last_delivered_at
            ]);

        return array_map(function ($item) use ($companiesLastLeadDeliveredAt) {
            $lastDeliveredAt = $companiesLastLeadDeliveredAt->get(Arr::get($item, 'company_id'));

            $item['last_lead_delivered_at'] = $lastDeliveredAt
                ? CarbonHelper::parseWithTimezone($lastDeliveredAt)->toIso8601String()
                : null;
            return $item;
        }, $items);
    }
}




