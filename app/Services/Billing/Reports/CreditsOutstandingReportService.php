<?php

namespace App\Services\Billing\Reports;

use App\Builders\Billing\Report\CreditOutstandingReport;
use App\Helpers\CarbonHelper;
use App\Models\Odin\ProductAssignment;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CreditsOutstandingReportService
{
    /**
     * TODO - Create DTO for filters?
     * @param array|null $filters
     * @return LengthAwarePaginator
     */
    public function getCreditsOutstandingReportService(?array $filters = []): LengthAwarePaginator
    {
        $paginated = CreditOutstandingReport::query()
            ->applyFilters($filters)
            ->sortBy(Arr::get($filters, 'sort_by', []) ?? [])
            ->getQuery()
            ->paginate(
                perPage: Arr::get($filters, 'per_page', 25),
                page   : Arr::get($filters, 'page', 1),
            );

        $itemsWithLastDeliveredAt = $this->addLastLeadDeliveredAtToReportItems(collect($paginated->items()));

        return new LengthAwarePaginator(
            items      : $itemsWithLastDeliveredAt,
            total      : $paginated->total(),
            perPage    : $paginated->perPage(),
            currentPage: $paginated->currentPage(),
        );
    }

    /**
     * @param Collection $items
     * @return Collection
     */
    protected function addLastLeadDeliveredAtToReportItems(Collection $items): Collection
    {
        $uniqueCompanyIds = $items->pluck('company_id')->unique();

        $companiesLastLeadDeliveredAt = ProductAssignment::query()
            ->select([
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID,
                DB::raw('MAX(' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED_AT . ') as last_delivered_at')
            ])
            ->whereIn(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID, $uniqueCompanyIds)
            ->groupBy(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID)
            ->get()
            ->mapWithKeys(fn($item) => [
                $item->company_id => $item->last_delivered_at
            ]);

        return $items->map(function ($item) use ($companiesLastLeadDeliveredAt) {
            $item->last_lead_delivered_at = CarbonHelper::parseWithTimezone($companiesLastLeadDeliveredAt->get($item->company_id))->toIso8601String();
            $item->created_at = CarbonHelper::parseWithTimezone($item->created_at)->toIso8601String();
            return $item;
        });
    }
}




