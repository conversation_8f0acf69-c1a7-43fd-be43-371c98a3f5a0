<?php

namespace App\Services\OutreachCadence;

use App\Enums\Timezone;
use App\Models\Odin\Company;
use App\Repositories\Legacy\ZipCodeSetRepository;
use Carbon\Carbon;
use Ramsey\Uuid\Type\Time;

class TimeZoneHelperService
{

    const FALLBACK_UTC_OFFSET = -5;

    public function __construct(protected ZipCodeSetRepository $zipCodeRepository) {}

    /**
     * @param string|null $zipCodeString
     * @return int
     */
    public function getTimeZoneOffsetForZipcode(?string $zipCodeString): int
    {
        if(!$zipCodeString)
            return self::FALLBACK_UTC_OFFSET;
        $zipCode = $this->zipCodeRepository->getZipCode($zipCodeString);
        [$standardUTCOffset, $observingDST] = $zipCode ? [(int)$zipCode->utc, $zipCode->dst === 'Y'] : [self::FALLBACK_UTC_OFFSET, false];
        return $standardUTCOffset + (($observingDST && $this->isActiveDST()) ? 1 : 0);
    }

    /**
     * @return bool
     */
    private function isActiveDST(): bool
    {
        $currentTimeZone    = date_default_timezone_get();
        $timeZoneToCheckDST = 'America/Los_Angeles';
        date_default_timezone_set($timeZoneToCheckDST);
        $isActiveDST = date("I");
        date_default_timezone_set($currentTimeZone);
        return $isActiveDST;
    }

    /**
     * @param int $targetUtcOffset
     * @return Carbon
     */
    public function getLocalTime(int $targetUtcOffset): Carbon
    {
        return Carbon::now('UTC')->utcOffset($targetUtcOffset*60);
    }

    /**
     * @param Carbon $time
     * @param int|null $startHour
     * @param int|null $startMinute
     * @param int|null $endHour
     * @param int|null $endMinute
     * @return bool
     */
    public function timeIsWithinHours(Carbon $time, ?int $startHour, ?int $startMinute, ?int $endHour, ?int $endMinute): bool
    {
        $afterStart = $time->hour > $startHour || ($time->hour === $startHour && $time->minute >= $startMinute);
        if(!$afterStart)
            return false;
        return $time->hour < $endHour || ($time->hour === $endHour && $time->minute <= $endMinute);
    }

    /**
     * @param Company $company
     * @param $default
     * @return Timezone
     */
    public function getCompanyTimezone(Company $company, $default = Timezone::MOUNTAIN): Timezone
    {
        $offset = $company->locations()->first()?->address?->utc ?? null;
        return $offset
            ? Timezone::tryFrom($offset) ?? $default
            : $default;
    }
}
