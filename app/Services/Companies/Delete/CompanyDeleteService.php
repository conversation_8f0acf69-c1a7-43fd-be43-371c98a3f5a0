<?php

namespace App\Services\Companies\Delete;

use App\Company\DeleteCompany\Deletes\BaseDelete;
use App\Company\DeleteCompany\Deletes\DeleteAction;
use App\Company\DeleteCompany\Deletes\DeleteCompany;
use App\Company\DeleteCompany\Deletes\DeleteCompanyCampaignBidPriceModule;
use App\Company\DeleteCompany\Deletes\DeleteCompanyCampaignData;
use App\Company\DeleteCompany\Deletes\DeleteCompanyCampaignDeliveryLog;
use App\Company\DeleteCompany\Deletes\DeleteCompanyCampaignDeliveryModule;
use App\Company\DeleteCompany\Deletes\DeleteCompanyCampaignDeliveryModuleCRM;
use App\Company\DeleteCompany\Deletes\DeleteContract;
use App\Company\DeleteCompany\Validate\DeleteValidatorContract;
use App\Company\DeleteCompany\Validate\NoAssignments;
use App\Company\DeleteCompany\Validate\NoBillingProfiles;
use App\Company\DeleteCompany\Validate\NoBundleInvoices;
use App\Company\DeleteCompany\Validate\NoCompanyPaymentMethod;
use App\Company\DeleteCompany\Validate\NoCredit;
use App\Company\DeleteCompany\Validate\NoInvoices;
use App\Company\DeleteCompany\Validate\NoReviews;
use App\Company\DeleteCompany\Validate\NotQueued;
use App\Models\ActivityFeed;
use App\Models\AvailableCampaignByLocation;
use App\Models\AvailableCompanyByLocation;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\CompanyInvoice;
use App\Models\Billing\CompanyPaymentMethod;
use App\Models\Billing\Credit;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\BundleInvoice;
use App\Models\Cadence\CompanyCadenceRoutine;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\CompanyContract;
use App\Models\CompanyManagerAssignmentRequest;
use App\Models\CompanyMetric;
use App\Models\CompanyOptInName;
use App\Models\CompanySlug;
use App\Models\CompanyUserRelationship;
use App\Models\ComputedRejectionStatistic;
use App\Models\ConsumerReviews\CompanyRating;
use App\Models\ConsumerReviews\Review;
use App\Models\ContractorProfile\ContractorProfile;
use App\Models\HistoricalCompanyActivation;
use App\Models\HistoricalCompanyRejectionPercentage;
use App\Models\HistoricalCompanyRevenue;
use App\Models\HistoricalCompanySalesStatus;
use App\Models\LeadRefund;
use App\Models\License;
use App\Models\MissedProducts\MissedProductReasonEvent;
use App\Models\MissedProducts\OpportunityNotification;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyChangeLog;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\CompanyData;
use App\Models\Odin\CompanyExpertReview;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyIndustryType;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyMediaAsset;
use App\Models\Odin\CompanyReview;
use App\Models\Odin\CompanyService;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\DashboardLoginToken;
use App\Models\Odin\GenericProfitabilityAssumptionConfiguration;
use App\Models\Odin\HistoricalCompanyStatuses;
use App\Models\Odin\OptInCompany;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\SingleProductSale;
use App\Models\PaymentCollection;
use App\Models\Prospects\CloserDemo;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\SalesBaitLead;
use App\Models\SalesBaitRestrictedCompany;
use App\Models\SalesIntel\FailedUserImportRecord;
use App\Models\SalesIntel\UserImportRecord;
use App\Models\Territory\CustomerManagerCompany;
use App\Models\TestProduct;
use App\Models\TopCompanyByCounty;
use Illuminate\Support\Facades\Cache;

class CompanyDeleteService
{
    const string QUEUED_FOR_DELETION_CACHE_KEY = 'deletion_queued:';
    protected array $validators = [
        NoAssignments::class,
        NoInvoices::class,
        NoCompanyPaymentMethod::class,
        NoBundleInvoices::class,
        NoBillingProfiles::class,
        NoCredit::class,
        NoReviews::class,
        NotQueued::class,
    ];

    protected array $deletes = [
        AvailableCampaignByLocation::class,
        AvailableCompanyByLocation::class,
        BillingProfile::class,
        BundleInvoice::class,
        CloserDemo::class,
        CompanyCadenceRoutine::class,
        CompanyCampaignLocationModuleLocation::class,
        CompanyChangeLog::class,
        CompanyConfiguration::class,
        CompanyIndustryType::class,
        CompanyInvoice::class,
        CompanyManagerAssignmentRequest::class,
        CompanyOptInName::class,
        CompanyPaymentMethod::class,
        CompanyRating::class,
        CompanyService::class,
        CompanySlug::class,
        ComputedRejectionStatistic::class,
        ContractorProfile::class,
        Credit::class,
        CustomerManagerCompany::class,
        DashboardLoginToken::class,
        HistoricalCompanyActivation::class,
        HistoricalCompanyRejectionPercentage::class,
        HistoricalCompanyRevenue::class,
        HistoricalCompanySalesStatus::class,
        HistoricalCompanyStatuses::class,
        InvoiceSnapshot::class,
        Invoice::class,
        LeadRefund::class,
        MissedProductReasonEvent::class,
        NewBuyerProspect::class,
        OpportunityNotification::class,
        OptInCompany::class,
        PaymentCollection::class,
        Review::class,
        SalesBaitLead::class,
        SalesBaitRestrictedCompany::class,
        FailedUserImportRecord::class,
        UserImportRecord::class,
        SingleProductSale::class,
        TestProduct::class,
        TopCompanyByCounty::class,
        ProductCampaign::class,
        CompanyUser::class,
        CompanyContract::class,
        ActivityFeed::class,
        CompanyData::class,
        CompanyMediaAsset::class,
        CompanyReview::class,
        CompanyExpertReview::class,
        GenericProfitabilityAssumptionConfiguration::class,
        ProductAssignment::class,
        License::class,
        CompanyMetric::class,
        CompanyLocation::class,
        CompanyIndustry::class,
        CompanyUserRelationship::class,
        DeleteAction::class,
        DeleteCompanyCampaignData::class,
        DeleteCompanyCampaignBidPriceModule::class,
        DeleteCompanyCampaignDeliveryLog::class,
        DeleteCompanyCampaignDeliveryModuleCRM::class,
        DeleteCompanyCampaignDeliveryModule::class,
        CompanyCampaign::class,
        CompanyCRMTemplate::class,
        DeleteCompany::class,
    ];

    protected bool $deletable = false;
    protected array $validationResults = [];

    public function __construct(
        protected Company $company,
    )
    {
    }

    public function validate(): static
    {
        foreach ($this->validators as $validator) {
            /** @var DeleteValidatorContract $validator */
            $validator = app($validator);

            $this->validationResults[] = [
                'title' => $validator->title(),
                'data' => $validator->validate(companyId: $this->company->id),
            ];
        }

        $this->deletable = !collect($this->validationResults)->pluck('data')->some(false);

        return $this;
    }

    public function previewDelete(): array
    {
        $impact = [];

        foreach ($this->deletes as $delete) {

            if (is_subclass_of($delete, DeleteContract::class)) {
                /** @var DeleteContract $delete */
                $delete = app($delete);
            } else {
                $delete = new BaseDelete($delete);
            }

            $impact[] = [
                'title' => $delete->title(),
                'data' => $delete->preview(companyId: $this->company->id),
            ];
        }

        return collect($impact)->sortByDesc('data')->values()->toArray();
    }

    public function isDeletable(): bool
    {
        return $this->deletable;
    }

    /**
     * @return array
     */
    public function getValidationResults(): array
    {
        return $this->validationResults;
    }

    public function delete(): bool
    {
        if (!$this->deletable) {
            $this->validate();
        }

        if ($this->deletable && $this->markedForDeletion()) {
            foreach ($this->deletes as $delete) {
                if (is_subclass_of($delete, DeleteContract::class)) {
                    /** @var DeleteContract $delete */
                    $delete = app($delete);
                } else {
                    $delete = new BaseDelete($delete);
                }

                $delete->delete(companyId: $this->company->id);
            }

            $this->removeMarkedForDeletion();

            return true;
        } else {
            logger()->info("Company deletion cancelled, did not meet prerequisite criteria.");
            logger()->info("Deletable: " . $this->deletable);
            logger()->info("Marked For Deletion: " . $this->markedForDeletion());

            return false;
        }
    }

    public function markForDeletion(): void
    {
        Cache::put(self::QUEUED_FOR_DELETION_CACHE_KEY . $this->company->id, true);
    }

    public function markedForDeletion(): bool
    {
        return Cache::has(self::QUEUED_FOR_DELETION_CACHE_KEY . $this->company->id);
    }

    public function removeMarkedForDeletion(): bool
    {
        return Cache::forget(self::QUEUED_FOR_DELETION_CACHE_KEY . $this->company->id);
    }

    public function cancelUrl(): string
    {
        return config('app.url') . "/companies/" . $this->company->id . "/delete/cancel";
    }
}
