<?php

namespace App\Services\Companies;

use App\Enums\CompanyUserRelationships\CompanyManagerAssignmentRule;
use App\Enums\RoleType;
use App\Jobs\Company\SendAmPreAssignmentNotificationJob;
use App\Jobs\Emails\SendGenericEmailJob;
use App\Models\CompanyUserRelationship;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Models\Role;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Throwable;

class CompanyManagerAssignmentService
{
    const array USER_EMAILS = ['<EMAIL>', '<EMAIL>'];

    /**
     * @return void
     * @throws Throwable
     */
    public function handleBdm(): void
    {
        CompanyUserRelationship::query()
            ->with([CompanyUserRelationship::RELATION_USER, CompanyUserRelationship::RELATION_COMPANY])
            ->with(CompanyUserRelationship::RELATION_COMPANY . '.' . Company::RELATION_PRODUCT_ASSIGNMENTS, function (HasMany $query) {
                $query->select([
                    ProductAssignment::FIELD_ID,
                    ProductAssignment::FIELD_COMPANY_ID,
                    ProductAssignment::FIELD_DELIVERED_AT,
                    ProductAssignment::CREATED_AT,
                    ProductAssignment::UPDATED_AT
                ])
                    ->where(ProductAssignment::FIELD_CHARGEABLE, true)
                    ->where(ProductAssignment::FIELD_DELIVERED, true)
                    ->oldest(ProductAssignment::FIELD_DELIVERED_AT)
                    ->where(ProductAssignment::CREATED_AT, '>=', now()->subMonths(15));
            })
            ->whereNull(CompanyUserRelationship::FIELD_DELETED_AT)
            ->whereHas(
                CompanyUserRelationship::RELATION_ROLE,
                fn(Builder $builder) => $builder->where(Role::FIELD_NAME, RoleType::BUSINESS_DEVELOPMENT_MANAGER->value)
            )
            ->chunk(500, fn(Collection $relationships) => $relationships->each(function (CompanyUserRelationship $relationship) {
                /** @var ProductAssignment|null $leadAfterAssignment */
                $leadAfterAssignment = $relationship->company->productAssignments
                    ->filter(fn(ProductAssignment $productAssignment) => $productAssignment->delivered_at->gte($relationship->created_at))
                    ->sortBy(fn(ProductAssignment $productAssignment) => $productAssignment->delivered_at?->timestamp)
                    ->first();

                $this->processBdmAssignment($relationship, $leadAfterAssignment);
                $this->processBdmNotifications($relationship, $leadAfterAssignment);
            }));
    }

    /**
     * @param Company $company
     * @param Carbon $firstLeadDate
     *
     * @return void
     * @throws Throwable
     */
    public function processOmAssignments(Company $company, Carbon $firstLeadDate): void
    {
        //Un-assign OM
        if ($firstLeadDate->diffInDays(now()) >= CompanyManagerAssignmentRule::OM_UN_ASSIGNMENT_DAYS->days()) {
            $this->unAssignOm(
                $company,
                "Un-assigned " . CompanyManagerAssignmentRule::OM_UN_ASSIGNMENT_DAYS->days() . " days after the company's first lead purchase."
            );

            return;
        }

        //Assign OM
        if ($this->canAssignOm($company)) {
            $user = $company->determineRoundRobin(RoleType::ONBOARDING_MANAGER->value);

            if ($user) {
                $company->assign($user)->as(role: RoleType::ONBOARDING_MANAGER->value, reason: "Assigned following the company's first lead purchase.");
                $company->updateRoundRobinCache($user, RoleType::ONBOARDING_MANAGER->value);
                $this->notifyAssigned(
                    user: $user,
                    company: $company,
                    role: RoleType::ONBOARDING_MANAGER->value,
                    reason: "Assigned following the company's first lead purchase."
                );
            }
        }
    }

    /**
     * @param Company $company
     * @param Carbon $firstLeadDate
     *
     * @return void
     * @throws Throwable
     */
    public function processAmAssignment(Company $company, Carbon $firstLeadDate): void
    {
        //Assign AM
        if ($firstLeadDate->diffInDays(now()) >= CompanyManagerAssignmentRule::AM_ASSIGNMENT_DAYS->days()) {
            if ($this->canAssignAm($company)) {
                if ($company->preassignedAccountManager && $company->preassignedAccountManager->hasRole(RoleType::ACCOUNT_MANAGER->value)) {
                    $company->assign($company->preassignedAccountManager)
                        ->as(
                            role: RoleType::ACCOUNT_MANAGER->value,
                            reason: 'Assigned ' . CompanyManagerAssignmentRule::AM_ASSIGNMENT_DAYS->days() . ' days after the first lead.'
                        );

                    $this->notifyAssigned(
                        user: $company->preassignedAccountManager,
                        company: $company,
                        role: RoleType::ACCOUNT_MANAGER->value,
                        reason: "Assigned " . CompanyManagerAssignmentRule::AM_ASSIGNMENT_DAYS->days() . " days after company's first lead purchase"
                    );

                    Company::query()->find($company->id)->update([Company::FIELD_PREASSIGNED_ACCOUNT_MANAGER_USER_ID => null]);

                    return;
                }

                $user = $company->determineRoundRobin(RoleType::ACCOUNT_MANAGER->value);

                if ($user) {
                    $company->assign($user)
                        ->as(
                            role: RoleType::ACCOUNT_MANAGER->value,
                            reason: 'Assigned ' . CompanyManagerAssignmentRule::AM_ASSIGNMENT_DAYS->days() . ' days after the first lead.'
                        );
                    $company->updateRoundRobinCache($user, RoleType::ACCOUNT_MANAGER->value);

                    $this->notifyAssigned(
                        user: $user,
                        company: $company,
                        role: RoleType::ACCOUNT_MANAGER->value,
                        reason: "Assigned " . CompanyManagerAssignmentRule::AM_ASSIGNMENT_DAYS->days() . " days after company's first lead purchase"
                    );
                }
            }

            return;
        }

        //Pre-assign AM
        if ($firstLeadDate->diffInDays(now()) >= CompanyManagerAssignmentRule::AM_SELECTION_DAYS->days()) {
            if ($this->canPreAssignAm($company)) {
                $accountManager = $company->determineRoundRobin(RoleType::ACCOUNT_MANAGER->value);

                if ($accountManager) {
                    Company::query()->find($company->id)->update([Company::FIELD_PREASSIGNED_ACCOUNT_MANAGER_USER_ID => $accountManager->id]);
                    $company->updateRoundRobinCache($accountManager, RoleType::ACCOUNT_MANAGER->value);
                    $this->notifyPreAssignment($company);
                }
            }
        }
    }

    /**
     * @param Company $company
     * @param int $days
     *
     * @return ProductAssignment|null
     */
    public function getFirstLeadForCompany(Company $company, int $days = 120): ?ProductAssignment
    {
        return $company->productAssignments()
            ->where(ProductAssignment::FIELD_DELIVERED_AT, '>=', now()->subDays($days)->startOfDay())
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->oldest()
            ->first();
    }

    /**
     * @param Company $company
     * @param string $reason
     *
     * @return void
     * @throws Throwable
     */
    protected function unAssignOm(Company $company, string $reason): void
    {
        if ($company->hasAssignment(RoleType::ONBOARDING_MANAGER->value)) {
            $user = $company->unassign(role: RoleType::ONBOARDING_MANAGER->value, reason: $reason);

            if ($user) {
                $this->notifyUnassigned(
                    user: $user,
                    company: $company,
                    role: RoleType::ONBOARDING_MANAGER->value,
                    reason: $reason
                );
            }
        }
    }

    /**
     * @param CompanyUserRelationship $companyUserRelationship
     * @param ProductAssignment|null $productAssignment
     *
     * @return void
     * @throws Throwable
     */
    protected function processBdmAssignment(CompanyUserRelationship $companyUserRelationship, ?ProductAssignment $productAssignment): void
    {
        if ($productAssignment?->delivered_at && $productAssignment->delivered_at->diffInDays(now()) > CompanyManagerAssignmentRule::BDM_UN_ASSIGNMENT_DAYS->days()) {
            $companyUserRelationship->company->unassign(
                role: RoleType::BUSINESS_DEVELOPMENT_MANAGER->value,
                reason: 'Default un-assignment after ' . CompanyManagerAssignmentRule::BDM_UN_ASSIGNMENT_DAYS->days() . ' days'
            );

            if ($companyUserRelationship->user && $companyUserRelationship->company) {
                $this->notifyUnassigned(
                    user: $companyUserRelationship->user,
                    company: $companyUserRelationship->company,
                    role: RoleType::BUSINESS_DEVELOPMENT_MANAGER->value,
                    reason: "You've completed the " . CompanyManagerAssignmentRule::BDM_UN_ASSIGNMENT_DAYS->days() . " days assignment period."
                );
            }

            return;
        }

        if (!$productAssignment && $companyUserRelationship->created_at->diffInDays(now()) >= CompanyManagerAssignmentRule::BDM_UN_ASSIGNMENT_DAYS_NOT_PURCHASED->days()) {
            $companyUserRelationship->company->unassign(
                role: RoleType::BUSINESS_DEVELOPMENT_MANAGER->value,
                reason: 'No lead purchase in ' . CompanyManagerAssignmentRule::BDM_UN_ASSIGNMENT_DAYS_NOT_PURCHASED->days() . ' days'
            );

            if ($companyUserRelationship->user && $companyUserRelationship->company) {
                $this->notifyUnassigned(
                    user: $companyUserRelationship->user,
                    company: $companyUserRelationship->company,
                    role: RoleType::BUSINESS_DEVELOPMENT_MANAGER->value,
                    reason: "No leads were purchased within " . CompanyManagerAssignmentRule::BDM_UN_ASSIGNMENT_DAYS_NOT_PURCHASED->days() . " days of your assignment."
                );
            }

            if ($companyUserRelationship->company) {
                $this->unAssignOm(
                    $companyUserRelationship->company,
                    "No leads have been purchased in the past " . CompanyManagerAssignmentRule::BDM_UN_ASSIGNMENT_DAYS_NOT_PURCHASED->days() . " days."
                );
            }
        }
    }

    /**
     * @param Company $company
     *
     * @return bool
     */
    protected function canAssignOm(Company $company): bool
    {
        return !$company->hasAssignment(RoleType::ONBOARDING_MANAGER->value) && !$company->hasAssignment(RoleType::ACCOUNT_MANAGER->value);
    }

    /**
     * @param Company $company
     *
     * @return bool
     */
    protected function canAssignAm(Company $company): bool
    {
        return !$company->hasAssignment(RoleType::ACCOUNT_MANAGER->value);
    }

    /**
     * @param Company $company
     *
     * @return bool
     */
    protected function canPreAssignAm(Company $company): bool
    {
        return !$company->hasAssignment(RoleType::ACCOUNT_MANAGER->value) && !$company->preassignedAccountManager;
    }

    /**
     * @param CompanyUserRelationship $companyUserRelationship
     * @param ProductAssignment|null $productAssignment
     *
     * @return void
     */
    protected function processBdmNotifications(CompanyUserRelationship $companyUserRelationship, ?ProductAssignment $productAssignment): void
    {
        if ($productAssignment) {
            return;
        }

        collect([
            CompanyManagerAssignmentRule::BDM_NOTIFICATION_FIRST_NOT_PURCHASED_DAYS,
            CompanyManagerAssignmentRule::BDM_NOTIFICATION_SECOND_NOT_PURCHASED_DAYS,
            CompanyManagerAssignmentRule::BDM_NOTIFICATION_THIRD_NOT_PURCHASED_DAYS
        ])->each(function (CompanyManagerAssignmentRule $rule) use ($companyUserRelationship) {
           if ($companyUserRelationship->created_at->diffInDays(now()) === $rule->days()) {
               $this->notifyLeadNotPurchased($companyUserRelationship->user, $companyUserRelationship->company, $rule->days());
           }
        });
    }

    /**
     * @param User $user
     * @param Company $company
     * @param int $days
     *
     * @return void
     */
    protected function notifyLeadNotPurchased(User $user, Company $company, int $days): void
    {
        $subject = "$company->name – No Lead Purchases in $days Days";
        $content = <<<CONTENT
Hi $user->name,

<a href="{$this->getCompanyUrl($company)}">$company->name</a> has not purchased any leads in the past $days days.

You may want to follow up and see if they need any assistance or have questions.

Regards,<br>
{$this->getSender()}
CONTENT;

        $this->sendEmailNotification(
            user: $user,
            subject: $subject,
            content: $content
        );
    }

    /**
     * @param User $user
     * @param Company $company
     * @param string $role
     * @param string $reason
     *
     * @return void
     */
    protected function notifyAssigned(User $user, Company $company, string $role, string $reason): void
    {
        $role    = Str::headline($role);
        $subject = "You have been assigned as the $role for $company->name.";
        $content = <<<CONTENT
Hi {$user->name},

You have been assigned as the $role for <a href="{$this->getCompanyUrl($company)}">$company->name</a>.

Reason: $reason

Regards,<br>
{$this->getSender()}
CONTENT;

        $this->sendEmailNotification(
            user: $user,
            subject: $subject,
            content: $content
        );
    }

    /**
     * @param User $user
     * @param Company $company
     * @param string $role
     * @param string $reason
     *
     * @return void
     */
    protected function notifyUnassigned(User $user, Company $company, string $role, string $reason): void
    {
        $role    = Str::headline($role);
        $subject = "You have been unassigned as the $role for $company->name.";
        $content = <<<CONTENT
Hi {$user->name},

You have been unassigned as the $role for <a href="{$this->getCompanyUrl($company)}">$company->name</a>.

Reason: $reason

Regards,<br>
{$this->getSender()}
CONTENT;

        $this->sendEmailNotification(
            user: $user,
            subject: $subject,
            content: $content
        );
    }

    /**
     * @param Company $company
     *
     * @return void
     */
    protected function notifyPreAssignment(Company $company): void
    {
        SendAmPreAssignmentNotificationJob::dispatch($company->id)
            ->delay(Carbon::now()->setTimezone('America/Denver')->addDays(15)->setTime(8, 0)); //8AM Denver time

        $company->refresh();

        User::query()->whereIn(User::FIELD_EMAIL, self::USER_EMAILS)->get()->each(function (User $user) use ($company) {
            $subject = 'Account Manager pre-assignment';
            $content = <<<CONTENT
Hi {$user->name},

<b>{$company->preassignedAccountManager->name}</b> has been pre-assigned as the Account Manager for <a href="{$this->getCompanyUrl($company)}">$company->name</a>.

Regards,<br>
{$this->getSender()}
CONTENT;

            $this->sendEmailNotification($user, $subject, $content);
        });
    }

    /**
     * @param User $user
     * @param string $subject
     * @param string $content
     *
     * @return void
     */
    protected function sendEmailNotification(User $user, string $subject, string $content): void
    {
        // these will send at approximately 8am MST
        SendGenericEmailJob::dispatch(
            $user->email,
            $subject,
            null,
            $content
        )->delay(Carbon::now()->addHours(4));
    }

    /**
     * @param Company $company
     *
     * @return string
     */
    protected function getCompanyUrl(Company $company): string
    {
        return  config('app.url') . "/companies/$company->id";
    }

    /**
     * @return string
     */
    protected function getSender(): string
    {
        return config('app.name');
    }
}
