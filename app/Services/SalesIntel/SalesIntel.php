<?php

namespace App\Services\SalesIntel;

use App\DTO\SalesIntel\PersonDTO;
use App\DTO\SalesIntel\ProspectDTO;
use App\Enums\Prospects\ProspectResolution;
use App\Enums\Prospects\ProspectStatus;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Prospects\Contact;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\SalesIntel\CompanyImportRecord;
use App\Models\SalesIntel\FailedCompanyImportRecord;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;

class SalesIntel
{
    private PendingRequest $client;

    public function __construct()
    {
        $this->client = Http::withHeaders([
            'X-CB-ApiKey' => config('services.salesintel.api.key'),
            'Accept' => 'application/json',
        ])->baseUrl('https://api.salesintel.io/service')
            ->throw();
    }

    public function importUsers(Company $company)
    {
        $this->importPeople($company->website, CompanyUser::class, 10)
            ->whenNotEmpty(
                fn ($contacts) => $company->users()->createMany($contacts),
                fn () => $company->failedImportRecords()->create()
            );
    }

    public function importContacts(NewBuyerProspect $prospect)
    {
        $prospectDomain = str($prospect->company_website)->replaceMatches('((http(s*)(:\/\/))|(www.)|(/$))', '')->before('/');

        $this->importPeople($prospectDomain, Contact::class, 5)
            ->whenNotEmpty(
                fn ($contacts) => $prospect->contacts()->createMany($contacts),
                fn () => $prospect->resolve(ProspectStatus::CLOSED, ProspectResolution::NO_AVAILABLE_CONTACTS)
            );
    }

    public function importCompanies(string $filter, string $value, ?array $naics = [])
    {
        throw_if($this->hasFailedRecords($filter, $value, $naics), "No more Company records exist for {$filter}: {$value}.");

        $page = $this->determinePageForFilter($filter, $value, $naics);

        $params = collect([
            'is_international' => false,
            'company_naics_codes' => when(filled($naics), implode(',', $naics)),
            ...match ($filter) {
                'zipcode' => [
                    'company_location_zipcodes' => $value,
                    'company_location_zipcodes_distance' => 50,
                ],
                'state' => [
                    'company_location_states' => $value,
                ],
                'domain' => [
                    'company_domain' => $value,
                ],
                'specialty' => [
                    'specialties' => $value,
                ]
            },
            'page' => $page,
        ]);

        $this->client
            ->get('/company', $params)
            ->collect('search_results')
            ->when($filter === 'domain', fn ($records) => $records->transform(fn ($record) => [...$record, 'targeted' => 'true']))
            ->mapInto(ProspectDTO::class)
            ->map(fn ($dto) => $dto->toArray())
            ->reject(fn ($dto) => empty($dto) || NewBuyerProspect::whereCompanyWebsite($dto['company_website'])->exists())
            ->collect()
            ->whenNotEmpty(
                fn ($prospects) => $prospects->each(
                    fn ($prospect) => NewBuyerProspect::create($prospect)
                        ->salesIntelImportRecord()
                        ->create([
                            'filter' => $filter,
                            'value' => $value,
                            'page' => $page,
                            'naics' => when($naics, implode(',', $naics)),
                        ])
                ),
                fn () => FailedCompanyImportRecord::create([
                    'filter' => $filter,
                    'value' => $value,
                    'naics' => when($naics, implode(',', $naics)),
                ])
            );
    }

    private function importPeople(string $domains, string $model, int $limit = 0)
    {
        return $this->client
            ->get('/people', [
                'company_domains' => $domains,
                'is_international' => false,
            ])
            ->collect('search_results')
            ->sortByDesc('verified')
            ->mapInto(PersonDTO::class)
            ->map(fn ($dto) => $dto->arrayFor($model))
            ->filter()
            ->when($limit, fn ($results) => $results->take($limit));
    }

    private function determinePageForFilter(string $filter, string $value, array $naics)
    {
        $latestImport = CompanyImportRecord::latest('id')
            ->whereFilter($filter)
            ->whereValue($value)
            ->when(
                $naics,
                fn ($query) => $query->whereNaics(implode(',', $naics)),
                fn ($query) => $query->whereNull('naics')
            )
            ->first();

        return $latestImport ? $latestImport->page + 1 : 1;
    }

    public function hasFailedRecords(string $filter, string $value, ?array $naics)
    {
        return FailedCompanyImportRecord::whereFilter($filter)
            ->whereValue($value)
            ->when(
                $naics,
                fn ($query) => $query->whereNaics(implode(',', $naics)),
                fn ($query) => $query->whereNull('naics')
            )->exists();
    }
}
