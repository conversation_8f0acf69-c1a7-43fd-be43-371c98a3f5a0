<?php

namespace App\Jobs;

use App\DTO\Meet\ConferenceDTO;
use App\Enums\AppFeature;
use App\Enums\Calendar\DemoStatus;
use App\Exceptions\GoogleMeet\GoogleMeetTokenNotFoundException;
use App\Models\Calendar\CalendarEvent;
use App\Models\Calendar\Demo;
use App\Services\AppLogger;
use App\Services\Conference\ConferenceSyncService;
use App\Services\GoogleMeetService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class SyncConferenceData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected CalendarEvent $calendarEvent)
    {
        $this->queue = 'mailbox_handle_mail_provider_event';
    }

    /**
     * @throws Exception
     */
    public function handle(
        GoogleMeetService $googleMeetService,
        ConferenceSyncService $conferenceService
    ): void
    {
        $appLogger = AppLogger::make(
            relations: [$this->calendarEvent],
            feature  : AppFeature::CALENDAR,
            function : 'syncConferenceData'
        );

        try {
            $eventConferences = $googleMeetService->getConferenceData(
                calendarEvent: $this->calendarEvent
            );

            $relevantConferences = $this->filterOutIrrelevantConferences($eventConferences);

            if ($relevantConferences->isEmpty()) {
                $appLogger->warn(
                    message: 'No relevant conferences found',
                );
                return;
            }

            foreach ($relevantConferences as $conference) {
                $conferenceService->importConference(
                    userId         : $this->calendarEvent->user_id,
                    calendarEventId: $this->calendarEvent->id,
                    conference     : $conference
                );
            }

            Demo::query()
                ->where(Demo::FIELD_CALENDAR_EVENT_ID, $this->calendarEvent->id)
                ->update([
                    Demo::FIELD_STATUS => DemoStatus::COMPLETED
                ]);

            $this->calendarEvent->update([
                CalendarEvent::FIELD_LAST_CONFERENCE_DATA_SYNC_AT => now(),
            ]);
        } catch (GoogleMeetTokenNotFoundException $exception) {
            $appLogger->exception(
                exception: $exception,
            );
        }
    }

    /**
     * @param Collection<ConferenceDTO> $conferences
     * @return Collection
     */
    public function filterOutIrrelevantConferences(Collection $conferences): Collection
    {
        return $conferences
            ->filter(function (ConferenceDTO $conference) {
                return $conference->getParticipants()->count() > 1
                    && $conference->getDurationInSeconds() > 0;
            });
    }
}
