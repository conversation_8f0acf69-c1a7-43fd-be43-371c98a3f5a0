<?php

namespace App\Jobs\Company;

use App\Enums\RoleType;
use App\Jobs\Emails\SendGenericEmailJob;
use App\Models\Odin\Company;
use App\Repositories\Odin\CompanyRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Str;

class SendAmPreAssignmentNotificationJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(public int $companyId) {}

    /**
     * Execute the job.
     */
    public function handle(CompanyRepository $repository): void
    {
        $company = $repository->findOrFail($this->companyId);

        if(!$company->preassignedAccountManager) {
            return;
        }

        $role    = Str::headline(RoleType::ACCOUNT_MANAGER->value);
        $user    = $company->preassignedAccountManager;
        $subject = "You have been selected as the upcoming $role for $company->name.";
        $content = <<<CONTENT
Hi {$user->name},

You have been selected as the upcoming $role for <a href="{$this->getCompanyUrl($company)}">$company->name</a>.

This is a great opportunity to introduce yourself and start building the relationship with the company.

Regards,<br>
{$this->getSender()}
CONTENT;

        SendGenericEmailJob::dispatchSync(
            $user->email,
            $subject,
            null,
            $content
        );
    }

    /**
     * @param Company $company
     *
     * @return string
     */
    protected function getCompanyUrl(Company $company): string
    {
        return  config('app.url') . "/companies/$company->id";
    }

    /**
     * @return string
     */
    protected function getSender(): string
    {
        return config('app.name');
    }
}
