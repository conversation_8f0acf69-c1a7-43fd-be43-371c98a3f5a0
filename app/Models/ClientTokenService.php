<?php

namespace App\Models;

use Carbon\Carbon;
use \Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property int $id
 * @property string $service_key
 * @property string $service_name
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read ClientToken $clientToken
 */
class ClientTokenService extends BaseModel
{
    const TABLE = 'client_token_services';

    const FIELD_ID = 'id';
    const FIELD_SERVICE_KEY = 'service_key';
    const FIELD_SERVICE_NAME = 'service_name';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';

    const LEGACY_API_SERVICE_KEY = 'legacy_admin_api';
    const MICROSOFT_ADS_API_SERVICE_KEY = 'microsoft_ads_api';
    const META_ADS_API_SERVICE_KEY = 'meta_ads_api';
    const SCHEDULING_API_SERVICE_KEY = 'scheduling_api';
    const FLOW_ENGINES_API_SERVICE_KEY = 'flow_engines_api';

    const RELATION_CLIENT_TOKEN = 'clientToken';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return HasOne
     */
    public function clientToken(): HasOne
    {
        return $this->hasOne(ClientToken::class, ClientToken::FIELD_SERVICE_ID, self::FIELD_ID);
    }
}
