<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Spatie\Permission\Contracts\Role;

/**
 * Custom ModelHasRoles pivot model that extends <PERSON><PERSON>'s functionality
 * to include round robin capability tracking.
 *
 * @property int $role_id
 * @property string $model_type
 * @property int $model_id
 * @property bool $can_round_robin
 * @property-read Role $role
 * @property-read Model $model
 */
class ModelHasRoles extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'model_has_roles';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'role_id',
        'model_type',
        'model_id',
        'can_round_robin',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'can_round_robin' => 'boolean',
    ];

    /**
     * The primary key for the model.
     */
    protected $primaryKey = null;

    /**
     * Indicates if the IDs are auto-incrementing.
     */
    public $incrementing = false;

    /**
     * Get the role that this pivot belongs to.
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(config('permission.models.role'), 'role_id');
    }

    /**
     * Get the model that this pivot belongs to.
     */
    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    public function getCanRoundRobinAttribute(): bool
    {
        return $this->attributes['can_round_robin'] ?? true;
    }

    public function setCanRoundRobinAttribute(bool $value): void
    {
        $this->attributes['can_round_robin'] = $value;
    }

    public function canParticipateInRoundRobin(): bool
    {
        return $this->can_round_robin;
    }
}
