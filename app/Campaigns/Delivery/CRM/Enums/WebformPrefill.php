<?php

namespace App\Campaigns\Delivery\CRM\Enums;

use App\Campaigns\Delivery\CRM\BaseCRMDeliverer;
use App\Campaigns\Delivery\CRM\WebForm\StandardWebFormCRMDeliverer;

enum WebformPrefill: string
{
    case LEAD_PERFECTION = 'Lead Perfection';
    case HATCH           = 'Hatch';
    case MARKET_SHARP    = 'Market Sharp';
    case ZAPIER          = 'Zapier';
    case LEGACY_ADMIN    = 'Legacy Admin';

    public static function getAllPrefillData(): array
    {
        return array_reduce(self::cases(), function(array $output, WebformPrefill $case) {
            $output[$case->value] = $case->getPrefillData();
            return $output;
        }, []);
    }

    protected function getPrefillData(): array
    {
        return match ($this) {
            self::LEAD_PERFECTION => $this->leadPerfectionPrefill(),
            self::HATCH           => $this->hatchPrefill(),
            self::MARKET_SHARP    => $this->marketSharpPrefill(),
            self::ZAPIER          => $this->zapierPrefill(),
            self::LEGACY_ADMIN    => $this->legacyPrefill(),
            default               => [],
        };
    }

    protected function leadPerfectionPrefill(): array
    {
        return [
            BaseCRMDeliverer::SYSTEM_FIELDS_KEY => [
                StandardWebFormCRMDeliverer::FIELD_SEND_FORMAT => 'POST',
                'success_keyword' => '200',
            ],
            BaseCRMDeliverer::CUSTOM_FIELDS_KEY => [
                'firstname'      => '[first_name]',
                'lastname'       => '[last_name]',
                'email'          => '[email]',
                'Lead Sale Type' => '[lead_sale_type]',
                'city'           => '[city]',
                'state'          => '[state]',
                'notes'          => '[combined_comments]',
                'quote id'       => '[lead_id]',
                'zip'            => '[zip_code]',
                'phone1'         => '[phone]',
                'address1'       => '[address]',
                'srs_id'         => '',
                'sender'         => '',
                'productID'      => '',
                'Source ID'      => '',
            ],
        ];
    }

    protected function hatchPrefill(): array
    {
        return [
            BaseCRMDeliverer::SYSTEM_FIELDS_KEY => [
                'url' => 'https://api.usehatchapp.com/v1/contacts',
                StandardWebFormCRMDeliverer::FIELD_SEND_FORMAT => 'JSON',
            ],
            BaseCRMDeliverer::HEADERS_KEY => [
                'Authorization' => '',
            ],
            BaseCRMDeliverer::JSON_FIELDS_KEY => [
                'body' => '{
                    "details": {
                        "address": "[full_address]",
                        "campaign": "[campaign_name]",
                        "leadprice": "[lead_price]",
                        "utilityprovider": "[utility_name]",
                        "electricbill": "[electric_spend]"
                    },
                    "email": "[email]",
                    "firstName": "[first_name]",
                    "lastName": "[last_name]",
                    "phoneNumber": "[phone]",
                    "source":
                }',
            ],
        ];
    }

    protected function zapierPrefill(): array
    {
        return [
            BaseCRMDeliverer::SYSTEM_FIELDS_KEY => [
                StandardWebFormCRMDeliverer::FIELD_SEND_FORMAT => 'POST',
            ],
            BaseCRMDeliverer::CUSTOM_FIELDS_KEY => [
                'First Name'     => '[first_name]',
                'Last Name'      => '[last_name]',
                'Date'           => '[date]',
                'Lead Source'    => '[lead_source]',
                'Lead ID'        => '[lead_id]',
                'Lead Price'     => '[lead_price]',
                'Lead Sale Type' => '[lead_sale_type]',
                'Email'          => '[email]',
                'Phone'          => '[phone]',
                'Address'        => '[address]',
                'City'           => '[city]',
                'State'          => '[state_abbr]',
                'Zip Code'       => '[zip_code]',
                'Notes'          => '[combined_comments]',
            ],
        ];
    }

    protected function marketSharpPrefill(): array
    {
        return [
            BaseCRMDeliverer::SYSTEM_FIELDS_KEY => [
                StandardWebFormCRMDeliverer::FIELD_SEND_FORMAT => 'POST',
                'success_keyword' => '200',
            ],
            BaseCRMDeliverer::CUSTOM_FIELDS_KEY => [
                'MSM_firstname'                 => '[first_name]',
                'MSM_lastname'                  => '[last_name]',
                'MSM_address1'                  => '[address1]',
                'MSM_address2'                  => '[address2]',
                'MSM_city'                      => '[city]',
                'MSM_state'                     => '[state]',
                'MSM_zip'                       => '[zip_code]',
                'MSM_email'                     => '[email]',
                'MSM_homephone'                 => '[phone]',
                'MSM_custom_interests'          => '[lead_industry]',
                'MSM_source'                    => '',
                'MSM_coy'                       => '',
                'MSM_formId'                    => '',
                'MSM_leadCaptureName'           => '',
                'MSM_custom_Best_Time_To_Reach' => '[combined_comments]',
            ],
        ];
    }

    protected function legacyPrefill(): array
    {
        return [
            BaseCRMDeliverer::CUSTOM_FIELDS_KEY => [
                'first_name'           => '[first_name]',
                'last_name'            => '[last_name]',
                'full_name'            => '[full_name]',
                'lead_process_date'    => '[date]',
                'lead_source'          => '[lead_source]',
                'lead_industry'        => '[lead_industry]',
                'account_name'         => '[account_name]',
                'email'                => '[email]',
                'phone'                => '[phone]',
                'address'              => '[address]',
                'address_1'            => '[address_1]',
                'address_2'            => '[address_2]',
                'city'                 => '[city]',
                'state_abbreviation'   => '[state_abbr]',
                'state_name'           => '[state_name]',
                'country_abbreviation' => '[country_abbr]',
                'country_name'         => '[country]',
                'full_address'         => '[full_address]',
                'combined_comments'    => '[combined_comments]',
            ],
        ];
    }
}