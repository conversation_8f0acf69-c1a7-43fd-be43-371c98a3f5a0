<?php

namespace App\DataModels\Campaigns;

use App\ConsumerProcessing\Jobs\AttemptConsumerProjectAllocationJob;
use App\Enums\Odin\Product;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\SaleTypes as SaleTypesEnum;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\DirectLeadsRepository;
use App\Repositories\ProductAppointmentRepository;
use App\Services\Campaigns\PotentialProductService;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class ConsumerProject
{
    public Carbon $created_at;

    public int $max_contact_requests;
    public bool $had_budget_coverage = true;

    private ?array $potentialProducts = null;

    protected int $countyLocationId = 0;
    protected int $zipCodeLocationId = 0;

    /**
     * @param Consumer $consumer
     * @param Address $address
     * @param Carbon|null $last_qualified_at
     * @param bool $skipTimezoneDelay
     * @param bool|null $unverifiedOnly
     * @param int[]|null $excludedCompanies
     * @param bool $isDirectLead
     */
    public function __construct(
        public Consumer    $consumer,
        public Address     $address,
        public ?Carbon     $last_qualified_at,
        public bool        $skipTimezoneDelay = false,
        public ?bool       $unverifiedOnly = null,
        protected ?array   $excludedCompanies = null,
        public bool        $isDirectLead = false,
        protected bool     $underReview = false
    )
    {
        $this->created_at = $this->consumer->created_at;
        if ($this->hasAppointments()) {
            $this->max_contact_requests = min(
                $this->appointments()->count(),
                AttemptConsumerProjectAllocationJob::GLOBAL_ALLOCATION_LIMIT,
            );
        }
        else {
            $baseRequestedContacts = $this->leadConsumerProduct()->contact_requests ?: $this->consumer->max_contact_requests;
            $this->max_contact_requests = min(
                $baseRequestedContacts,
                AttemptConsumerProjectAllocationJob::GLOBAL_ALLOCATION_LIMIT
            );
        }

        if ($this->consumer->max_contact_requests !== $this->max_contact_requests) {
            $this->consumer->update([Consumer::FIELD_MAX_CONTACT_REQUESTS => $this->max_contact_requests]);
            $this->consumer->refresh();
        }

        $this->storeLocationIds();
    }

    /**
     * @return Collection<int, ProductAssignment>
     */
    public function productAssignments(): Collection
    {
        return $this->consumer->refresh()
            ->consumerProducts
            ->reduce(fn(Collection $output, ConsumerProduct $product) => $output->merge($product->productAssignment()->get()), collect());
    }

    /**
     * This currently only checks if the Lead product has already been invoiced
     * @return bool
     */
    public function leadProductIsInvoiced(): bool
    {
        return $this->leadConsumerProduct()
            ?->productAssignment()
            ->join(DatabaseHelperService::readOnlyDatabase() .'.'. EloquentQuoteCompany::TABLE, fn(JoinClause $join) =>
                $join->on(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_LEGACY_ID, '=', EloquentQuoteCompany::TABLE .'.'. EloquentQuoteCompany::QUOTE_COMPANY_ID)
                    ->where(EloquentQuoteCompany::TABLE .'.'. EloquentQuoteCompany::INVOICE_ITEM_ID, '>', 0)
            )->exists()
            ?? false;
    }

    /**
     * @return array
     */
    public function potentialProducts(): array
    {
        if (!$this->potentialProducts) {
            /** @var PotentialProductService $potentialProductService */
            $potentialProductService = app(PotentialProductService::class);
            $this->potentialProducts = $potentialProductService->getPotentialProductsForConsumerProject($this);
        }
        return $this->potentialProducts ?? [];
    }

    /**
     * Collect secondary industry services for consumer
     * @return array
     */
    public function secondaryIndustryServices(): array
    {
        return $this->consumer->consumerProducts()
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID, '=', ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->where(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_IS_SECONDARY_SERVICE, true)
            ->select(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
            ->pluck(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
            ->unique()
            ->toArray();
    }

    /**
     * Check if this is a cross-service product
     * @return bool
     */
    public function isMultiService(): bool
    {
        return count($this->secondaryIndustryServices()) > 0;
    }

    /**
     * Attempt to find a CP id attached to the project matching a given service product, for multi-service consumers.
     * @param int $serviceProductId
     * @return int|null
     */
    public function tryConsumerProductIdFromServiceProductId(int $serviceProductId): ?int
    {
        return $this->consumer->consumerProducts()
            ->where(ConsumerProduct::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
            ->first()
            ?->id
            ?? null;
    }

    /**
     * @return EloquentQuote
     */
    public function legacyLead(): EloquentQuote
    {
        return $this->consumer->legacyLead;
    }

    /**
     * @return ConsumerProduct
     */
    public function leadConsumerProduct(): ConsumerProduct
    {
        /** @var ConsumerProduct */
        return $this->getConsumerProductByProductQuery(Product::LEAD)->first();
    }

    /**
     * @param ProductEnum $product
     * @return ConsumerProduct|null
     */
    public function getFirstConsumerProductByProduct(ProductEnum $product): ?ConsumerProduct
    {
        /** @var ConsumerProduct|null */
        return $this->getConsumerProductByProductQuery($product)->first();
    }

    /**
     * @return Collection<ProductAppointment>
     */
    public function appointments(): Collection
    {
        return $this->leadConsumerProduct()->leadAppointments;
    }

    /**
     * @return bool
     */
    public function hasAppointments(): bool
    {
        return $this->appointments()->count() > 0;
    }

    /**
     * @return Collection<ConsumerProduct>
     */
    public function appointmentConsumerProducts(): Collection
    {
        return $this->getConsumerProductByProductQuery(Product::APPOINTMENT)
            ->with(ConsumerProduct::RELATION_APPOINTMENT)
            ->get();
    }

    /**
     * @return int
     */
    public function appointmentConsumerProductsCount(): int
    {
        return $this->getConsumerProductByProductQuery(Product::APPOINTMENT)->count();
    }

    /**
     * @return string
     */
    public function zipCode(): string
    {
        return $this->address->zip_code;
    }

    /**
     * @return int
     */
    public function countyLocationId(): int
    {
        if (property_exists($this, 'countyLocationId') && $this->countyLocationId > 0) {
            return $this->countyLocationId;
        }
        // temp workaround for queued jobs
        else {
            /** @var LocationRepository $locationRepository */
            $locationRepository = app(LocationRepository::class);

            return $locationRepository->getCountyFromZipcode($this->zipCode())?->id ?? 0;
        }
    }
    /**
     * @return int
     */
    public function zipCodeLocationId(): int
    {
        if (property_exists($this, 'zipCodeLocationId') && $this->zipCodeLocationId > 0) {
            return $this->zipCodeLocationId;
        }
        // temp workaround for queued jobs
        else {
            /** @var LocationRepository $locationRepository */
            $locationRepository = app(LocationRepository::class);

            return $locationRepository->getZipCode($this->zipCode())?->id ?? 0;
        }
    }

    /**
     * @return string
     */
    public function propertyType(): string
    {
        return $this->leadConsumerProduct()->propertyType?->name ?? PropertyType::RESIDENTIAL->value;
    }

    /**
     * @return int
     */
    public function propertyTypeId(): int
    {
        return $this->leadConsumerProduct()->propertyType?->id ?? PropertyType::RESIDENTIAL->model()->id;
    }

    /**
     * @return int
     */
    public function createAppointmentConsumerProducts(): int
    {
        /** @var ProductAppointmentRepository $repository */
        $repository = app(ProductAppointmentRepository::class);

        if (!$repository->appointmentsExistForConsumerProject($this))
            return 0;

        $repository->createAppointmentConsumerProductsForConsumerProject($this);

        return $this->appointmentConsumerProducts()->count();
    }

    /**
     * @return bool
     */
    public function createDirectLeadsConsumerProduct(): bool
    {
        /** @var DirectLeadsRepository $direLeadsRepository */
        $direLeadsRepository = app(DirectLeadsRepository::class);

      return $direLeadsRepository->createDirectLeadConsumerProduct($this->leadConsumerProduct());
    }

    /**
     * @return string
     */
    public function getSaleTypeFromConsumer(): string
    {
        return match($this->consumer->max_contact_requests) {
            2 => SaleTypesEnum::DUO->value,
            3 => SaleTypesEnum::TRIO->value,
            default => SaleTypesEnum::EXCLUSIVE->value,
        };
    }

    /**
     * @return ConsumerProduct
     */
    public function getDirectLeadConsumerProductOrFail(): ConsumerProduct
    {
        /** @var ConsumerProduct */
        return $this->getConsumerProductByProductQuery(Product::DIRECT_LEADS)->firstOrFail();
    }

    /**
     * @return ConsumerProduct|null
     */
    public function getDirectLeadConsumerProduct(): ?ConsumerProduct
    {
        /** @var ConsumerProduct|null */
        return $this->getConsumerProductByProductQuery(Product::DIRECT_LEADS)->first();
    }

    /**
     * @return bool
     */
    public function hasOptInCompanies(): bool
    {
        return $this->leadConsumerProduct()->optInCompanies()->count() > 0;
    }

    /**
     * @return bool
     */
    public function unverifiedSaleOnly(): bool
    {
        return $this->unverifiedOnly ?? false;
    }

    /**
     * @return int[]|null
     */
    public function getExcludedCompanies(): ?array
    {
        return $this->excludedCompanies ?? null;
    }

    /**
     * @return bool
     */
    public function getUnderReview(): bool
    {
        return $this->underReview ?? false;
    }

    /**
     * Store the location ids to make location filter() more efficient
     * @return void
     */
    protected function storeLocationIds(): void
    {
        // Temporary check for queue/serialization property issue
        if (property_exists($this, 'zipCodeLocationId') && property_exists($this, 'countyLocationId')) {
            /** @var LocationRepository $locationRepository */
            $locationRepository      = app(LocationRepository::class);
            $this->zipCodeLocationId = $locationRepository->getZipCode($this->zipCode())?->id ?? 0;
            $this->countyLocationId  = $locationRepository->getCountyFromZipcode($this->zipCode())?->id ?? 0;
        }
        else
            logger()->warning("Missing own properties 'countyLocationId'/'zipCodeLocationId' on " . self::class);
    }

    protected function getConsumerProductByProductQuery(ProductEnum $product): Builder
    {
        return $this->consumer->consumerProducts()
            ->whereHas(ConsumerProduct::RELATION_SERVICE_PRODUCT, fn(Builder $query) =>
                $query->where(ServiceProduct::FIELD_PRODUCT_ID, $product->model()->id)
            )->getQuery();
    }

    /**
     * @return int
     */
    public function ageInMinutes(): int
    {
        return Carbon::now()->diffInMinutes($this->consumer->created_at);
    }
}
