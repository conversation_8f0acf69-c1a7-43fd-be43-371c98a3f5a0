<?php

namespace App\Repositories;

use App\Models\Odin\CompanyConfiguration;
use Illuminate\Support\Collection;

class CompanyConfigurationRepository
{
    public function getCompanyConfiguration(int $companyId): ?CompanyConfiguration
    {
        /** @var ?CompanyConfiguration $companyConfiguration */
        $companyConfiguration = CompanyConfiguration::query()
            ->where(CompanyConfiguration::FIELD_COMPANY_ID, $companyId)
            ->first();

        return $companyConfiguration;
    }

    public function updateOrCreateCompanyConfiguration(
        int $companyId,
        ?bool $allowLeadsNoCc = null,
        ?bool $enableTcpaPlayback = null,
        ?bool $neverExceedBudget = null,
        ?bool $disallowRanking = null,
        ?bool $receiveOffHourLeads = null,
        ?bool $appointmentsActive = null,
        ?bool $miAppointmentsActive = null,
        ?bool $requireAppointmentsCalendar = null,
        ?bool $missedProductsActive = null,
        ?bool $reviewEnabled = null,
        ?bool $unrestrictedZipCodeTargeting = null,
        ?bool $acceptUnderReviewLeads = null
    ): CompanyConfiguration
    {
        /** @var CompanyConfiguration $companyConfiguration */
        $companyConfiguration = CompanyConfiguration::query()->updateOrCreate([
            CompanyConfiguration::FIELD_COMPANY_ID => $companyId
        ],
        collect([
            CompanyConfiguration::FIELD_COMPANY_ID                      => $companyId,
            CompanyConfiguration::FIELD_ALLOW_LEADS_NO_CC               => $allowLeadsNoCc,
            CompanyConfiguration::FIELD_ENABLE_TCPA_PLAYBACK            => $enableTcpaPlayback,
            CompanyConfiguration::FIELD_NEVER_EXCEED_BUDGET             => $neverExceedBudget,
            CompanyConfiguration::FIELD_DISALLOW_RANKING                => $disallowRanking,
            CompanyConfiguration::FIELD_RECEIVE_OFF_HOUR_LEADS          => $receiveOffHourLeads,
            CompanyConfiguration::FIELD_APPOINTMENTS_ACTIVE             => $appointmentsActive,
            CompanyConfiguration::FIELD_MI_APPOINTMENTS_ACTIVE          => $miAppointmentsActive,
            CompanyConfiguration::FIELD_REQUIRE_APPOINTMENTS_CALENDAR   => $requireAppointmentsCalendar,
            CompanyConfiguration::FIELD_MISSED_PRODUCTS_ACTIVE          => $missedProductsActive,
            CompanyConfiguration::FIELD_REVIEWS_ENABLED                 => $reviewEnabled,
            CompanyConfiguration::FIELD_UNRESTRICTED_ZIP_CODE_TARGETING => $unrestrictedZipCodeTargeting,
            CompanyConfiguration::FIELD_ACCEPT_UNDER_REVIEW_LEADS       => $acceptUnderReviewLeads,
        ])->filter(fn($value) => $value !== null)->toArray());

        return $companyConfiguration;
    }

    /**
     * @param array $companyIds
     * @param array $columns
     * @return Collection
     */
    public function getConfigurationsByCompanyIds(array $companyIds, array $columns = ['*']): Collection
    {
        return CompanyConfiguration::query()->whereIntegerInRaw(CompanyConfiguration::FIELD_COMPANY_ID, $companyIds)->get($columns);
    }
}
