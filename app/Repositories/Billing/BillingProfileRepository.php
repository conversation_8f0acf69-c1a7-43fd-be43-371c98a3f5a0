<?php

namespace App\Repositories\Billing;

use App\Enums\Billing\PaymentMethodServices;
use App\Enums\RoleType;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\CampaignBillingProfile;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\CompanyUserRelationship;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class BillingProfileRepository
{
    public function __construct(protected CompanyCampaignRepository $companyCampaignRepository)
    {

    }

    /**
     * @param PaymentMethodServices $paymentMethod
     * @param string $billingFrequencyCron
     * @param array $cronData
     * @param int $companyId
     * @param string $thresholdInDollars
     * @param string $maxAllowedChargeAttempts
     * @param int $dueInDays
     * @param string|null $billingContact
     * @param int|null $createdById
     * @param int|null $updatedById
     * @param string|null $paymentGatewayClientCode
     * @param string|null $paymentGatewayPaymentMethodCode
     * @param bool $processAuto
     * @param bool|null $default
     * @param int|null $id
     * @param int|null $paymentMethodId
     * @param int|null $invoiceTemplateId
     * @return BillingProfile
     */
    public function createOrUpdate(
        PaymentMethodServices $paymentMethod,
        string $billingFrequencyCron,
        array $cronData,
        int $companyId,
        string $thresholdInDollars,
        string $maxAllowedChargeAttempts,
        int $dueInDays,
        ?string $billingContact = null,
        ?int $createdById = null,
        ?int $updatedById = null,
        ?string $paymentGatewayClientCode = null,
        ?string $paymentGatewayPaymentMethodCode = null,
        ?bool $processAuto = true,
        ?bool $default = false,
        ?int $id = null,
        ?int $paymentMethodId = null,
        ?int $invoiceTemplateId = null
    ): BillingProfile
    {
        /** @var BillingProfile $profile */
        $profile = BillingProfile::query()->updateOrCreate([
            BillingProfile::FIELD_ID => $id,
        ], [
            BillingProfile::FIELD_CREATED_BY_ID                       => $createdById,
            BillingProfile::FIELD_PAYMENT_GATEWAY_CLIENT_CODE         => $paymentGatewayClientCode,
            BillingProfile::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE => $paymentGatewayPaymentMethodCode,
            BillingProfile::FIELD_PAYMENT_METHOD                      => $paymentMethod,
            BillingProfile::FIELD_BILLING_CONTACT                     => $billingContact,
            BillingProfile::FIELD_BILLING_FREQUENCY_CRON              => $billingFrequencyCron,
            BillingProfile::FIELD_CRON_DATA                           => $cronData,
            BillingProfile::FIELD_COMPANY_ID                          => $companyId,
            BillingProfile::FIELD_THRESHOLD_IN_DOLLARS                => $thresholdInDollars,
            BillingProfile::FIELD_MAX_ALLOWED_CHARGE_ATTEMPTS         => $maxAllowedChargeAttempts,
            BillingProfile::FIELD_DEFAULT                             => $default,
            BillingProfile::FIELD_PROCESS_AUTO                        => $processAuto,
            BillingProfile::FIELD_UPDATED_BY_ID                       => $updatedById,
            BillingProfile::FIELD_PAYMENT_METHOD_ID                   => $paymentMethodId,
            BillingProfile::FIELD_DUE_IN_DAYS                         => $dueInDays,
            BillingProfile::FIELD_INVOICE_TEMPLATE_ID                 => $invoiceTemplateId,
        ]);

        return $profile;
    }

    /**
     * @param string $paymentMethodCode
     * @return bool|null
     */
    public function deleteBillingProfile(string $paymentMethodCode): ?bool
    {
        /** @var BillingProfile $billingProfile */
        return $this->getBillingProfilesQuery(
            paymentMethodCode: $paymentMethodCode,
        )->delete();
    }

    /**
     * @param string $customerId
     * @param string $paymentMethod
     * @return bool
     */
    public function updateDefaultBillingProfile(string $customerId, string $paymentMethod): bool
    {
        /** @var BillingProfile $billingProfile */
        $billingProfile = $this->getBillingProfilesQuery(
            paymentMethodCode: $paymentMethod
        )->first();

        $this->getBillingProfilesQuery(
            companyId: $billingProfile->company_id
        )->update([BillingProfile::FIELD_DEFAULT => false]);

        return $billingProfile->update([BillingProfile::FIELD_DEFAULT => true]);
    }

    /**
     * @param int|null $id
     * @param int|null $companyId
     * @param string|null $billingContact
     * @param string|null $paymentMethodCode
     * @param string|null $paymentMethod
     * @param int|null $campaignId
     * @param User|null $filterByUserRole
     * @return Builder
     */
    public function getBillingProfilesQuery(
        ?int $id = null,
        ?int $companyId = null,
        ?string $billingContact = null,
        ?string $paymentMethodCode = null,
        ?string $paymentMethod = null,
        ?int $campaignId = null,
        ?User $filterByUserRole = null
    ): Builder
    {
        $query = BillingProfile::query()
            ->with(BillingProfile::RELATION_INVOICE_TEMPLATE)
            ->select(BillingProfile::TABLE . '.*')
            ->distinct();

        if (filled($companyId)) {
            $query->where(BillingProfile::TABLE . '.' . BillingProfile::FIELD_COMPANY_ID, $companyId);
        }

        if (filled($id)) {
            $query->where(BillingProfile::TABLE . '.' . BillingProfile::FIELD_ID, $id);
        }

        if (filled($paymentMethodCode)) {
            $query->where(BillingProfile::TABLE . '.' . BillingProfile::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE, $paymentMethodCode);
        }

        if (filled($paymentMethod)) {
            $query->where(BillingProfile::TABLE . '.' . BillingProfile::FIELD_PAYMENT_METHOD, $paymentMethod);
        }

        if (filled($billingContact)) {
            $query->whereHas(BillingProfile::RELATION_CONTACT, function (Builder $query) use ($billingContact) {
                $query->whereAny([
                    CompanyUser::FIELD_FIRST_NAME,
                    CompanyUser::FIELD_LAST_NAME,
                    CompanyUser::FIELD_EMAIL
                ], 'LIKE', '%' . $billingContact . '%');
            });
        }

        if (filled($campaignId)) {
            $query->whereHas(BillingProfile::RELATION_CAMPAIGNS, function (Builder $query) use ($campaignId) {
                return $query->where('campaign_id', $campaignId);
            });
        }

        if (filled($filterByUserRole) && empty($companyId)) {
            $isFinanceOwner = $filterByUserRole->hasRole(RoleType::FINANCE_OWNER);

            if (!$isFinanceOwner) {
                $query->join(
                    CompanyUserRelationship::TABLE,
                    CompanyUserRelationship::TABLE . '.' . CompanyUserRelationship::FIELD_COMPANY_ID,
                    BillingProfile::TABLE . '.' . BillingProfile::FIELD_COMPANY_ID
                )->where(CompanyUserRelationship::TABLE . '.' . CompanyUserRelationship::FIELD_USER_ID, $filterByUserRole->id);
            }
        }

        return $query
            ->orderByDesc(BillingProfile::TABLE . '.' . BillingProfile::FIELD_CREATED_AT);
    }

    /**
     * @param BillingProfile $billingProfile
     * @param array $campaignIds
     * @return true
     */
    public function syncAssociatedCompanyCampaigns(BillingProfile $billingProfile, array $campaignIds): true
    {
        $currentCampaigns = $billingProfile->campaigns->pluck(CompanyCampaign::FIELD_ID)->toArray();
        $restoreCampaigns = array_intersect($currentCampaigns, $campaignIds);
        $detachCampaigns = array_diff($currentCampaigns, $campaignIds);
        $attachCampaigns = array_diff($campaignIds, $currentCampaigns);

        if (!empty($restoreCampaigns)) {
            CampaignBillingProfile::query()
                ->where(CampaignBillingProfile::FIELD_BILLING_PROFILE_ID, $billingProfile->id)
                ->whereIn(CampaignBillingProfile::FIELD_CAMPAIGN_ID, $restoreCampaigns)
                ->update(['deleted_at' => null, 'updated_at' => now()]);
        }

        if (!empty($detachCampaigns)) {
            CampaignBillingProfile::query()
                ->where(CampaignBillingProfile::FIELD_BILLING_PROFILE_ID, $billingProfile->id)
                ->whereIn(CampaignBillingProfile::FIELD_CAMPAIGN_ID, $detachCampaigns)
                ->update(['deleted_at' => now()]);
        }

        if (!empty($attachCampaigns)) {
            $billingProfile->campaigns()->attach($attachCampaigns, ['created_at' => now(), 'updated_at' => now()]);
        }

        return true;
    }


    /**
     * @param BillingProfile $billingProfile
     * @param int $campaignId
     * @return true
     */
    public function attachCompanyCampaignToBillingProfile(BillingProfile $billingProfile, int $campaignId): true
    {
        $billingProfile->campaigns()->attach($campaignId);

        return true;
    }

    /**
     * Get billing profile by campaign id or fallback by company id
     * @param int $campaignId
     * @param int $companyId
     * @return BillingProfile|null
     */
    public function getBillingProfileByCampaignId(
        int $campaignId,
        int $companyId,
    ): ?BillingProfile
    {
        /** @var ?BillingProfile */
        return $this->getBillingProfilesQuery(
            companyId : $companyId,
            campaignId: $campaignId
        )->first();
    }

    /**
     * @param BillingProfile $billingProfile
     * @return bool
     */
    public function hasDefaultBillingProfiles(BillingProfile $billingProfile): bool
    {
        return BillingProfile::query()
            ->where(BillingProfile::FIELD_COMPANY_ID, $billingProfile->{BillingProfile::FIELD_COMPANY_ID})
            ->whereNot(BillingProfile::FIELD_ID, $billingProfile->{BillingProfile::FIELD_ID})
            ->where(BillingProfile::FIELD_DEFAULT, true)
            ->exists();
    }


    /**
     * @param int $companyId
     * @return ?BillingProfile
     */
    public function getCompanyDefaultBillingProfile(int $companyId): ?BillingProfile
    {
        return BillingProfile::query()
            ->where(BillingProfile::FIELD_COMPANY_ID, $companyId)
            ->where(BillingProfile::FIELD_DEFAULT, true)
            ->first();
    }


    /**
     * Return the total of chargeable-uninvoiced leads grouped by billing profile
     * @param int|string|null $greaterOrEqualValue
     * @param bool $overThreshold
     * @return Builder
     */
    public function getTotalChargeableUninvoicedLeadsGroupedByBillingProfile(
        int|string|null $greaterOrEqualValue = null,
        bool $overThreshold = false
    ): Builder
    {
        return BillingProfile::query()
            ->select([
                DB::raw(BillingProfile::TABLE . '.' . BillingProfile::FIELD_ID . ' as billing_profile_id'),
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_COMPANY_ID,
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_THRESHOLD_IN_DOLLARS,
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_DEFAULT,
                DB::raw('SUM(sub.total_leads_cost_in_dollars) as total_leads_cost_in_dollars'),
                DB::raw('SUM(sub.count_leads) as count_leads'),
                DB::raw("GROUP_CONCAT(sub.campaign_id SEPARATOR ', ') as campaign_ids"),
                DB::raw("GROUP_CONCAT(sub.campaign_name SEPARATOR ', ') as campaign_name"),
            ])
            ->leftJoin(
                CampaignBillingProfile::TABLE,
                CampaignBillingProfile::TABLE . '.' . CampaignBillingProfile::FIELD_BILLING_PROFILE_ID,
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_ID
            )
            ->joinSub($this->companyCampaignRepository->getTotalChargeableUninvoicedLeadsGroupedByCampaign(), 'sub', function ($join) {
                $join->on(
                    CampaignBillingProfile::TABLE . '.' . CampaignBillingProfile::FIELD_CAMPAIGN_ID,
                    '=',
                    "sub.campaign_id"
                )->whereNull(CampaignBillingProfile::TABLE . '.' . 'deleted_at');
            })
            ->when(!is_null($greaterOrEqualValue), function ($query) use ($greaterOrEqualValue) {
                $query->havingRaw('total_leads_cost_in_dollars >= ?', [$greaterOrEqualValue]);
            })
            ->when($overThreshold, function ($query) {
                $query->havingRaw('total_leads_cost_in_dollars >= ' . BillingProfile::TABLE . '.' . BillingProfile::FIELD_THRESHOLD_IN_DOLLARS);
            })
            ->groupBy([
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_ID,
            ])
            ->orderBy(BillingProfile::TABLE . '.' . BillingProfile::FIELD_COMPANY_ID)
            ->orderBy(BillingProfile::TABLE . '.' . BillingProfile::FIELD_DEFAULT);
    }
}
