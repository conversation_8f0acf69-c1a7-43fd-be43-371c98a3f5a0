<?php

namespace App\Repositories;

use App\Contracts\Repositories\UserRepositoryContract;
use App\Enums\RoleType;
use App\Models\Role;
use App\Models\User;
use App\Models\UserPhone;
use App\Repositories\Legacy\LeadProcessingRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class UserRepository implements UserRepositoryContract
{
    public function getUserByLegacyId(int $id): ?User
    {
        /** @var User $user */
        $user = User::query()->where(User::FIELD_LEGACY_USER_ID, $id)->first();

        return $user;
    }

    /**
     * Handles searching and returning admin users by their name.
     *
     * @param string $name
     * @param array  $fields
     * @return Collection
     */
    public function getAdminUsersByName(string $name, array $fields = ["*"]): Collection
    {
        $queryString = Str::replace(" ", "%", $name);

        $query = User::query()
            ->select($fields)
            ->where(User::FIELD_NAME, 'LIKE', "%{$queryString}%");

        return $query->get();
    }

    /**
     * @inheritDoc
     */
    public function createUserFromAttributes(array $data = []): User
    {
        $user = new User();
        $user->fill(collect($data)->filter(fn($value) => $value !== null)->toArray());
        $user->save();

        return $user;
    }

    /**
     * @param int $phoneId
     * @param int $userId
     * @return UserPhone|Model
     */
    public function assignPhoneToUser(int $phoneId, int $userId): UserPhone
    {
        $oldPhone = UserPhone::query()->where(UserPhone::FIELD_USER_ID, $userId)->first();
        if($oldPhone?->id === $phoneId){return $oldPhone;}

        // Release old phone number
        $oldPhone?->delete();

        return UserPhone::firstOrCreate(
            [UserPhone::FIELD_PHONE_ID => $phoneId],
            [UserPhone::FIELD_USER_ID  => $userId]
        );
    }

    /**
     * @return Collection
     */
    public function getUsers() : Collection
    {
        return User::query()
            ->with([
                User::RELATION_PHONES,
                User::RELATION_ROLES
            ])
            ->orderBy(User::FIELD_ID)
            ->get();
    }

    /**
     * @param string $searchText
     * @param array $fields
     * @param array $excludeIds
     * @return Builder
     */
    public function searchUsersForAutocomplete(string $searchText, array $fields = ["*"], array $excludeIds = []): Builder
    {
        return $this->getSearchByNameQuery($searchText, $fields)
            ->whereNotIn(User::FIELD_ID, $excludeIds)
            ->limit(10);
    }

    /**
     * @inheritDoc
     */
    public function searchUsersByNameOrId(string $searchQuery, array $fields = ["*"]): ?Collection
    {
        return $this->getSearchByNameQuery($searchQuery, $fields)
            ->orWhere(User::FIELD_ID, '=', $searchQuery)
            ->get();
    }

    /**
     * @param string $searchText
     * @param array $fields
     * @return Builder
     */
    public function getSearchByNameQuery(string $searchText, array $fields = ["*"]): Builder
    {
        return User::query()
            ->select($fields)
            ->where(User::FIELD_NAME, 'LIKE', "%{$searchText}%");
    }

    /**
     * Returns 'system' user, to be used for automated functionality
     * Note: User is 'deleted' so as not to appear in UI
     *
     * @return User
     */
    public function getSystemUser(): User
    {
        $systemUser = User::where(User::FIELD_NAME, User::SYSTEM_USER_RESERVED_NAME)
            ->where(User::FIELD_EMAIL, User::SYSTEM_USER_RESERVED_EMAIL)
            ->withTrashed()
            ->first();
        if (!$systemUser) {
            /** @var LeadProcessingRepository $leadProcessingRepository */
            $leadProcessingRepository = app(LeadProcessingRepository::class);
            $systemUser = $leadProcessingRepository->createSystemUser();
        }
        return $systemUser;
    }

    public function getUsersByRole(RoleType $role): Collection
    {
        return User::query()->whereHas('roles', fn ($query) => $query->where('name', $role->value))->get();
    }


    /**
     * @param bool $showDeactivated
     * @param string|null $userDetail
     * @return Collection
     */
    public function listUsers(
        bool $showDeactivated,
        ?string $userDetail = null
    ): Collection
    {
        $query = User::query()
            ->with([
                User::RELATION_PHONES,
                User::RELATION_ROLES
            ]);

        if (!$showDeactivated) {
            $query->whereDoesntHave(User::RELATION_ROLES, function ($query) {
                $query->where(Role::FIELD_NAME, RoleType::DEACTIVATED->value);
            });
        } else {
            $query->whereHas(User::RELATION_ROLES, function ($query) {
                $query->whereIn(Role::FIELD_NAME, [RoleType::DEACTIVATED->value]);
            });
        }

        if ($userDetail) {
            $query->where(function ($query) use ($userDetail) {
                $query->where(User::FIELD_NAME, 'LIKE', "%{$userDetail}%")
                    ->orWhere(User::FIELD_EMAIL, 'LIKE', "%{$userDetail}%");
            });
        }

        return $query->orderBy(User::FIELD_ID)->get();
    }
    /**
     * @param array $permissions
     * @return Collection
     */
    public function getUsersByRolePermissions(array $permissions): Collection
    {
        return User::query()->whereHas(User::RELATION_ROLES . '.' . 'permissions', function($query) use ($permissions) {
            $query->whereIn('name', $permissions);
        })->get();
    }
}
