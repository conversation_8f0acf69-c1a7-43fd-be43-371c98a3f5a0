<?php
namespace App\View\Composers;
use App\Enums\PermissionType;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Gate;
use Illuminate\View\View;
class NavLinksComposer
{
    const array NAV_LINKS = [
        ['name' => 'dashboard', 'permission' => 'dashboard'],
        ['name' => 'admin', 'permission' => 'admin'],
        ['name' => 'user-management', 'permission' => 'user-management'],
        ['name' => 'minimum-price-management', 'permission' => PermissionType::MINIMUM_PRICE_MANAGEMENT_VIEW->value],
        ['name' => 'minimum-price-suggestions', 'permission' => PermissionType::INDUSTRY_CONFIGURATION->value],
        ['name' => 'task-management', 'permission' => 'admin'],
        ['name' => 'companies', 'permission' => 'companies'],
        ['name' => 'company-users', 'permission' => 'company-users/view'],
        ['name' => 'consumer-reviews', 'permission' => 'company-reviews/view'],
        ['name' => 'company-quality-score-management', 'permission' => 'company-quality-score-management'],
        ['name' => 'roles-permissions-management', 'linkName' => 'Roles & Permissions Management', 'permission' => 'permission-management/view'],
        ['name' => 'reports', 'permission' => 'reports'],
        ['name' => 'lead-search', 'route' => 'consumer-search', 'permission' => 'consumer-search'],
        ['name' => 'lead-processing', 'permission' => 'lead-processing'],
        ['name' => 'lead-processing-management', 'permission' => 'lead-processing-management'],
        ['name' => 'lead-refunds', 'linkName' => 'Lead refunds', 'permission' => PermissionType::LEAD_REFUNDS_VIEW->value],
        ['name' => 'sales-management', 'permission' => 'sales-management'],
        ['name' => 'tasks', 'permission' => 'dashboard'],
        ['name' => 'industry-management', 'permission' => 'industry-management'],
        ['name' => 'advertising', 'permission' => 'advertising'],
        ['name' => 'missed-revenue', 'permission' => 'missed-revenue'],
        ['name' => 'bundle-management', 'permission' => 'view-bundles'],
        ['name' => 'flow-management', 'permission' => 'flow-management'],
        ['name' => 'opportunity-notifications', 'permission' => 'opportunity-notifications'],
        ['name' => 'ruleset-management', 'permission' => 'ruleset-management'],
        ['name' => 'outreach-cadence', 'permission' => 'outreach-cadence'],
        ['name' => 'directory-management', 'permission' => 'silo-management'],
        ['name' => 'test-leads', 'permission' => 'test-leads'],
        ['name' => 'global-configurations-management', 'permission' => 'view-configurations'],
        ['name' => 'contract-management', 'permission' => PermissionType::CONTRACT_MANAGEMENT_VIEW->value],
        ['name' => 'horizon', 'permission' => 'horizon'],
        ['name' => 'privacy-management', 'permission' => PermissionType::PRIVACY_MANAGEMENT_VIEW->value],
        ['name' => 'sales-overview', 'permission' => PermissionType::SALES_OVERVIEW_VIEW->value],
        ['name' => 'company-campaign-delivery-logs', 'permission' => PermissionType::COMPANY_CAMPAIGN_DELIVERY_LOGS_VIEW->value],
        ['name' => 'company-campaign-custom-pricing-logs', 'permission' => PermissionType::COMPANY_CAMPAIGN_CUSTOM_PRICING_LOGS_VIEW->value],
        ['name' => 'affiliates', 'permission' => PermissionType::AFFILIATES->value],
        ['name' => 'billing-management', 'permission' => PermissionType::BILLING_MANAGEMENT_VIEW->value],
        ['name' => 'marketing', 'permission' => PermissionType::MARKETING_CAMPAIGN_VIEW->value],
        ['name' => 'template-management', 'permission' => PermissionType::TEMPLATE_MANAGEMENT->value],
        ['name' => 'business-development-management', 'route' => 'bdm-dashboard', 'permission' => PermissionType::PROSPECTOR->value],
        ['name' => 'prospect-configurations', 'route' => 'prospect-configurations', 'permission' => 'update-prospecting-configurations'],
    ];

    /**
     * Handles binding the nav link data to the view.
     *
     * @param View $view
     * @return void
     */
    public function compose(View $view): void
    {
        $view->with('navLinks', $this->filterNavLinks());
    }

    /**
     * Handles returning the nav links for a given user.
     *
     * @return Collection
     */
    protected function filterNavLinks(): Collection
    {
        return collect(self::NAV_LINKS)
            ->groupBy('permission')
            ->filter(fn($item, $key) => Gate::allows($key))
            ->flatten(1)
            ->sortBy('name');
    }
}
