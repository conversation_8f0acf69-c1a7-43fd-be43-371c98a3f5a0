<?php

namespace App\Enums\ActivityLog;

enum ActivityLogName: string
{
    case COMPANY_ACCOUNT_MANAGER_CHANGE         = 'company_account_manager_change';
    case COMPANY_RELATIONSHIP_MANAGER_CHANGE    = 'company_relationship_manager_change';
    case COMPANY_SUCCESS_MANAGER_CHANGE         = 'company_success_manager_change';
    case AFFILIATE_SHADOW                       = 'affiliate_shadow';
    case AFFILIATE_PAYOUT_STRATEGY_UPDATE       = 'affiliate_payout_strategy_update';
    case COMPANY_ROLE_UNASSIGNED                = 'company_role_unassigned';
    case COMPANY_ROLE_ASSIGNED                  = 'company_role_assigned';
    case COMPANY_ROLE_UPDATED                   = 'company_role_updated';
    case COMPANY_DELETION_QUEUED                = 'company_deletion_queued';
}
