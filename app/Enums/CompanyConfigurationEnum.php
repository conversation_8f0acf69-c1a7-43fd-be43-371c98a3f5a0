<?php

namespace App\Enums;

enum CompanyConfigurationEnum: string
{
    case ALLOW_LEADS_NO_CC             = 'allow_leads_no_cc';
    case ENABLE_TCPA_PLAYBACK          = 'enable_tcpa_playback';
    case NEVER_EXCEED_BUDGET           = 'never_exceed_budget';
    case DISALLOW_RANKING              = 'disallow_ranking';
    case RECEIVE_OFF_HOUR_LEADS        = 'receive_off_hour_leads';
    case APPOINTMENTS_ACTIVE           = 'appointments_active';
    case MI_APPOINTMENTS_ACTIVE        = 'mi_appointments_active';
    case REQUIRE_APPOINTMENTS_CALENDAR = 'require_appointments_calendar';
    case MISSED_PRODUCTS_ACTIVE        = 'missed_products_active';
    case REVIEWS_ENABLED               = 'reviews_enabled';
    case FIELD_CAMPAIGN_ALERT_ENABLED  = 'campaign_alert_enabled';
    case ACCEPT_UNDER_REVIEW_LEADS     = 'accept_under_review_leads';

    /**
     * @return array
     */
    public function getRequiredPermissions(): array
    {
        return match ($this) {
            self::ACCEPT_UNDER_REVIEW_LEADS => [
                PermissionType::COMPANY_MANAGE_UNDER_REVIEW_LEADS->value
            ],
            default                         => []
        };
    }

    /**
     * @return string
     */
    public function getDescription(): string
    {
        return match ($this) {
            self::ALLOW_LEADS_NO_CC             => 'Allow Leads No CC',
            self::ENABLE_TCPA_PLAYBACK          => 'Enable TCPA Playback',
            self::NEVER_EXCEED_BUDGET           => 'Never Exceed Budget',
            self::DISALLOW_RANKING              => 'Disallow Ranking',
            self::RECEIVE_OFF_HOUR_LEADS        => 'Receive Off Hour Leads',
            self::APPOINTMENTS_ACTIVE           => 'Appointments Active',
            self::MI_APPOINTMENTS_ACTIVE        => 'MI Appointments Active',
            self::REQUIRE_APPOINTMENTS_CALENDAR => 'Require Appointments Calendar',
            self::MISSED_PRODUCTS_ACTIVE        => 'Missed Products Active',
            self::REVIEWS_ENABLED               => 'Reviews Enabled',
            self::FIELD_CAMPAIGN_ALERT_ENABLED  => 'Campaign Alert Enabled',
            self::ACCEPT_UNDER_REVIEW_LEADS     => 'Accept Under Review Leads',
            default                             => $this->value
        };
    }
}
