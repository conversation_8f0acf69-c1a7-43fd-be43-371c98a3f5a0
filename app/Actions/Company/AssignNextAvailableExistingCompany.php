<?php

namespace App\Actions\Company;

use App\Enums\CompanySalesStatus;
use App\Models\Odin\Company;
use App\Services\Prospects\BusinessDevelopmentManagerAssignmentService;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class AssignNextAvailableExistingCompany
{
    public function run(array $timezones = []): ?Company
    {
        $undersold = $this->getUndersold();

        $company = Company::query()
            ->distinct()
            ->select('companies.*')
            ->join('company_locations', 'company_locations.company_id', 'companies.id')
            ->join('addresses', 'addresses.id', 'company_locations.address_id')
            ->whereNotIn('sales_status', CompanySalesStatus::disqualifyingStatuses())
            ->doesntHave('accountManager')
            ->doesntHave('businessDevelopmentManager')
            ->doesntHave('salesDevelopmentRepresentative')
            ->when($timezones, fn ($query) => $query->whereHas('primaryLocation.address.usZipCode', fn ($query) => $query->whereIn('time_zone', $timezones)))
            ->where(
                fn ($query) => $undersold->each(
                    fn ($record) => $query->orWhere(
                        fn ($query) => $query->whereHas('industries', fn (Builder $query) => $query->whereName($record->industry))
                            ->where('addresses.county_location_id', $record->county_id)
                    )
                )
            )->first();

        if ($company) {
            BusinessDevelopmentManagerAssignmentService::assignToCompany(auth()->user()->id, $company->id);
        }

        return $company;
    }

    private function getUndersold()
    {
        $undersold = DB::table('consumer_products as cp')
            ->selectRaw('cp.id as cpid, count(pa.id) as all_legs, count(case when !pa.chargeable or !pa.delivered then pa.id end) as legs, count(case when pa.chargeable and pa.delivered then pa.id end) as sold, avg(case when pa.chargeable and pa.delivered then 1 else 0 end) as avg_legs')
            ->leftJoin('product_assignments as pa', 'cp.id', 'pa.consumer_product_id')
            ->whereRaw('cp.created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)')
            ->where('cp.good_to_sell', true)
            ->where('cp.service_product_id', '<>', 1)
            ->groupBy('cp.id')
            ->having('avg_legs', '<', 1.0);

        return DB::table('consumer_products as cp')
            ->selectRaw('a.county_location_id as county_id, a.state, a.county, i.name as industry, count(cpid) as unsold_leads, sum(undersold.legs) as unsold_legs, sum(undersold.sold) as sold_legs, avg(undersold.avg_legs) as avg_sold')
            ->joinSub($undersold, 'undersold', 'cp.id', 'undersold.cpid')
            ->join('addresses as a', 'a.id', 'cp.address_id')
            ->join('service_products as sp', 'sp.id', 'cp.service_product_id')
            ->join('industry_services as is2', 'is2.id', 'sp.industry_service_id')
            ->join('industries as i', 'i.id', 'is2.industry_id')
            ->whereRaw('cp.created_at > DATE_SUB(CURDATE(), INTERVAL 30 DAY)')
            ->where('cp.good_to_sell', true)
            ->where('cp.service_product_id', '<>', 1)
            ->whereNotNull('a.county_location_id')
            ->groupBy('i.name', 'a.county_location_id')
            ->having('unsold_leads', '>=', 10)
            ->orderByRaw('avg_sold asc, unsold_leads desc, unsold_legs asc')
            ->limit(10)
            ->get('county_id, industry');
    }
}
