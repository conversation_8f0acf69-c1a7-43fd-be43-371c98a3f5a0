<?php

namespace App\Http\Resources\CompanyCampaign;

use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Odin\PropertyType;
use App\Repositories\LocationRepository;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin CompanyCampaign
 */
class BaseCompanyCampaignSummaryResource extends JsonResource
{
    const string PAYLOAD_STATE_COUNT          = 'state_count';
    const string PAYLOAD_COUNTY_COUNT         = 'county_count';
    const string PAYLOAD_ZIP_CODE_COUNT       = 'zip_code_count';
    const string PAYLOAD_ZIP_CODE_COUNT_TOTAL = 'zip_code_count_total';
    const string PAYLOAD_STATUS_DISPLAY       = 'status_display';
    const string PAYLOAD_ACTIVE               = 'active';
    const string PAYLOAD_AVG_DAILY_SPEND      = 'average_daily_spend';
    const string PAYLOAD_AVG_DAILY_WON        = 'average_daily_won';
    const string PAYLOAD_AVG_DAILY_AVAILABLE  = 'average_daily_available';
    const string PAYLOAD_INDUSTRY             = 'industry';
    const string PAYLOAD_BUDGETS              = 'budgets';
    const string PAYLOAD_BUDGET_TYPE_DISPLAY  = 'type_display';
    const string PAYLOAD_PROPERTY_TYPES       = 'property_types';
    const string PAYLOAD_STATISTICS           = 'statistics';
    const string PAYLOAD_LEAD_LAST_SOLD_AT    = 'lead_last_sold_at';
    const string PAYLOAD_AVERAGE_LEAD_COST    = 'average_lead_cost';

    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        $locationCounts = $this->getLocationCounts();
        $service = $this->service;

        return [
            CompanyCampaign::FIELD_ID                          => $this->id,
            CompanyCampaign::FIELD_NAME                        => $this->name,
            CompanyCampaign::FIELD_REFERENCE                   => $this->reference,
            CompanyCampaign::FIELD_STATUS                      => $this->status,
            CompanyCampaign::FIELD_ZIP_CODE_TARGETED           => $this->zip_code_targeted,
            CompanyCampaign::FIELD_USES_CUSTOM_FLOOR_PRICES    => $this->uses_custom_floor_prices,
            self::PAYLOAD_STATUS_DISPLAY                       => $this->status->getDisplayName(),
            self::PAYLOAD_ACTIVE                               => $this->status === CampaignStatus::ACTIVE,
            self::PAYLOAD_STATE_COUNT                          => $locationCounts[self::PAYLOAD_STATE_COUNT],
            self::PAYLOAD_COUNTY_COUNT                         => $locationCounts[self::PAYLOAD_COUNTY_COUNT],
            self::PAYLOAD_ZIP_CODE_COUNT                       => $locationCounts[self::PAYLOAD_ZIP_CODE_COUNT],
            self::PAYLOAD_AVG_DAILY_SPEND                      => $this->getAvgDailySpend(),
            self::PAYLOAD_AVG_DAILY_WON                        => $this->getAvgDailyLeadWon(),
            self::PAYLOAD_AVG_DAILY_AVAILABLE                  => $this->getAvgDailyAvailable(),
            CompanyCampaign::RELATION_PRODUCT                  => $this->product->name,
            CompanyCampaign::RELATION_SERVICE                  => $service->name,
            self::PAYLOAD_INDUSTRY                             => $service->industry->name,
            self::PAYLOAD_PROPERTY_TYPES                       => $this->campaignPropertyTypes()->pluck(PropertyType::FIELD_NAME),
            self::PAYLOAD_BUDGETS                              => $this->getCampaignBudget(),
            self::PAYLOAD_STATISTICS                           => $this->getStatistics(),
            CompanyCampaign::FIELD_BIDDING_DISABLED            => $this->bidding_disabled,
            CompanyCampaign::FIELD_EXCLUDED_FROM_AD_AUTOMATION => $this->excluded_from_ad_automation,
        ];
    }

    /**
     * @return array
     */
    protected function getCampaignBudget(): array
    {
        return $this->budgetContainer->budgets
            ->map(fn(Budget $budget) => [
                Budget::FIELD_TYPE                => $budget->type,
                self::PAYLOAD_BUDGET_TYPE_DISPLAY => $budget->type->getDisplayName(),
                Budget::FIELD_KEY                 => $budget->key,
                Budget::FIELD_DISPLAY_NAME        => $budget->display_name,
                Budget::FIELD_VALUE               => $budget->value,
                Budget::FIELD_STATUS              => $budget->status,
            ])->toArray();
    }

    /**
     * @return array
     */
    private function getLocationCounts(): array
    {
        $locationIdCount = $this->locationModule?->locations()->count();
        $statesAndCounties = $this->locationModule->getStateAndCountyLocationIds();

        return [
            self::PAYLOAD_STATE_COUNT          => count($statesAndCounties[LocationRepository::STATE_LOCATION_IDS]),
            self::PAYLOAD_COUNTY_COUNT         => count($statesAndCounties[LocationRepository::COUNTY_LOCATION_IDS]),
            self::PAYLOAD_ZIP_CODE_COUNT       => $locationIdCount,
        ];
    }

    /**
     * @return string
     */
    protected function getAvgDailySpend(): string
    {
        /** @var Budget|null $verifiedBudget */
        $verifiedBudget = $this->budgetContainer->budgets->where(Budget::FIELD_PRODUCT_CONFIGURATION, BudgetProductConfigurationEnum::LEAD_VERIFIED)->first();
        $budget = BudgetType::NO_LIMIT->getDisplayName();
        $spend = "$" . number_format(($this->getStatistics()['cost'] ?? 0) / $this->getCalculationOfDays(), 2);

        if ($verifiedBudget) {
            if ($verifiedBudget[Budget::FIELD_TYPE] === BudgetType::TYPE_DAILY_UNITS) {
                $budget = "{$verifiedBudget->value} Leads";
                $spend = ($this->getStatistics()['purchased'] ?? 0) / $this->getCalculationOfDays();
            }
            else if ($verifiedBudget[Budget::FIELD_TYPE] === BudgetType::TYPE_DAILY_SPEND)
                $budget = '$' . $verifiedBudget->value;
        }


        return "$spend /  $budget";
    }

    /**
     * @return float
     */
    protected function getAvgDailyLeadWon(): float
    {
        return round(($this->getStatistics()['purchased'] ?? 0) / $this->getCalculationOfDays(), 2);
    }

    /**
     * @return float
     */
    protected function getAvgDailyAvailable(): float
    {
        return round(($this->getStatistics()['available'] ?? 0) / $this->getCalculationOfDays(), 2);
    }

    /**
     * @return int
     */
    protected function getCalculationOfDays(): int
    {
        $days = $this->getStatistics()['days_of_stats'] ?? 0;

        return $days > 0 ? $days : 1;
    }

    /**
     * @return array
     */
    protected function getStatistics(): array
    {
        return $this->{self::PAYLOAD_STATISTICS} ?? [];
    }
}
