<?php

namespace App\Http\Resources\Dashboard\v4;

use App\Enums\Campaigns\PauseReason;
use App\Enums\Timezone;
use App\Http\Resources\AlertResource;
use App\Http\Resources\CompanyCampaign\BaseCompanyCampaignSummaryResource;
use App\Models\Alert;
use App\Models\Campaigns\CampaignReactivation;
use App\Models\Campaigns\CompanyCampaign;
use App\Services\OutreachCadence\TimeZoneHelperService;
use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use JsonSerializable;

/**
 * @mixin CompanyCampaign
 */
class DashboardCompanyCampaignSummaryResource extends BaseCompanyCampaignSummaryResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        return array_merge(parent::toArray($request), [
            ...$this->getReactivation()
        ], [
            'campaign_alerts' => $this->alerts->map(fn(Alert $alert) => new AlertResource($alert))
        ]);
    }

    /**
     * @return array
     */
    private function getReactivation(): array
    {
        $output = [];
        $reactivation = $this->reactivation;

        if ($reactivation) {
            if ($reactivation->reactivate_at)
                $output[CampaignReactivation::FIELD_REACTIVATE_AT] = $this->getOffsetTime();
            if ($reactivation->reason)
                $output[CampaignReactivation::FIELD_REASON] = PauseReason::tryFrom($this->reactivation->reason)?->getDisplayName() ?? $this->reactivation->reason ?? null;
        }

        return $output;
    }

    /**
     * Returns a human readable timezone from the company's timezone (default to MST)
     * to reduce confusion about when a campaign reactivates
     * Fallback to unix MS and let the browser handle it
     *
     * @return string|int
     */
    private function getOffsetTime(): string|int
    {
        $timezoneService = app(TimeZoneHelperService::class);
        $timezone = $timezoneService->getCompanyTimezone($this->company);

        return $timezone
            ? $this->reactivation->reactivate_at->timezone($timezone->value)->format('D, M d, H:ia ')  . $timezone->getDisplayName() . " Time"
            : $this->reactivation->reactivate_at->getTimestampMs();
    }
}
