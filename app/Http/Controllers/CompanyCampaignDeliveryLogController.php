<?php

namespace App\Http\Controllers;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\CompanyCampaignDeliveryLog\ListDeliveryLogsRequest;
use App\Http\Resources\CompanyCampaignDeliveryLog\CompanyCampaignDeliveryLogResource;
use App\Repositories\CompanyCampaignDeliveryLogRepository;
use App\Services\CompanyCampaignDeliveryLogService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;

class CompanyCampaignDeliveryLogController extends APIController
{
    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $apiResponseFactory
     * @param CompanyCampaignDeliveryLogService $deliveryLogService
     */
    public function __construct(
        Request                                     $request,
        JsonAPIResponseFactory                      $apiResponseFactory,
        protected CompanyCampaignDeliveryLogService $deliveryLogService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param ListDeliveryLogsRequest $request
     * @return AnonymousResourceCollection
     */
    public function listDeliveryLogs(ListDeliveryLogsRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();

        $deliveryLogs = $this->deliveryLogService->listDeliveryLogs(
            perPage             : Arr::get($validated, ListDeliveryLogsRequest::REQUEST_PER_PAGE, 10),
            page                : Arr::get($validated, ListDeliveryLogsRequest::REQUEST_PAGE, 1),
            companyId           : Arr::get($validated, ListDeliveryLogsRequest::REQUEST_COMPANY_ID),
            succeeded           : Arr::get($validated, ListDeliveryLogsRequest::REQUEST_DELIVERY_STATUS),
            campaign            : Arr::get($validated, ListDeliveryLogsRequest::REQUEST_CAMPAIGN),
            consumerProductId   : Arr::get($validated, ListDeliveryLogsRequest::REQUEST_CONSUMER_PRODUCT_ID),
            dateRange           : Arr::get($validated, key: ListDeliveryLogsRequest::REQUEST_DATE)
        );

        return CompanyCampaignDeliveryLogResource::collection($deliveryLogs);
    }

    /**
     * @param ListDeliveryLogsRequest $request
     * @param CompanyCampaignDeliveryLogRepository $deliveryLogRepository
     *
     * @return AnonymousResourceCollection
     */
    public function exportDeliveryLogs(ListDeliveryLogsRequest $request, CompanyCampaignDeliveryLogRepository $deliveryLogRepository): AnonymousResourceCollection
    {
        $validated = $request->validated();

        $deliveryLogs = $deliveryLogRepository->listAllDeliveryLogs(
            companyId: Arr::get($validated, ListDeliveryLogsRequest::REQUEST_COMPANY_ID),
            succeeded: Arr::get($validated, ListDeliveryLogsRequest::REQUEST_DELIVERY_STATUS),
            campaign : Arr::get($validated, ListDeliveryLogsRequest::REQUEST_CAMPAIGN),
        );

        return CompanyCampaignDeliveryLogResource::collection($deliveryLogs);
    }
}
