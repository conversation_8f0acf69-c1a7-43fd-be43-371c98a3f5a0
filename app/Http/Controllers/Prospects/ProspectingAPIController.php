<?php

namespace App\Http\Controllers\Prospects;

use App\Actions\Company\AssignNextAvailableExistingCompany;
use App\Enums\Prospects\ProspectResolution;
use App\Enums\Prospects\ProspectSource;
use App\Enums\Prospects\ProspectStatus;
use App\Enums\QueueSortable\QueueName;
use App\Http\Controllers\Controller;
use App\Http\Requests\Prospects\FlagAsDuplicateRequest;
use App\Http\Requests\Prospects\UpdateProspectRequest;
use App\Http\Resources\MissedProducts\MissedProductResource;
use App\Jobs\Prospects\CreateCloserDemoJob;
use App\Models\Legacy\Location;
use App\Models\MissedProducts\MissedProduct;
use App\Models\MissedProducts\OpportunityNotification;
use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\User;
use App\Repositories\MissedProductRepository;
use App\Services\MissedProducts\MissedProductDeliveryService;
use App\Services\OpportunityNotifications\OpportunityNotificationService;
use App\Services\Prospects\ProspectConversionService;
use App\Services\Prospects\ProspectDuplicateMatchingService;
use App\Services\Prospects\ProspectHuntingService;
use App\Services\Prospects\BusinessDevelopmentManagerAssignmentService;
use App\Transformers\Prospects\ProspectedCompanyTransformer;
use App\Transformers\Prospects\ProspectTransformer;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Sentry;
use Symfony\Component\Translation\Exception\NotFoundResourceException;
use Tests\Feature\Builders\Odin\CompanyCommunicationsBuilderTest;
use App\Builders\Odin\CompanyCommunicationsBuilder;

class ProspectingAPIController extends Controller
{
    const string REQUEST_CONTENT       = 'content';
    const string REQUEST_SUBJECT       = 'subject';
    const string REQUEST_TEMPLATE_NAME = 'name';
    const string REQUEST_TEMPLATE_ID   = 'templateId';

    public function getProspect(?NewBuyerProspect $newBuyerProspect): JsonResponse
    {
        if ($newBuyerProspect?->user_id === auth()->user()->id) {
            return response()->json(ProspectTransformer::transform($newBuyerProspect));
        }
        return response()->json();
    }

    public function getNextAvailableProspect(Request $request): JsonResponse|Response
    {
        $validated = $request->validate(['timezones' => 'nullable|array']);
        $timezones = $validated['timezones'] ?? [];

        /** @var User $user */
        $user = auth()->user();

        try {
            $prospect = ProspectHuntingService::getNextAvailableProspect($user, $timezones);
        } catch (Exception $e) {
            Sentry::captureException($e);

            return response($e->getMessage(), 500);
        }

        if ($prospect === null)
        {
            return response ("Prospect not found", 404);
        }

        return response()->json(['prospect' => ProspectTransformer::transform($prospect)]);
    }

    public function getNextAvailableExistingCompany(Request $request): JsonResponse
    {
        $validated = $request->validate(['timezones' => 'nullable|array']);
        $timezones = $validated['timezones'] ?? [];

        $company = app(AssignNextAvailableExistingCompany::class)->run($timezones);

        return response()->json([
            'company_id'   => $company?->id,
            'profile_link' => $company?->getAdminProfileUrl(),
            'company_name' => $company?->name,
        ]);
    }

    public function getNextAvailableRegistration(): JsonResponse
    {
        /** @var User $user */
        $user = auth()->user();

        if ($user->cant('work-newly-registered-prospects'))
            return response()->json(['prospect' => null]);

        try {
            $prospect = ProspectHuntingService::getNextAvailableRegistrationProspect($user);
        } catch (Exception $e) {
            $prospect = null;
        }

        return response()->json(['prospect' => ProspectTransformer::transform($prospect)]);
    }

    public function getStateOptions(): JsonResponse
    {
        return response()->json(Location::query()->where(Location::TYPE, Location::TYPE_STATE)->get()->map(fn(Location $state) => [
            'id'   => $state->state_abbr,
            'name' => $state->state,
        ])->toArray());
    }

    public function getCityOptions(string $stateAbbr): JsonResponse
    {
        return response()->json(
            array_values(Location::query()
                ->where(Location::TYPE, Location::TYPE_CITY)
                ->where(Location::STATE_ABBREVIATION, $stateAbbr)
                ->orderBy(Location::CITY)
                ->get()
                ->unique(Location::CITY_KEY)
                ->map(fn(Location $state) => [
                    'id'   => $state->city_key,
                    'name' => $state->city,
                ])->toArray())
        );
    }

    public function updateProspect(NewBuyerProspect $prospect, UpdateProspectRequest $request): JsonResponse
    {
        $prospect->update($request->validated());
        return response()->json(['prospect' => ProspectTransformer::transform($prospect->fresh())]);
    }

    public function convertToCompany(NewBuyerProspect $prospect, UpdateProspectRequest $request): JsonResponse
    {
        $demoBooked = false;
        $demo       = $request->validated('demo');
        if ($demo) {
            $demoBooked = $demo['booked'];
            CreateCloserDemoJob::dispatch($demo['user_id'], $demo['calendly_event_url'], $prospect)->delay(now()->addMinute());
        }

        $prospect->update($request->except('demo'));

        $company = null;
        if (ProspectConversionService::canConvertToCompany($prospect)) {
            $company = ProspectConversionService::convertToCompany($prospect, $demoBooked ? ProspectResolution::DEMO_BOOKED : ProspectResolution::DECISION_MAKER_IDENTIFIED);

            /** @var User $user */
            $user = auth()->user();
            BusinessDevelopmentManagerAssignmentService::assignToCompany($user->id, $company->id);
        }

        return response()->json([
            'company_name' => $company?->name,
            'profile_link' => $company?->getAdminProfileUrl(),
        ]);
    }

    public function archiveProspect(NewBuyerProspect $prospect): JsonResponse
    {
        $prospect->resolve(ProspectStatus::CLOSED, ProspectResolution::CANCELLED);

        return response()->json();
    }

    public function releaseBackToQueue(NewBuyerProspect $prospect): JsonResponse
    {
        $prospect->update([
            NewBuyerProspect::FIELD_STATUS              => ProspectStatus::INITIAL,
            NewBuyerProspect::FIELD_USER_ID             => 0,
            NewBuyerProspect::FIELD_RELEASED_BY_USER_ID => auth()->user()->id,
            NewBuyerProspect::FIELD_RELEASED_AT         => now(),
        ]);

        return response()->json();
    }

    public function releaseCompanyBackToQueue(Company $company): JsonResponse
    {
        /** @var User $user */
        $user = auth()->user();
        $bdm  = $company->businessDevelopmentManager;
        if ($bdm?->id === $user->id) {
            $company->unassignBusinessDevelopmentManager();
            $company->releasedBackToQueue(QueueName::EXISTING_COMPANIES, $user);
        }

        return response()->json();
    }

    public function getMyProspects(): JsonResponse
    {
        /** @var User $user */
        $user = auth()->user();

        $prospects = ProspectHuntingService::getProspectsByUserId($user);

        return response()->json(['prospects' => ProspectTransformer::transformMany($prospects)]);
    }

    public function getMyConvertedCompanies(CompanyCommunicationsBuilder $communications): JsonResponse
    {
        /** @var User $user */
        $user = auth()->user();

        $companies = $user->assignedCompanies()
            ->with([Company::RELATION_LATEST_PROSPECT])
            ->groupBy(Company::FIELD_ID)
            ->get();

        return response()->json(['companies' => ProspectedCompanyTransformer::transformMany($companies, $user)]);
    }

    public function flagAsDuplicate(FlagAsDuplicateRequest $request): JsonResponse
    {
        $companyId = $request->validated('duplicate_company_id');
        ProspectDuplicateMatchingService::flagAsDuplicate(
            $request->validated('prospect_external_reference'),
            $companyId,
        );

        /** @var User $user */
        $user = auth()->user();
        BusinessDevelopmentManagerAssignmentService::assignToCompany($user->id, $companyId, true);

        return response()->json();
    }

    public function getCloserForDemo(): JsonResponse
    {
        // todo: round robin?

        /** @var User $user */
        $user = auth()->user();
        return response()->json([
            'user_id'     => $user->id,
            'meeting_url' => $user->meeting_url,
        ]);
    }

    /**
     * @param MissedProductRepository $missedProductRepository
     * @return JsonResponse
     */
    public function scanMissedProducts(MissedProductRepository $missedProductRepository): JsonResponse
    {
        $config = $this->getBDMQueueConfig();

        $responseData = [
            'config' => [
                OpportunityNotificationConfig::FIELD_MAXIMUM_SEND_FREQUENCY => $config->maximum_send_frequency,
                OpportunityNotificationConfig::FIELD_LEAD_THRESHOLD         => $config->lead_threshold,
                OpportunityNotificationConfig::FIELD_DAYS_TO_QUERY          => $config->days_to_query
            ],
            'companies' => [],
        ];
        $fromDate = now()->subDays($config->days_to_query);

        auth()->user()->businessDevelopmentManagerCompanies()
            ->each(function(Company $company) use ($missedProductRepository, &$responseData, $fromDate) {
                $count = $missedProductRepository->getMissedProductsForNewCompany($company, $fromDate)
                    ->count();
                $responseData['companies'][$company->id] = ['missed_product_count' => $count];
            });

        OpportunityNotification::query()
            ->selectRaw(OpportunityNotification::FIELD_COMPANY_ID . ',MAX(' . OpportunityNotification::FIELD_SENT_AT . ') AS sent_at')
            ->whereIn(OpportunityNotification::FIELD_COMPANY_ID, array_keys($responseData['companies']))
            ->groupBy(OpportunityNotification::FIELD_COMPANY_ID)
            ->get()
            ->each(function(OpportunityNotification $opportunityNotification) use (&$responseData, $config) {
                $responseData['companies'][$opportunityNotification->company_id]['last_sent_at'] = $opportunityNotification->sent_at->timestamp * 1000;
                $responseData['companies'][$opportunityNotification->company_id]['can_send_at'] = max(0, $opportunityNotification->sent_at->addDays($config->maximum_send_frequency)->timestamp * 1000);
            });

        return response()->json([
            'missed_products' => $responseData,
        ]);
    }

    /**
     * @param Request $request
     * @param OpportunityNotificationService $opportunityNotificationService
     * @return JsonResponse
     */
    public function sendMissedProductsNotification(Request $request, OpportunityNotificationService $opportunityNotificationService): JsonResponse
    {
        $company = Company::query()
            ->findOrFail($request->route('companyId'));
        $config = $this->getBDMQueueConfig();
        $recipients = $request->get('company_user_ids', []);

        $canBeNotified = $opportunityNotificationService->companyCanBeNotified($company, $config);

        if (count($recipients) && $canBeNotified) {
            //TODO: remove test flag
            $success = $opportunityNotificationService->sendNotification($recipients, $company->id, $config->id, true);
            $message = $success
                ? 'There was an error sending the email to ' . implode($recipients)
                : null;
        } else {
            $message = !$canBeNotified
                ? 'This company has already received a missed opportunity email in the configured time period'
                : 'Could not find valid recipients for an opportunity notification email';
            $success = false;
        }

        return response()->json([
            'status'  => $success,
            'message' => $message,
        ]);
    }

    /**
     * @param Request $request
     * @param MissedProductRepository $missedProductRepository
     * @return JsonResponse
     */
    public function getAvailableMissedProductPreviews(Request $request, MissedProductRepository $missedProductRepository): JsonResponse
    {
        $company = Company::query()
            ->findOrFail($request->route('companyId'));

        $config             = $this->getBDMQueueConfig();
        $canReceiveProducts = $this->companyCanReceivePromoProducts($company, $config);

        if ($canReceiveProducts) {
            $from = now()->subDays($config->days_to_query);
            $products = $missedProductRepository->getMissedProductsForNewCompany($company, $from)
                ->orderBy(ConsumerProduct::TABLE .'.'. ConsumerProduct::CREATED_AT, 'desc')
                ->limit($config->maximum_promo_products + 10)
                ->get();

            $message = !$products->count()
                ? "No missed products are available for this company at this time."
                : null;
        } else {
            $products = collect();
            $message  = "This company has already received the maximum allocation of free missed products.";
        }

        return response()->json([
            'missed_products' => MissedProductResource::collection($products),
            'max_selectable'  => $config->maximum_promo_products,
            'message'         => $message,
        ]);
    }

    /**
     * @param Request $request
     * @param MissedProductDeliveryService $missedProductDeliveryService
     * @return JsonResponse
     */
    public function deliverMissedProducts(Request $request, MissedProductDeliveryService $missedProductDeliveryService): JsonResponse
    {
        $company = Company::query()
            ->findOrFail($request->route('companyId'));
        $config  = $this->getBDMQueueConfig();

        $consumerProductIds = $request->get('consumer_product_ids', []);
        $consumerProducts   = ConsumerProduct::query()
            ->select(ConsumerProduct::TABLE . '.*')
            ->join(MissedProduct::TABLE, MissedProduct::TABLE . '.' . MissedProduct::FIELD_CONSUMER_PRODUCT_ID, '=', ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID)
            ->whereIn(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID, $consumerProductIds)
            ->limit($config->maximum_promo_products)
            ->get();
        $emails             = $company->users()->pluck(CompanyUser::FIELD_EMAIL)->toArray();

        $delivered = $missedProductDeliveryService->deliverPromotionalLeadsByEmail(
            company: $company,
            consumerProducts: $consumerProducts,
            email: $emails[0],
            notificationConfigId: $config->id,
        );

        return response()->json([
            'status'    => count($delivered) > 0,
            'delivered' => $delivered,
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getAvailableEmailRecipients(Request $request): JsonResponse
    {
        $company = Company::query()
            ->findOrFail($request->route('companyId'));
        /** @var OpportunityNotificationService $service */
        $service = app(OpportunityNotificationService::class);

        return response()->json([
            'status'        => true,
            'company_users' => $service->getAvailableEmailRecipients($company),
        ]);
    }

    /**
     * @return OpportunityNotificationConfig
     */
    private function getBDMQueueConfig(): OpportunityNotificationConfig
    {
        $config = OpportunityNotificationConfig::getBDMQueueConfig();
        if (!$config)
            throw new NotFoundResourceException("Configuration not found for BDM queue notifications.");

        return $config;
    }

    /**
     * @param Company $company
     * @param OpportunityNotificationConfig $config
     * @return bool
     */
    private function companyCanReceivePromoProducts(Company $company, OpportunityNotificationConfig $config): bool
    {
        $promoCount = $company
            ->productAssignments()
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE, false)
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true)
            ->count();

        return $promoCount < $config->maximum_promo_products;
    }

    /**
     * @return JsonResponse
     */
    public function getVerifiedCompanyCount(): JsonResponse
    {
        /** @var User $user */
        $user = auth()->user();
        return response()->json(['count' => $user->businessDevelopmentManagerCompanies()->count()]);
    }

    /**
     * @return JsonResponse
     */
    public function getMostRecentRegistrations(): JsonResponse
    {
        /** @var User $user */
        $user      = auth()->user();
        $companies = $user->businessDevelopmentManagerCompanies()
            ->whereHas(Company::RELATION_NEW_BUYER_PROSPECTS, function ($query) {
                $query->where(NewBuyerProspect::FIELD_SOURCE, ProspectSource::REGISTRATION);
            })->orderByDesc(Company::FIELD_ID)->limit(10)->get();
        return response()->json(['companies' => ProspectedCompanyTransformer::transformMany($companies, $user)]);
    }
}
