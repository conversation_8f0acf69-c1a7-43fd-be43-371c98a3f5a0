<?php

namespace App\Http\Controllers;

use App\Builders\Odin\CompanyCampaignBuilder;
use App\Campaigns\Delivery\CRM\Enums\CRMType;
use App\Campaigns\Delivery\CRM\Enums\WebformPrefill;
use App\Campaigns\Modules\DashboardModule;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Campaigns\CampaignType;
use App\Enums\Campaigns\CRMFieldReplacerKey;
use App\Enums\Campaigns\CustomCampaignBudgetType;
use App\Enums\LoweredFloorPricePolicy;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\PropertyType as PropertyTypeEnum;
use App\Enums\PermissionType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Controllers\DashboardAPI\V4\StatisticsController;
use App\Http\Requests\CompanyCampaigns\SaveCompanyCampaignRequest;
use App\Http\Requests\CompanyCampaigns\StoreCompanyCRMTemplateRequest;
use App\Http\Requests\Dashboard\DashboardPriceRequest;
use App\Http\Requests\Odin\CompanyCampaignSearchRequest;
use App\Http\Requests\Odin\v2\BaseOdinRequest;
use App\Http\Requests\StoreCompanyUserRequest;
use App\Http\Resources\CompanyCampaign\AdminCompanyCampaignSummaryResource;
use App\Http\Resources\Dashboard\CompanyUserResource;
use App\Http\Resources\Dashboard\v4\CompanyCampaignCRMDelivererResource;
use App\Http\Resources\Dashboard\v4\CompanyCRMTemplateResource;
use App\Mediators\CampaignMediator;
use App\Models\Campaigns\CampaignReactivation;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\BaseCompanyCampaignModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\CompanyOptInName;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryConfiguration;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\PropertyType;
use App\Models\Odin\ServiceProduct;
use App\Models\User;
use App\Registries\CampaignRegistry;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Repositories\Odin\Campaigns\CustomCampaignFloorPriceRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\CompanyUserRepository;
use App\Repositories\Odin\IndustryRepository;
use App\Services\Campaigns\CampaignHelpService;
use App\Services\Campaigns\CampaignPricingService;
use App\Services\Campaigns\CompanyCampaignService;
use App\Services\Campaigns\CRMIntegrations\CRMDelivererService;
use App\Services\Companies\ZipCodeExceptionService;
use App\Services\CompanyCRMTemplateService;
use App\Services\IndustryServicesService;
use App\Services\Odin\ProductStatistics\ProductStatisticsService;
use App\Services\UserAuthorizationService;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Illuminate\Validation\UnauthorizedException;
use App\Enums\Odin\Industry as IndustryEnum;
use Illuminate\Validation\ValidationException;

class CompanyCampaignWizardController extends APIController
{
    const string RESPONSE_STATUS                 = 'status';
    const string RESPONSE_COMPANY_USERS          = 'company_users';
    const string RESPONSE_COMPANY_USER           = 'company_user';
    const string RESPONSE_CAMPAIGN               = 'campaign';
    const string RESPONSE_CAMPAIGNS              = 'campaigns';
    const string RESPONSE_WIZARD                 = 'wizard';
    const string RESPONSE_CONFIGURATIONS         = 'configurations';
    const string RESPONSE_PRICES                 = 'prices';
    const string RESPONSE_INDUSTRY_SERVICES      = 'industry_services';
    const string RESPONSE_STATUS_CONFIG          = 'status_config';
    const string RESPONSE_CRM_CONFIGURATIONS     = 'crm_configurations';
    const string RESPONSE_CRM_TEMPLATES          = 'crm_templates';
    const string RESPONSE_CRM_DELIVERER          = 'crm_deliverer';
    const string RESPONSE_CRM_DELIVERERS         = 'crm_deliverers';
    const string RESPONSE_CRM_SHORTCODES         = 'crm_shortcodes';
    const string RESPONSE_CRM_PREFILLS           = 'crm_prefills';
    const string RESPONSE_STATISTICS             = 'statistics';
    const string RESPONSE_MESSAGE                = 'message';
    const string RESPONSE_ZIP_CODE_EXCEPTIONS    = 'zip_code_exceptions';
    const string RESPONSE_FLOOR_PRICE_INDUSTRIES = 'floor_price_industry_ids';
    const string RESPONSE_CUSTOM_FLOOR_PRICES    = 'custom_floor_prices';
    const string RESPONSE_UNRESTRICTED_ZIP_CODES = 'unrestricted_zip_code_targeting';
    const string RESPONSE_REPLACER_INSTRUCTIONS  = 'replacer_instructions';
    const string RESPONSE_REPLACER_EXAMPLES      = 'replacer_examples';
    const string RESPONSE_CUSTOM_BUDGET_TYPES    = 'custom_budget_types';
    const string RESPONSE_CAMPAIGN_CONFIGURATION = 'campaign_configuration';
    const string RESPONSE_COMPANY_OPT_IN_NAMES   = 'company_opt_in_names';

    const string REQUEST_PAYLOAD            = 'payload';
    const string REQUEST_PRODUCT            = 'product';
    const string REQUEST_INDUSTRY           = 'industry';
    const string REQUEST_SERVICE            = 'service';
    const string REQUEST_ZIP_CODES          = 'zip_codes';
    const string REQUEST_COMPANY_USER_ID    = 'company_user_id';
    const string REQUEST_PROPERTY_TYPE      = 'property_type';
    const string REQUEST_PROPERTY_TYPES     = 'property_types';
    const string REQUEST_PAGE               = 'page';
    const string REQUEST_PER_PAGE           = 'per_page';
    const string REQUEST_STATUS             = 'status';
    const string REQUEST_CRM_TYPE_ID        = 'crm_type_id';
    const string REQUEST_METHOD_NAME        = 'method_name';
    const string REQUEST_CRM_DELIVERER_ID   = 'crm_deliverer_id';
    const string REQUEST_COMPANY_ID         = 'company_id';
    const string REQUEST_STATE_LOCATION_ID  = 'state_location_id';
    const string REQUEST_COUNTY_LOCATION_ID = 'county_location_id';
    const string REQUEST_TEMPLATE_ID        = 'template_id';
    const string REQUEST_IS_TEMPLATE        = 'is_template';
    const string REQUEST_ALL_CAMPAIGNS_LIST = 'all_campaigns_list';

    public function __construct(
        Request                             $request,
        JsonAPIResponseFactory              $apiResponseFactory,
        protected CompanyCampaignRepository $companyCampaignRepository,
        protected CompanyRepository         $companyRepository,
        protected CompanyUserRepository     $companyUserRepository,
        protected CampaignRegistry          $campaignRegistry,
        protected CampaignMediator          $campaignMediator,
        protected CompanyCampaignService    $campaignService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param CompanyCampaignSearchRequest $searchRequest
     * @param ZipCodeExceptionService $zipCodeExceptionService
     * @return JsonResponse
     */
    public function getCampaignSummaryList(CompanyCampaignSearchRequest $searchRequest, ZipCodeExceptionService $zipCodeExceptionService): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($this->request->route('companyId'));
        $getFullCampaignSummary = $searchRequest->get(self::REQUEST_ALL_CAMPAIGNS_LIST, false);
        $validated = $searchRequest->validated();

        $statusFilter = intval($validated[CompanyCampaign::FIELD_STATUS] ?? null);

        $statusMap = [
            -1  => [CampaignStatus::ACTIVE, CampaignStatus::PAUSED_TEMPORARILY, CampaignStatus::PAUSED_PERMANENTLY], // All Statuses
            1   => [CampaignStatus::PAUSED_TEMPORARILY], // Paused - temporarily paused
            0   => [CampaignStatus::PAUSED_PERMANENTLY], // Off - permanently paused
            2   => [CampaignStatus::ACTIVE], // Active - currently active
        ];

        $statuses = $statusMap[$statusFilter] ?? $statusMap[-1]; // Default to All statuses if not matched

        $industry = IndustryEnum::tryFrom($validated[BaseOdinRequest::REQUEST_INDUSTRY] ?? null);
        $product = ProductEnum::tryFrom($validated[BaseOdinRequest::REQUEST_PRODUCT] ?? null);

        $campaignQuery = CompanyCampaignBuilder::query()
            ->forCompanyId($company->id)
            ->forStatuses($statuses ?? null)
            ->forIndustries($industry ? [$industry] : null)
            ->forProducts($product ? [$product] : null);

        $allCampaignsList = $getFullCampaignSummary
            ? $campaignQuery->getQuery()
                ->select([CompanyCampaign::FIELD_REFERENCE, CompanyCampaign::FIELD_NAME])
                ->get()
                ->toArray()
            : [];

        $campaignList = $campaignQuery
            ->eagerLoadRelations([
                CompanyCampaign::RELATION_DELIVERY_MODULE . '.' . BaseCompanyCampaignModule::RELATION_CAMPAIGN,
                CompanyCampaign::RELATION_DELIVERY_MODULE . '.' . CompanyCampaignDeliveryModule::RELATION_CRMS,
                CompanyCampaign::RELATION_CAMPAIGN_DATA,
                CompanyCampaign::RELATION_REACTIVATION,
                CompanyCampaign::RELATION_LOCATION_MODULE,
            ])
            ->getPaginatedQuery($validated[BaseOdinRequest::REQUEST_PAGE] ?? null, $validated[BaseOdinRequest::REQUEST_PER_PAGE] ?? null);

        return $this->formatResponse([
            self::RESPONSE_STATUS                 => !!$campaignList,
            self::RESPONSE_CAMPAIGNS              => AdminCompanyCampaignSummaryResource::collection($campaignList)->resource,
            self::RESPONSE_ZIP_CODE_EXCEPTIONS    => $zipCodeExceptionService->getCompanyExceptionsKeyedByState($company->id),
            self::RESPONSE_UNRESTRICTED_ZIP_CODES => $company->configuration?->unrestricted_zip_code_targeting ?? false,
            self::REQUEST_ALL_CAMPAIGNS_LIST      => $allCampaignsList,
        ]);
    }

    /**
     * @param CompanyCampaignService $companyCampaignService
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getProductConfigurations(CompanyCampaignService $companyCampaignService): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($this->request->route('companyId'));
        $config = $this->companyCampaignRepository->getProductConfigData($company);
        $statusConfig = $companyCampaignService->getCampaignStatusConfig();

        return $this->formatResponse([
            self::RESPONSE_STATUS         => !!$config,
            self::RESPONSE_CONFIGURATIONS => $config,
            self::RESPONSE_STATUS_CONFIG  => $statusConfig,
        ]);
    }

    /**
     * @param CampaignPricingService $pricingService
     * @return JsonResponse
     */
    public function getPriceRangeForZipCodes(CampaignPricingService $pricingService): JsonResponse
    {
        $zipCodes = $this->request->get(self::REQUEST_ZIP_CODES, []);
        $propertyTypes = $this->request->get(self::REQUEST_PROPERTY_TYPES, [PropertyTypeEnum::RESIDENTIAL->value]);

        $propertyTypeIds = PropertyType::query()
            ->whereIn(PropertyType::FIELD_NAME, $propertyTypes)
            ->pluck(PropertyType::FIELD_ID)
            ->toArray();

        $serviceSlug = $this->request->get(self::REQUEST_SERVICE);
        $product = ProductEnum::fromLowerCase($this->request->get(self::REQUEST_PRODUCT));

        $serviceId = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, $serviceSlug)
            ->firstOrFail()
            ->id;
        $productId = Product::query()
            ->where(Product::FIELD_NAME, $product->value)
            ->firstOrFail()
            ->id;

        $serviceProductId = ServiceProduct::query()
            ->where(ServiceProduct::FIELD_PRODUCT_ID, $productId)
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $serviceId)
            ->firstOrFail()
            ->id;

        $groupByQualityTier = $product === ProductEnum::APPOINTMENT;

        $prices = $pricingService->getPriceRangeForZipCodes($serviceProductId, $propertyTypeIds, $zipCodes, $groupByQualityTier);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$prices,
            self::RESPONSE_PRICES   => $prices
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getCompanyUsers(): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($this->request->route('companyId'));

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$company,
            self::RESPONSE_COMPANY_USERS    => CompanyUserResource::collection($company->users),
        ]);
    }

    /**
     * @param StoreCompanyUserRequest $request
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function updateCompanyUser(StoreCompanyUserRequest $request): JsonResponse
    {
        $this->verifyUserCanModifyCompanyData(PermissionType::COMPANY);

        $company = $this->companyRepository->findOrFail($this->request->route('companyId'));
        $user = $this->companyUserRepository->findCompanyUserByIdOrFail($request[CompanyUser::FIELD_ID]);
        $validated = $request->safe()->toArray();

        $success = $validated[CompanyUser::USER_IS_CONTACT]
            ? $this->companyUserRepository->updateCompanyContact($company->reference, $user, $validated, true)
            : $this->companyUserRepository->updateCompanyUser($company->reference, $user, $validated, true);

        return $this->formatResponse([
            self::RESPONSE_STATUS       => !!$success,
            self::RESPONSE_COMPANY_USER => $success,
        ]);
    }

    /**
     * @param StoreCompanyUserRequest $request
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function createCompanyUser(StoreCompanyUserRequest $request): JsonResponse
    {
        $this->verifyUserCanModifyCompanyData(PermissionType::COMPANY);

        $company = $this->companyRepository->findOrFail($this->request->route('companyId'));
        $validated = $request->safe()->toArray();

        $success = $validated[CompanyUser::USER_IS_CONTACT]
            ? $this->companyUserRepository->createCompanyContact($company, $validated, true)
            : $this->companyUserRepository->createCompanyUser($company->id, $validated);

        return $this->formatResponse([
            self::RESPONSE_STATUS       => !!$success,
            self::RESPONSE_COMPANY_USER => $success,
        ]);
    }

    /**
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function deleteCompanyUser(): JsonResponse
    {
        $this->verifyUserCanModifyCompanyData(PermissionType::COMPANY);

        $company = $this->companyRepository->findOrFail($this->request->route('companyId'));
        $user = $this->companyUserRepository->findCompanyUserByIdOrFail($this->request->get(self::REQUEST_COMPANY_USER_ID));

        $success = $this->companyUserRepository->deleteCompanyUser($company->reference, $user);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $success
        ]);
    }

    /**
     * @param IndustryServicesService $industryServicesService
     * @return JsonResponse
     */
    public function getIndustryServiceProducts(IndustryServicesService $industryServicesService, IndustryRepository $industryRepository): JsonResponse
    {
        $futureIndustries = Industry::query()
            ->whereHas(Industry::RELATION_INDUSTRY_CONFIGURATION, fn(Builder $query) =>
                $query->where(IndustryConfiguration::FIELD_FUTURE_CAMPAIGNS_ACTIVE, true)
            )->pluck(Industry::FIELD_ID);

        $companyId = $this->request->get(self::REQUEST_COMPANY_ID);

        if ($companyId) {
            $companyIndustries = Company::query()
                ->find($companyId)
                ?->industries()
                ->pluck(Industry::TABLE .'.'. Industry::FIELD_ID)
                ->toArray()
                ?? [];

            if ($companyIndustries) {
                $futureIndustries = $futureIndustries->filter(fn(int $id) => in_array($id, $companyIndustries));
            }
        }

        $industryServiceProducts = $industryServicesService->allServicesByIndustryWithProducts(Industry::FIELD_SLUG, $futureIndustries->toArray());

        return $this->formatResponse([
            self::RESPONSE_STATUS                 => !!$industryServicesService,
            self::RESPONSE_INDUSTRY_SERVICES      => $industryServiceProducts,
            self::RESPONSE_FLOOR_PRICE_INDUSTRIES => $industryRepository->getCustomFloorPriceIndustryIds(),
            self::RESPONSE_CUSTOM_BUDGET_TYPES    => CustomCampaignBudgetType::allNamesByProduct(),
        ]);
    }

    /**
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getNewWizardConfiguration(): JsonResponse
    {
        $productKey = $this->request->get('productKey');
        $industryKey = $this->request->get('industryKey');
        $serviceKey = $this->request->get('serviceKey');
        $budgetKey = $this->request->get('customBudgetKey', '');
        $customBudget = CustomCampaignBudgetType::tryFrom($budgetKey) ?? null;
        $industry = IndustryEnum::tryFromSlug($industryKey) ?? null;
        $product = ProductEnum::fromLowerCase($productKey);

        $campaignType = $this->campaignRegistry->getCampaignMappingType($product, $industry, $serviceKey, $customBudget);
        $campaignDefinition = $this->campaignRegistry->getCampaignDefinition($campaignType);

        $config = $this->campaignMediator->getFrontEndWizardConfiguration($campaignDefinition)->values() ?? null;

        return $this->formatResponse([
            self::RESPONSE_STATUS                 => !!$config,
            self::RESPONSE_WIZARD                 => $config,
            self::RESPONSE_CAMPAIGN               => [CompanyCampaign::FIELD_TYPE => $campaignType->value],
            self::RESPONSE_CAMPAIGN_CONFIGURATION => $this->companyCampaignRepository->getConfigurationByCampaignType($campaignType),
        ]);
    }

    /**
     * @param SaveCompanyCampaignRequest $request
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function saveNewCampaign(SaveCompanyCampaignRequest $request): JsonResponse
    {
        $this->verifyUserCanModifyCompanyData(PermissionType::CAMPAIGNS_CREATE);

        $validated = $request->safe()->toArray();

        /** @var Company $company */
        $company = Company::query()
            ->findOrFail($request->route('companyId'));

        $unrestrictedZipCodes = $company->configuration?->unrestricted_zip_code_targeting ?? false;

        if (!$unrestrictedZipCodes && $validated[CompanyCampaign::FIELD_ZIP_CODE_TARGETED]) {
            /** @var ZipCodeExceptionService $zipCodeService */
            $zipCodeService = app(ZipCodeExceptionService::class);
            if (!$zipCodeService->companyHasExceptions($company->id))
                throw new Exception("This company cannot create zip code targeted campaigns.");
        }

        $campaignCreated = $this->saveCampaign($company, $validated, null);

        return $this->formatResponse([
             self::RESPONSE_STATUS  => !!$campaignCreated,
        ]);
    }

    /**
     * @param SaveCompanyCampaignRequest $request
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function updateCampaign(SaveCompanyCampaignRequest $request): JsonResponse
    {
        $this->verifyUserCanModifyCompanyData(PermissionType::CAMPAIGNS_UPDATE);

        $validated = $request->safe()->toArray();
        $campaign =  $this->companyCampaignRepository->findOrFailByReference($validated[CompanyCampaign::FIELD_REFERENCE]);

        /** @var Company $company */
        $company = Company::query()
            ->findOrFail($request->route('companyId'));

        $campaignUpdated = $this->saveCampaign($company, $validated, $campaign);

        return $this->formatResponse([
            self::RESPONSE_STATUS  => !!$campaignUpdated,
        ]);
    }

    /**
     * @param Company $company
     * @param array $payload
     * @param CompanyCampaign|null $existingCampaign
     * @return bool
     * @throws BindingResolutionException
     */
    protected function saveCampaign(Company $company, array $payload, ?CompanyCampaign $existingCampaign = null): bool
    {
        /** @var Product $product */
        $productId = $existingCampaign->product_id ?? Product::query()
            ->where(Product::FIELD_NAME, $payload[self::REQUEST_PRODUCT])
            ->firstOrFail()
            ->id;
        /** @var IndustryService $industryService */
        $industryServiceId = $existingCampaign->service_id ?? IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, $payload[self::REQUEST_SERVICE])
            ->firstOrFail()
            ->id;
        $propertyTypes = PropertyType::query()
            ->whereIn(PropertyType::FIELD_NAME, $payload[self::REQUEST_PROPERTY_TYPES])
            ->pluck(PropertyType::FIELD_ID)
            ->toArray();

        $campaign = $this->companyCampaignRepository->updateOrCreate(
            $company,
            $productId,
            $industryServiceId,
            $payload[CompanyCampaign::FIELD_NAME],
            CampaignStatus::from($payload[CompanyCampaign::FIELD_STATUS]),
            $propertyTypes,
            $existingCampaign,
            $payload[CompanyCampaign::FIELD_ZIP_CODE_TARGETED] ?? false,
            $payload[CompanyCampaign::FIELD_TYPE] ?? null,
        );

        return $this->campaignMediator->save($campaign, collect($payload[self::REQUEST_PAYLOAD]));
    }

    /**
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getCompanyCampaignForEditing(): JsonResponse
    {
        $campaign = $this->getCompanyCampaignDetail();
        $campaignType = CampaignType::tryFrom($campaign->get(CompanyCampaign::FIELD_TYPE, 0));

        $productKey = strtolower($campaign->get(self::REQUEST_PRODUCT));
        $industryKey = $campaign->get(self::REQUEST_INDUSTRY);
        $serviceKey = $campaign->get(self::REQUEST_SERVICE);

        $industryName = IndustryEnum::tryFromSlug($industryKey)?->value ?? null;

        $wizardConfiguration = $this->getWizardConfiguration($productKey, $industryName, $serviceKey);

        return $this->formatResponse([
            self::RESPONSE_STATUS                 => !empty($campaign),
            self::RESPONSE_CAMPAIGN               => $campaign->toArray(),
            self::RESPONSE_WIZARD                 => $wizardConfiguration,
            self::RESPONSE_CAMPAIGN_CONFIGURATION => $this->companyCampaignRepository->getConfigurationByCampaignType($campaignType)
        ]);
    }

    /**
     * Get config data for valid Campaign statuses & pause reasons
     * @param CompanyCampaignService $campaignService
     * @return JsonResponse
     */
    public function getCampaignStatusChangeConfig(CompanyCampaignService $campaignService): JsonResponse
    {
        $config = $campaignService->getCampaignStatusConfig();

        return $this->formatResponse([
            self::RESPONSE_STATUS        => !!$config,
            self::RESPONSE_STATUS_CONFIG => $config
        ]);
    }

    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function pauseCampaign(): JsonResponse
    {
        $this->verifyUserCanModifyCompanyData(PermissionType::CAMPAIGNS_UPDATE);
        $campaign = $this->companyCampaignRepository->findOrFailByReference($this->request->get(CompanyCampaign::FIELD_REFERENCE, null));
        $oldStatus = $campaign->status;
        $validated = $this->request->validate([
            CompanyCampaign::FIELD_STATUS             => ['numeric', 'required'],
            CampaignReactivation::FIELD_REASON        => ['string', 'required'],
            CampaignReactivation::FIELD_REACTIVATE_AT => ['date', 'nullable'],
        ]);
        $newStatus = CampaignStatus::from($validated[CompanyCampaign::FIELD_STATUS]);

        if ($newStatus === CampaignStatus::PAUSED_TEMPORARILY) {
            $reactivateAt = new Carbon($validated[CampaignReactivation::FIELD_REACTIVATE_AT] ?? null);

            if (!$reactivateAt || $reactivateAt < now()) {
                throw new Exception("A valid Reactivation date must be supplied for a temporary pause.");
            }

            $reactivateAt->startOfDay();
        }

        $statusChanged = $this->campaignService->pauseCampaign($campaign, $newStatus, $oldStatus, $validated[CampaignReactivation::FIELD_REASON], $reactivateAt ?? null);

        return $this->formatResponse([
            self::RESPONSE_STATUS   =>  $statusChanged,
        ]);
    }

    /**
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function unpauseCampaign(): JsonResponse
    {
        $this->verifyUserCanModifyCompanyData(PermissionType::CAMPAIGNS_UPDATE);
        $campaign = $this->companyCampaignRepository->findOrFailByReference($this->request->get(CompanyCampaign::FIELD_REFERENCE, null));
        $oldStatus = $campaign->status;

        $statusChanged = $this->campaignService->unpauseCampaign($campaign, $oldStatus);

        return $this->formatResponse([
            self::RESPONSE_STATUS => $statusChanged,
        ]);
    }

    /**
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function deleteCampaign(): JsonResponse
    {
        $this->verifyUserCanModifyCompanyData(PermissionType::CAMPAIGNS_DELETE);
        $campaign = $this->companyCampaignRepository->findOrFailByReference($this->request->get(CompanyCampaign::FIELD_REFERENCE, null));

        $deleted = $this->campaignService->deleteCampaign($campaign);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $deleted
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function toggleCampaignBiddingStatus(): JsonResponse
    {
        if (!Auth::user()?->hasPermissionTo(PermissionType::CAMPAIGNS_ENABLE_DISABLE_BIDDING->value)) {
            throw new UnauthorizedException('The user does not have permission to enable/disable bidding.');
        }

        $campaign = $this->companyCampaignRepository->findOrFailByReference($this->request->route('campaignReference'));

        $campaign->bidding_disabled = !$campaign->bidding_disabled;

        return $this->formatResponse([
            self::RESPONSE_STATUS => $campaign->save()
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function toggleAdAutomationExclusion(): JsonResponse
    {
        if (!Auth::user()?->hasPermissionTo(PermissionType::CAMPAIGNS_EXCLUDE_FROM_AD_AUTOMATION->value)) {
            throw new UnauthorizedException('The user does not have permission to update ad exclusion.');
        }

        $this->validate($this->request, [
            'status' => 'required|boolean'
        ]);

        return $this->formatResponse([
            'status' => $this->companyCampaignRepository->findOrFailByReference($this->request->route('campaignReference'))->update([
                CompanyCampaign::FIELD_EXCLUDED_FROM_AD_AUTOMATION => $this->request->get('status')
            ])
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function downloadCampaignZipCodes(): JsonResponse
    {
        $campaign = $this->companyCampaignRepository->findOrFailByReference($this->request->get(CompanyCampaign::FIELD_REFERENCE));
        $zipData = $campaign->locationModule?->locations()->select(CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID, CompanyCampaignLocationModuleLocation::FIELD_ZIP_CODE)->get()
            ->map(fn(CompanyCampaignLocationModuleLocation $location) => $location->location_id . "," . $location->zip_code) ?? collect();
        $csv = "ID,ZIP CODE\n" . $zipData->join("\n");

        return $this->formatResponse([
            self::RESPONSE_STATUS   => true,
            self::REQUEST_ZIP_CODES => $csv,
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getCRMConfigurations(): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($this->request->route('companyId'));
        $configurations = CRMType::getAllFieldConfigurations();
        $templates = $company->crmTemplates;
        $prefills = WebformPrefill::getAllPrefillData();
        ksort($prefills);

        return $this->formatResponse([
            self::RESPONSE_STATUS             => !!$configurations,
            self::RESPONSE_CRM_CONFIGURATIONS => $configurations,
            self::RESPONSE_CRM_TEMPLATES      => CompanyCRMTemplateResource::collection($templates),
            self::RESPONSE_CRM_SHORTCODES     => CRMFieldReplacerKey::getAllKeysAndNames(),
            self::RESPONSE_CRM_PREFILLS       => $prefills,
        ]);
    }

    /**
     * @param CRMDelivererService $CRMDelivererService
     * @return JsonResponse
     * @throws Exception
     */
    public function executeCrmMethod(CRMDelivererService $CRMDelivererService): JsonResponse
    {
        $methodName = $this->request->get(self::REQUEST_METHOD_NAME);
        $delivererId = $this->request->get(self::REQUEST_CRM_DELIVERER_ID);
        $payload = $this->request->get(self::REQUEST_PAYLOAD, []);
        $crmType = CRMType::tryFrom($this->request->get(self::REQUEST_CRM_TYPE_ID));
        $isTemplate = $this->request->get(self::REQUEST_IS_TEMPLATE, false);

        if ((!$crmType && !$delivererId) || !$methodName)
            throw new Exception("Bad CRM Interactable call - no CRM type or missing method.");

        $result = $CRMDelivererService->executeInteractableMethod($methodName, $payload, $crmType, $delivererId, $isTemplate);

        return $this->formatResponse([
            self::RESPONSE_STATUS           => !!$result,
            self::RESPONSE_CRM_DELIVERER    => $result,
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getCrmImportOptions(): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($this->request->route('companyId'));
        $campaignReference = $this->request->get(CompanyCampaign::FIELD_REFERENCE, '');

        $crmOptions = CompanyCampaignDeliveryModuleCRM::query()
            ->join(CompanyCampaignDeliveryModule::TABLE, CompanyCampaignDeliveryModule::TABLE .'.'. CompanyCampaignDeliveryModule::FIELD_ID, '=', CompanyCampaignDeliveryModuleCRM::FIELD_MODULE_ID)
            ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, '=', CompanyCampaignDeliveryModule::FIELD_CAMPAIGN_ID)
            ->where(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_COMPANY_ID, $company->id)
            ->where(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_REFERENCE, '!=', $campaignReference)
            ->whereNull(CompanyCampaignDeliveryModuleCRM::FIELD_TEMPLATE_ID)
            ->whereNull(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_DELETED_AT)
            ->select([CompanyCampaignDeliveryModuleCRM::TABLE . '.*', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_NAME . ' as campaign_name'])
            ->get();

        return $this->formatResponse([
            self::RESPONSE_STATUS         => true,
            self::RESPONSE_CRM_DELIVERERS => CompanyCampaignCRMDelivererResource::collection($crmOptions),
        ]);
    }

    /**
     * @param StoreCompanyCRMTemplateRequest $request
     * @param CompanyCRMTemplateService $templateService
     * @param CompanyCampaignRepository $companyCampaignRepository
     * @return JsonResponse
     */
    public function saveCrmTemplate(StoreCompanyCRMTemplateRequest $request, CompanyCRMTemplateService $templateService, CompanyCampaignRepository $companyCampaignRepository): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($this->request->route('companyId'));
        $templatePayload = $request->safe()->toArray();
        $templatePayload[CompanyCRMTemplate::FIELD_COMPANY_ID] = $company->id;
        $syncCampaigns = $templatePayload[StoreCompanyCRMTemplateRequest::PAYLOAD_SYNC_CAMPAIGNS] ?? false;

        $success = $templateService->saveCompanyCRMTemplate($templatePayload, $syncCampaigns);
        $templates = $success
            ? $company->crmTemplates
            : collect();

        return $this->formatResponse([
            self::RESPONSE_STATUS        => $success,
            self::RESPONSE_CRM_TEMPLATES => CompanyCRMTemplateResource::collection($templates),
        ]);
    }

    /**
     * @param CompanyCRMTemplateService $templateService
     * @return JsonResponse
     */
    public function deleteCrmTemplate(CompanyCRMTemplateService $templateService): JsonResponse
    {
        $company = $this->companyRepository->findOrFail($this->request->route('companyId'));
        $templateId = $this->request->get(self::REQUEST_TEMPLATE_ID);
        /** @var CompanyCRMTemplate $template */
        $template = CompanyCRMTemplate::query()->findOrFail($templateId);
        $success = $templateService->deleteCompanyCRMTemplate($template);
        $templates = $success
            ? $company->crmTemplates
            : collect();

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $success,
            self::RESPONSE_CRM_TEMPLATES => CompanyCRMTemplateResource::collection($templates),
        ]);
    }

    /**
     * @param CampaignHelpService $campaignHelpService
     * @return JsonResponse
     */
    public function getReplacerFieldDocumentation(CampaignHelpService $campaignHelpService): JsonResponse
    {
        $examples = $campaignHelpService->getCrmFieldReplacerExamples();
        $instructions = $campaignHelpService->getFieldReplacerInstructions();

        return $this->formatResponse([
            self::RESPONSE_STATUS                => !!$examples,
            self::RESPONSE_REPLACER_INSTRUCTIONS => $instructions,
            self::RESPONSE_REPLACER_EXAMPLES     => $examples
        ]);
    }

    /**
     * @param ProductStatisticsService $statisticsService
     * @return JsonResponse
     */
    public function getBiddingLocationStatistics(ProductStatisticsService $statisticsService): JsonResponse
    {
        /** @var Company $company */
        $company = Company::query()->findOrFail($this->request->get(self::REQUEST_COMPANY_ID));
        /** @var IndustryService $service */
        $service = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, $this->request->get(self::REQUEST_SERVICE))
            ->firstOrFail();
        $product = Product::query()
            ->where(Product::FIELD_NAME, $this->request->get(self::REQUEST_PRODUCT))
            ->firstOrFail();
        /** @var ServiceProduct $serviceProduct */
        $serviceProduct = ServiceProduct::query()
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $service->id)
            ->where(ServiceProduct::FIELD_PRODUCT_ID, $product->id)
            ->firstOrFail();

        $validated = $this->request->validate([
            self::REQUEST_COUNTY_LOCATION_ID => ['numeric', 'nullable'],
            self::REQUEST_STATE_LOCATION_ID  => ['numeric', Rule::requiredIf(fn() => $this->request->get(self::REQUEST_COUNTY_LOCATION_ID) === null)]
        ]);
        $stateLocationId  = $validated[self::REQUEST_STATE_LOCATION_ID] ?? null;
        $countyLocationId = $validated[self::REQUEST_COUNTY_LOCATION_ID] ?? null;

        $stats = $statisticsService->getBiddingStatisticsByLocation(
            companyId: $company->id,
            serviceProductId: $serviceProduct->id,
            stateLocationId: $stateLocationId,
            countyLocationId: $countyLocationId,
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS     => !!$stats,
            self::RESPONSE_STATISTICS => $stats,
        ]);
    }

    /**
     * @param ZipCodeExceptionService $zipCodeExceptionService
     * @return JsonResponse
     */
    public function validateTargetedZipCodes(ZipCodeExceptionService $zipCodeExceptionService): JsonResponse
    {
        $this->request->validate([ self::REQUEST_ZIP_CODES => ['array'] ]);
        $zipCodeIds = $this->request->get(self::REQUEST_ZIP_CODES);
        $companyId = $this->request->route('companyId');
        $validatedZipCodes = $zipCodeExceptionService->validateZipCodeLocationIdsForCompany($companyId, $zipCodeIds);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => !!$validatedZipCodes,
            self::REQUEST_ZIP_CODES => $validatedZipCodes,
        ]);
    }

    /**
     * @param CustomCampaignFloorPriceRepository $floorPriceRepository
     * @return JsonResponse
     */
    public function getCustomCampaignFloorPrices(CustomCampaignFloorPriceRepository $floorPriceRepository): JsonResponse
    {
        $floorPrices = $floorPriceRepository->getCustomFloorPricesByCampaignReference($this->request->route('campaignReference'));

        return $this->formatResponse([
             self::RESPONSE_STATUS  => !!$floorPrices,
             self::RESPONSE_CUSTOM_FLOOR_PRICES => $floorPrices,
        ]);
    }

    /**
     * @param string $productKey
     * @param string $industryKey
     * @param string $serviceKey
     * @return ?Collection
     * @throws BindingResolutionException
     */
    protected function getWizardConfiguration(string $productKey, string $industryKey, string $serviceKey): ?Collection
    {
        $product = ProductEnum::fromLowerCase($productKey);
        $industry = IndustryEnum::tryFromSlug($industryKey);

        $campaignDefinition = $this->campaignRegistry->getCampaignMapping($product, $industry, $serviceKey);

        return $this->campaignMediator->getFrontEndWizardConfiguration($campaignDefinition)->values() ?? null;
    }

    /**
     * @return Collection
     * @throws BindingResolutionException
     */
    protected function getCompanyCampaignDetail(): Collection
    {
        $transformModule = app()->make(DashboardModule::class);

        $campaignReference = $this->request->route('campaignReference', null);
        $campaign = $this->companyCampaignRepository->findOrFailByReference($campaignReference);
        $transformedCampaign = $transformModule->transform($campaign);
        $moduleKeys = $transformedCampaign->get('modules')->values()->toArray();

        return $transformedCampaign->mergeRecursive(
            $this->campaignMediator->transformSingle(
                $campaign,
                $moduleKeys,
            )
        );
    }

    /**
     * @param PermissionType|null $permissionType
     * @return void
     * @throws BindingResolutionException
     */
    protected function verifyUserCanModifyCompanyData(?PermissionType $permissionType = null): void
    {
        /** @var User $user */
        $user = Auth::user();
        $company = $this->companyRepository->findOrFail($this->request->route('companyId'));
        $authorizationService = app()->make(UserAuthorizationService::class);

        $authorized = $authorizationService->canUpdateCompanySalesData($company)
            || ($permissionType && $user->hasPermissionTo($permissionType->value));

        if (!$authorized)
            throw new UnauthorizedException("The user does not have permission to modify this Company's campaigns.");
    }

    /**
     * @return JsonResponse
     */
    protected function getCustomPricingBidModificationOptions(): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            'options'             => LoweredFloorPricePolicy::getCustomFloorPricePolicyLabels(),
            'tooltips'            => LoweredFloorPricePolicy::getCustomFloorPricePolicyDescriptions(),
        ]);
    }
}
