<?php

namespace App\Http\Requests\Billing\BillingProfile;

use App\Enums\Billing\PaymentMethodServices;
use App\Enums\PermissionType;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class CreateBillingProfilesRequest extends FormRequest
{
    const string FIELD_COMPANY_ID          = 'company_id';
    const string FIELD_THRESHOLD           = 'threshold';
    const string FIELD_CHARGE_ATTEMPTS     = 'charge_attempts';
    const string FIELD_CAMPAIGNS           = 'campaigns';
    const string FIELD_FREQUENCY_TYPE      = 'frequency_type';
    const string FIELD_FREQUENCY_DATA      = 'frequency_data';
    const string FIELD_PROCESS_AUTO        = 'process_auto';
    const string FIELD_BILLING_CONTACT_ID  = 'billing_contact_id';
    const string FIELD_DEFAULT             = 'default';
    const string FIELD_PAYMENT_METHOD_ID   = 'payment_method_id';
    const string FIELD_PAYMENT_METHOD      = 'payment_method';
    const string FIELD_DUE_IN_DAYS         = 'due_in_days';
    const string FIELD_INVOICE_TEMPLATE_ID = 'invoice_template_id';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_PROFILES_CREATE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule>
     */
    public function rules(): array
    {
        return [
            self::FIELD_COMPANY_ID          => ['required', 'int', 'exists:' . Company::TABLE . ',' . Company::FIELD_ID],
            self::FIELD_THRESHOLD           => ['nullable', 'numeric'],
            self::FIELD_CHARGE_ATTEMPTS     => ['nullable', 'numeric'],
            self::FIELD_CAMPAIGNS           => ['array'],
            self::FIELD_BILLING_CONTACT_ID  => ['numeric'],
            self::FIELD_FREQUENCY_DATA      => ['array'],
            self::FIELD_FREQUENCY_TYPE      => ['string'],
            self::FIELD_PROCESS_AUTO        => ['bool'],
            self::FIELD_DEFAULT             => ['bool'],
            self::FIELD_PAYMENT_METHOD_ID   => ['nullable', 'numeric', 'min:1'],
            self::FIELD_PAYMENT_METHOD      => ['required', Rule::in([
                PaymentMethodServices::MANUAL->value,
                PaymentMethodServices::STRIPE->value
            ])],
            self::FIELD_DUE_IN_DAYS         => ['numeric'],
            self::FIELD_INVOICE_TEMPLATE_ID => ['sometimes', 'numeric'],
        ];
    }

    public function messages(): array
    {
        return [
            self::FIELD_FREQUENCY_DATA . '.' . 'data.day' => 'Invoice day frequency must be between 1 and 100 days.'
        ];
    }
}
