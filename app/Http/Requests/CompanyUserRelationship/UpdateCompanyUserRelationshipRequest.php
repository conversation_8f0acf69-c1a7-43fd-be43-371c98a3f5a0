<?php

namespace App\Http\Requests\CompanyUserRelationship;

use App\Enums\PermissionType;
use App\Models\CompanyUserRelationship;
use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateCompanyUserRelationshipRequest extends FormRequest
{
    const string COMMISSIONABLE = 'commissionable';
    const string DELETED_AT     = 'deleted_at';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::COMPANY_USER_RELATIONSHIPS_EDIT->value);
    }


    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            self::COMMISSIONABLE => ['nullable', 'date'],
            self::DELETED_AT     => ['nullable', 'date'],
        ];
    }
}
