# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Laravel Specific
/node_modules

/public/hot
/public/storage
/public/js
/public/css
/public/vendor
/public/build

/storage/*.key
/vendor
.env
.env.backup
.env.testing
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
/.idea
/.vscode

#redis
/data

#google
/google

#coverage
/coverage

.cache
!.env.example
.env.*

phpunit.xml

# laravel debugbar
storage/debugbar

# laravel ide helper
_ide_helper.php
.phpstorm.meta.php
_ide_helper_models.php
