{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "repositories": {"pipedrive-client": {"type": "vcs", "url": "**************:Solar-Investments/pipedrive-php-client.git"}, "privacy-request-processing-package": {"type": "vcs", "url": "**************:Solar-Investments/privacy-request-processing-package.git"}}, "require": {"php": "^8.3", "doctrine/dbal": "^3.3", "docusign/esign-client": "^8.0", "facebook/php-business-sdk": "^20.0", "firebase/php-jwt": "^6.3", "google/apiclient": "^2.15", "google/cloud-error-reporting": "^0.19.6", "google/cloud-firestore": "^1.33", "google/cloud-logging": "^1.25", "google/cloud-pubsub": "^1.37", "google/cloud-storage": "^1.27", "googleads/google-ads-php": "^27.2.0", "grpc/grpc": "^1.38", "guzzlehttp/guzzle": "^7.4.4", "heaps-good-services/privacy-request-processing": "dev-main", "laravel-workflow/laravel-workflow": "^1.0", "laravel/framework": "^11.0", "laravel/horizon": "^5.23", "laravel/sanctum": "^4.0", "laravel/slack-notification-channel": "^3.3", "laravel/tinker": "^2.7", "league/csv": "*", "mailchimp/marketing": "^3.0", "mailslurp/mailslurp-client-php": "^15.17", "microsoft/bingads": "^13.0", "mtdowling/cron-expression": "*", "nesbot/carbon": "^2.72", "phpseclib/phpseclib": "3.0.37", "pragmarx/google2fa": "^8.0", "predis/predis": "^2.0", "pusher/pusher-php-server": "^7.0", "sentry/sentry-laravel": "^4.3", "simplesoftwareio/simple-qrcode": "~4", "socketlabs/email-delivery": "^1.4", "solar-investments/support": "^2.0", "solarreviews/pipedrive-client": "^6.3.2", "spatie/laravel-activitylog": "^4.8", "spatie/laravel-event-sourcing": "7.10.2", "spatie/laravel-model-states": "^2.7", "spatie/laravel-pdf": "^1.5", "spatie/laravel-permission": "^6.7", "spatie/laravel-tags": "^4.6", "spatie/laravel-url-signer": "^3.0", "stripe/stripe-php": "^v14.9.0", "twilio/sdk": "^6.37"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "barryvdh/laravel-ide-helper": "^3.5", "fakerphp/faker": "^1.9.1", "laravel/breeze": "^2.0", "laravel/pint": "^1.22", "laravel/sail": "^1.14", "laravel/telescope": "^5.0", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.1.3", "spatie/laravel-ignition": "^2.0", "thedoctor0/laravel-factory-generator": "^1.2"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"ide-helper": ["@php artisan ide-helper:generate", "@php artisan ide-helper:models -RW --write-mixin", "@php artisan ide-helper:meta"], "lint": ["./vendor/bin/pint"], "lint-dirty": ["./vendor/bin/pint --dirty"], "lint-frontend": ["npm run format"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan ide-helper:generate", "@php artisan ide-helper:meta"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "refresh-databases-and-seed": ["@php artisan migrate:fresh", "@php artisan migrate:fresh --database=readonly --path=readonly", "@php artisan db:seed"]}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true, "pestphp/pest-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}