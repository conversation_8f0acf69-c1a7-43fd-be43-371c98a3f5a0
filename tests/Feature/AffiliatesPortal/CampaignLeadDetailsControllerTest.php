<?php

namespace Tests\Feature\AffiliatesPortal;

use App\Helpers\CarbonHelper;
use App\Helpers\NumberHelper;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Models\Affiliates\Payout;
use App\Models\Affiliates\PayoutStrategy;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use Carbon\CarbonImmutable;
use Database\Seeders\ProductsSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CampaignLeadDetailsControllerTest extends TestCase
{
    use RefreshDatabase;

    private Affiliate $affiliate;

    private PayoutStrategy $payoutStrategy;

    protected function setUp(): void
    {
        parent::setUp();

        $this->affiliate = Affiliate::factory()->create();
        $this->payoutStrategy = PayoutStrategy::factory()
            ->for($this->affiliate, PayoutStrategy::RELATION_AFFILIATE)
            ->create();

        $this->seed(ProductsSeeder::class);

        $this->withToken(config('services.affiliates_portal_api.token'));
    }

    #[Test]
    public function get_campaign_lead_details_successfully(): void
    {
        $now = CarbonImmutable::now('UTC');

        $campaign = Campaign::factory()
            ->for($this->affiliate, Campaign::RELATION_AFFILIATE)
            ->create();

        $records = ConsumerProductAffiliateRecord::factory()
            ->for($this->affiliate, ConsumerProductAffiliateRecord::RELATION_AFFILIATE)
            ->for($campaign, ConsumerProductAffiliateRecord::RELATION_AFFILIATE_CAMPAIGN)
            ->create();

        $consumerProducts = ConsumerProduct::factory(2)
            ->has(ProductAssignment::factory(2)->chargeableAndDelivered(), ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT)
            ->for($records)
            ->create([ConsumerProduct::FIELD_CREATED_AT => $now->format('Y-m-d H:i:s')]);

        $page = 1;
        $perPage = 10;

        $start = $now->subWeek()->timestamp;
        $end = $now->timestamp;

        $res = $this
            ->getJson(route(
                'affiliates-portal.v1.affiliates.campaigns.lead-details.index',
                ['affiliate' => $this->affiliate->uuid, 'campaign' => $campaign->{Campaign::FIELD_ID}, ...compact('page', 'perPage', 'start', 'end')]
            ))
            ->assertSuccessful()
            ->assertJsonStructure([
                'meta' => [
                    'current_page',
                    'per_page',
                    'from',
                    'to',
                    'total',
                ],
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'good_to_sell',
                        'status',
                        'consumer' => [
                            'id',
                            'name',
                            'email',
                            'phone',
                            'legs_requested',
                        ],
                        'address' => [
                            'street',
                            'city',
                            'state',
                            'zip_code',
                            'county',
                        ],
                        'service_product' => [
                            'industry_service' => [
                                'id',
                                'name',
                                'industry' => [
                                    'id',
                                    'name',
                                ],
                            ],
                        ],
                        'payout',
                        'legs_sold',
                    ],
                ],
            ]);

        $this->assertEquals($res['meta']['current_page'], $page);
        $this->assertEquals($res['meta']['per_page'], $perPage);
        $this->assertEquals($res['meta']['from'], 1);
        $this->assertEquals($res['meta']['to'], 2);
        $this->assertEquals($res['meta']['total'], 2);

        $leadDetails = collect($res['data'])->keyBy('id')->toArray();

        $consumerProducts->each(function (ConsumerProduct $cp) use ($leadDetails) {
            $cp->refresh();

            $currentLeadDetails = $leadDetails[$cp->{ConsumerProduct::FIELD_ID}];

            $this->assertEquals($cp->getAffiliateStatus(), $currentLeadDetails['status']);
            $this->assertEquals($cp->{ConsumerProduct::FIELD_GOOD_TO_SELL}, $currentLeadDetails['good_to_sell']);
            $this->assertEquals($cp->created_at->timezone('MST')->format(CarbonHelper::FORMAT_BASE_TIMEZONE), $currentLeadDetails['created_at']);

            $address = $cp->{ConsumerProduct::RELATION_ADDRESS};

            $this->assertEquals($address->{Address::FIELD_ADDRESS_1}.' '.$address->{Address::FIELD_ADDRESS_2}, $currentLeadDetails['address']['street']);
            $this->assertEquals($address->{Address::FIELD_CITY}, $currentLeadDetails['address']['city']);
            $this->assertEquals($address->{Address::FIELD_STATE}, $currentLeadDetails['address']['state']);
            $this->assertEquals($address->{Address::FIELD_ZIP_CODE}, $currentLeadDetails['address']['zip_code']);

            $consumer = $cp->{ConsumerProduct::RELATION_CONSUMER};

            $this->assertEquals($consumer->getFullName(), $currentLeadDetails['consumer']['name']);
            $this->assertEquals($consumer->{Consumer::FIELD_EMAIL}, $currentLeadDetails['consumer']['email']);
            $this->assertEquals($consumer->{Consumer::FIELD_PHONE}, $currentLeadDetails['consumer']['phone']);
            $this->assertEquals($consumer->{Consumer::FIELD_MAX_CONTACT_REQUESTS}, $currentLeadDetails['consumer']['legs_requested']);

            $industryService = $cp->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE};

            $this->assertEquals($industryService->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_NAME}, $currentLeadDetails['service_product']['industry_service']['industry']['name']);
            $this->assertEquals($industryService->{IndustryService::FIELD_NAME}, $currentLeadDetails['service_product']['industry_service']['name']);

            $this->assertEquals($cp->{ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT}->count(), $currentLeadDetails['legs_sold']);
            $this->assertEquals(NumberHelper::currencyFromCents($cp->{ConsumerProduct::RELATION_AFFILIATE_PAYOUT}->{Payout::FIELD_CENT_VALUE}), $currentLeadDetails['payout']);
        });
    }

    #[Test]
    public function no_leads_returned_if_outside_time_range(): void
    {
        $now = CarbonImmutable::now('UTC');

        $campaign = Campaign::factory()
            ->for($this->affiliate, Campaign::RELATION_AFFILIATE)
            ->create();

        $records = ConsumerProductAffiliateRecord::factory()
            ->for($this->affiliate, ConsumerProductAffiliateRecord::RELATION_AFFILIATE)
            ->for($campaign, ConsumerProductAffiliateRecord::RELATION_AFFILIATE_CAMPAIGN)
            ->create();

        ConsumerProduct::factory(2)
            ->has(ProductAssignment::factory(2)->chargeableAndDelivered(), ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT)
            ->for($records)
            ->create([ConsumerProduct::FIELD_CREATED_AT => $now->format('Y-m-d H:i:s')]);

        $page = 1;
        $perPage = 10;

        $start = $now->addDay()->timestamp;
        $end = $now->addDays(2)->timestamp;

        $res = $this
            ->getJson(route(
                'affiliates-portal.v1.affiliates.campaigns.lead-details.index',
                ['affiliate' => $this->affiliate->uuid, 'campaign' => $campaign->{Campaign::FIELD_ID}, ...compact('page', 'perPage', 'start', 'end')]
            ))
            ->assertSuccessful()
            ->assertJsonStructure([
                'meta' => [
                    'current_page',
                    'per_page',
                    'from',
                    'to',
                    'total',
                ],
                'data' => [],
            ]);

        $this->assertEquals($res['meta']['current_page'], $page);
        $this->assertEquals($res['meta']['per_page'], $perPage);
        $this->assertEquals($res['meta']['from'], 0);
        $this->assertEquals($res['meta']['to'], 0);
        $this->assertEquals($res['meta']['total'], 0);
        $this->assertEmpty($res['data']);
    }

    #[Test]
    public function fail_to_get_lead_details_without_campaign(): void
    {
        $this
            ->getJson(route('affiliates-portal.v1.affiliates.campaigns.lead-details.index', ['affiliate' => $this->affiliate->uuid, 'campaign' => 0]))
            ->assertNotFound();
    }

    #[Test]
    public function fail_to_get_lead_details_without_affiliate(): void
    {
        $this
            ->getJson(route('affiliates-portal.v1.affiliates.campaigns.lead-details.index', ['affiliate' => 'fake', 'campaign' => 1]))
            ->assertNotFound();
    }
}
