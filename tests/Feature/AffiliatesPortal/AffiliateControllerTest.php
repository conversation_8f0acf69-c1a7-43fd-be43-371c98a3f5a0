<?php

namespace Tests\Feature\AffiliatesPortal;

use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Enums\PermissionType;
use App\Enums\RoleType;
use App\Http\Requests\Affiliate\BaseAffiliateRequest;
use App\Http\Requests\Affiliate\CreateAffiliateRequest;
use App\Http\Requests\Affiliate\UpdateAffiliateRequest;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\PayoutStrategy;
use App\Models\Permission;
use App\Models\User;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class AffiliateControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        Permission::factory()->create([Permission::FIELD_NAME => PermissionType::AFFILIATES->value]);
        Permission::factory()->create([Permission::FIELD_NAME => PermissionType::AFFILIATES_SHADOW->value]);
        Permission::factory()->create([Permission::FIELD_NAME => PermissionType::AFFILIATES_CREATE]);
        Permission::factory()->create([Permission::FIELD_NAME => PermissionType::AFFILIATES_UPDATE]);
        Permission::factory()->create([Permission::FIELD_NAME => PermissionType::AFFILIATES_DELETE]);

        $role = Role::findOrCreate(RoleType::AFFILIATES_ADMIN->value);
        $role->givePermissionTo(PermissionType::AFFILIATES->value);
        $role->givePermissionTo(PermissionType::AFFILIATES_SHADOW->value);
        $role->givePermissionTo(PermissionType::AFFILIATES_CREATE->value);
        $role->givePermissionTo(PermissionType::AFFILIATES_UPDATE->value);
        $role->givePermissionTo(PermissionType::AFFILIATES_DELETE->value);

        $user = User::factory()->create()->assignRole($role);

        $this->actingAs($user);
    }

    #[Test]
    public function list_affiliates_successfully(): void
    {
        $affiliates = Affiliate::factory(2)->create();

        $page = 1;
        $perPage = 10;
        $start = now()->subDays(30);
        $end = now();
        $searchName = '';
        $date_range = json_encode(['from' => $start, 'to' => $end]);

        $array = [
            'data' => [
                [
                    'id' => $affiliates->first()->id,
                    'lead_count' => 0,
                    'lead_gts_count' => 0,
                    'leg_count' => '0',
                    'name' => $affiliates->first()->name,
                    'payout' => '$0.00',
                    'revenue' => '$0.00',
                    'strategy' => null,
                    'strategy_count' => 0,
                    'uuid' => $affiliates->first()->uuid,
                ],
                [
                    'id' => $affiliates->last()->id,
                    'lead_count' => 0,
                    'lead_gts_count' => 0,
                    'leg_count' => '0',
                    'name' => $affiliates->last()->name,
                    'payout' => '$0.00',
                    'revenue' => '$0.00',
                    'strategy' => null,
                    'strategy_count' => 0,
                    'uuid' => $affiliates->last()->uuid,
                ],
            ],
            'links' => [
                'first' => config('app.url').'/internal-api/v1/affiliates-portal/affiliates?page=1',
                'last' => config('app.url').'/internal-api/v1/affiliates-portal/affiliates?page=1',
                'next' => null,
                'prev' => null,
            ],
            'meta' => [
                'current_page' => 1,
                'from' => 1,
                'last_page' => 1,
                'links' => [
                    [
                        'active' => false,
                        'label' => '&laquo; Previous',
                        'url' => null,
                    ],
                    [
                        'active' => true,
                        'label' => '1',
                        'url' => config('app.url').'/internal-api/v1/affiliates-portal/affiliates?page=1',
                    ],
                    [
                        'active' => false,
                        'label' => 'Next &raquo;',
                        'url' => null,
                    ],
                ],
                'path' => config('app.url').'/internal-api/v1/affiliates-portal/affiliates',
                'per_page' => 10,
                'to' => 2,
                'total' => 2,
            ],
        ];

        $this
            ->getJson(route('internal-api.v1.affiliates-portal.affiliates.index', compact('page', 'perPage', 'start', 'end', 'searchName', 'date_range')))
            ->assertSuccessful()
            ->assertExactJson($array);
    }

    #[Test]
    public function filter_affiliates_by_name_successfully(): void
    {
        $affiliates = Affiliate::factory(2)->create();

        $page = 1;
        $perPage = 10;
        $start = now()->subDays(30);
        $end = now();
        $date_range = json_encode(['from' => $start, 'to' => $end]);
        $name = $affiliates->last()->name;

        $array = [
            'data' => [
                [
                    'id' => $affiliates->last()->id,
                    'lead_count' => 0,
                    'lead_gts_count' => 0,
                    'leg_count' => '0',
                    'name' => $affiliates->last()->name,
                    'payout' => '$0.00',
                    'revenue' => '$0.00',
                    'strategy' => null,
                    'strategy_count' => 0,
                    'uuid' => $affiliates->last()->uuid,
                ],
            ],
            'links' => [
                'first' => config('app.url').'/internal-api/v1/affiliates-portal/affiliates?page=1',
                'last' => config('app.url').'/internal-api/v1/affiliates-portal/affiliates?page=1',
                'next' => null,
                'prev' => null,
            ],
            'meta' => [
                'current_page' => 1,
                'from' => 1,
                'last_page' => 1,
                'links' => [
                    [
                        'active' => false,
                        'label' => '&laquo; Previous',
                        'url' => null,
                    ],
                    [
                        'active' => true,
                        'label' => '1',
                        'url' => config('app.url').'/internal-api/v1/affiliates-portal/affiliates?page=1',
                    ],
                    [
                        'active' => false,
                        'label' => 'Next &raquo;',
                        'url' => null,
                    ],
                ],
                'path' => config('app.url').'/internal-api/v1/affiliates-portal/affiliates',
                'per_page' => 10,
                'to' => 1,
                'total' => 1,
            ],
        ];

        $this
            ->getJson(route('internal-api.v1.affiliates-portal.affiliates.index', compact('page', 'perPage', 'date_range', 'name')))
            ->assertSuccessful()
            ->assertExactJson($array);
    }

    #[Test]
    public function create_affiliate_successfully(): void
    {
        $strategy = PayoutStrategyTypeEnum::REVENUE_PERCENTAGE->getClass();

        $this
            ->postJson(
                route('internal-api.v1.affiliates-portal.affiliates.store'),
                [
                    CreateAffiliateRequest::REQUEST_NAME => 'New Affiliate',
                    BaseAffiliateRequest::REQUEST_PAYOUT_STRATEGY => [
                        BaseAffiliateRequest::STRATEGY_TYPE => $strategy->type()->value,
                        BaseAffiliateRequest::STRATEGY_VALUE => $strategy->toPresentationValue($strategy->defaultValue()),
                    ],
                ]
            )
            ->assertSuccessful();

        $this->assertDatabaseHas(Affiliate::TABLE, [
            Affiliate::FIELD_NAME => 'New Affiliate',
        ]);

        $this->assertDatabaseHas(PayoutStrategy::TABLE, [
            PayoutStrategy::FIELD_TYPE => $strategy->type(),
            PayoutStrategy::FIELD_VALUE => $strategy->defaultValue(),
        ]);
    }

    #[Test]
    public function fail_to_create_affiliate_without_name(): void
    {
        $strategy = PayoutStrategyTypeEnum::REVENUE_PERCENTAGE->getClass();

        $this
            ->postJson(
                route('internal-api.v1.affiliates-portal.affiliates.store'),
                [
                    CreateAffiliateRequest::REQUEST_NAME => '',
                    BaseAffiliateRequest::REQUEST_PAYOUT_STRATEGY => [
                        BaseAffiliateRequest::STRATEGY_TYPE => $strategy->type()->value,
                        BaseAffiliateRequest::STRATEGY_VALUE => $strategy->toPresentationValue($strategy->defaultValue()),
                    ],
                ]
            )
            ->assertJsonValidationErrors([
                CreateAffiliateRequest::REQUEST_NAME,
            ]);

        $this->assertDatabaseEmpty(Affiliate::TABLE);
    }

    #[Test]
    public function read_affiliate_successfully(): void
    {
        $affiliate = Affiliate::factory()->create();

        $this
            ->getJson(route('internal-api.v1.affiliates-portal.affiliates.show', ['affiliate' => $affiliate->{Affiliate::FIELD_UUID}]))
            ->assertSuccessful()
            ->assertJson([
                'data' => [
                    'id' => $affiliate->{Affiliate::FIELD_ID},
                    'name' => $affiliate->{Affiliate::FIELD_NAME},
                ],
            ], true);
    }

    #[Test]
    public function update_affiliate_successfully(): void
    {
        $affiliate = Affiliate::factory()->create([Affiliate::FIELD_NAME => 'New Affiliate']);

        /** @var PayoutStrategy $strategy */
        $strategy = PayoutStrategy::factory()->for($affiliate, PayoutStrategy::RELATION_AFFILIATE)->create();

        $this
            ->patchJson(
                route('internal-api.v1.affiliates-portal.affiliates.update', ['affiliate' => $affiliate->{Affiliate::FIELD_UUID}]),
                [
                    UpdateAffiliateRequest::REQUEST_NAME => 'Updated Affiliate',
                    UpdateAffiliateRequest::REQUEST_AFFILIATE_ID => $affiliate->{Affiliate::FIELD_ID},
                    BaseAffiliateRequest::REQUEST_PAYOUT_STRATEGY => [
                        BaseAffiliateRequest::STRATEGY_TYPE => $strategy->type->getClass()->type()->value,
                        BaseAffiliateRequest::STRATEGY_VALUE => $strategy->type->getClass()->toPresentationValue($strategy->value),
                    ],
                ]
            )
            ->assertSuccessful();

        $this->assertDatabaseHas(Affiliate::TABLE, [
            Affiliate::FIELD_NAME => 'Updated Affiliate',
        ]);

        // if nothing in payment strategy has changed, shouldn't make a new record
        $this->assertDatabaseCount(PayoutStrategy::TABLE, 1);
    }

    #[Test]
    public function fail_to_update_affiliate_without_name(): void
    {
        $affiliate = Affiliate::factory()->create([Affiliate::FIELD_NAME => 'New Affiliate']);

        /** @var PayoutStrategy $strategy */
        $strategy = PayoutStrategy::factory()->for($affiliate, PayoutStrategy::RELATION_AFFILIATE)->create();

        $this
            ->patchJson(
                route('internal-api.v1.affiliates-portal.affiliates.update', ['affiliate' => $affiliate->{Affiliate::FIELD_UUID}]),
                [
                    UpdateAffiliateRequest::REQUEST_NAME => '',
                    UpdateAffiliateRequest::REQUEST_AFFILIATE_ID => $affiliate->{Affiliate::FIELD_ID},
                    BaseAffiliateRequest::REQUEST_PAYOUT_STRATEGY => [
                        BaseAffiliateRequest::STRATEGY_TYPE => $strategy->type->getClass()->type()->value,
                        BaseAffiliateRequest::STRATEGY_VALUE => $strategy->type->getClass()->toPresentationValue($strategy->value),
                    ],
                ]
            )
            ->assertJsonValidationErrors([UpdateAffiliateRequest::REQUEST_NAME]);

        $this->assertDatabaseHas(Affiliate::TABLE, [
            Affiliate::FIELD_NAME => 'New Affiliate',
        ]);
    }

    #[Test]
    public function delete_affiliate_successfully(): void
    {
        $affiliate = Affiliate::factory()->create();

        $this
            ->deleteJson(route('internal-api.v1.affiliates-portal.affiliates.destroy', ['affiliate' => $affiliate->{Affiliate::FIELD_UUID}]))
            ->assertSuccessful();

        $this->assertSoftDeleted(Affiliate::TABLE, [
            Affiliate::FIELD_ID => $affiliate->{Affiliate::FIELD_ID},
        ]);
    }

    #[Test]
    public function fail_to_delete_affiliate_if_it_does_not_exist(): void
    {
        $this->assertDatabaseEmpty(Affiliate::TABLE);

        $this
            ->deleteJson(route('internal-api.v1.affiliates-portal.affiliates.destroy', ['affiliate' => 1]))
            ->assertNotFound();
    }

    #[Test]
    public function create_shadow_affiliate_token(): void
    {
        /** @var Affiliate $affiliate */
        $affiliate = Affiliate::factory()->create();

        $signingKey = config('dashboard.jwt.signing_key');

        $payload = [
            'affiliate_numeric_id' => $affiliate->id,
            'affiliate_uuid' => $affiliate->uuid,
            'shadow_id' => Auth()->user()->id,
        ];

        $response = $this
            ->getJson(route('internal-api.v1.affiliates-portal.affiliates.shadow-url', ['affiliate' => $affiliate->uuid]))
            ->assertStatus(200);

        $url = $response->json('data.url');

        // Parse the query parameters from the URL
        $queryParams = [];
        parse_str(parse_url($url, PHP_URL_QUERY), $queryParams);

        // Assert that a token exists
        $this->assertArrayHasKey('token', $queryParams, 'Token parameter missing');

        $token = $queryParams['token'];

        $data = (array) JWT::decode($token, new Key($signingKey, 'HS256'));

        foreach ($payload as $key => $value) {
            $this->assertEquals($value, $data[$key]);
        }
    }
}
