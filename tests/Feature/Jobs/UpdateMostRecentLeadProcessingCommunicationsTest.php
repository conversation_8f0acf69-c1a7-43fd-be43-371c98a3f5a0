<?php

namespace Tests\Feature\Jobs;

use App\Jobs\UpdateMostRecentLeadProcessingCommunications;
use App\Models\LeadProcessingCommunication;
use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class UpdateMostRecentLeadProcessingCommunicationsTest extends TestCase
{
    use RefreshDatabase;

    protected bool $seed = false;

    protected function setUp(): void
    {
        parent::setUp();

        LeadProcessingCommunication::query()->delete();

        $now = CarbonImmutable::now('UTC');

        LeadProcessingCommunication::factory(10)
            ->state(new Sequence(
                [LeadProcessingCommunication::FIELD_LEAD_ID => 1],
                [LeadProcessingCommunication::FIELD_LEAD_ID => 2]
            ))
            ->sequence(fn (Sequence $s) => [
                LeadProcessingCommunication::CREATED_AT => $now->addDays($s->index),
            ])
            ->create();
    }

    #[Test]
    public function can_set_most_recent_communications(): void
    {
        UpdateMostRecentLeadProcessingCommunications::dispatchSync();

        $this->assertEquals(2, LeadProcessingCommunication::where(LeadProcessingCommunication::FIELD_MOST_RECENT, true)->count());

        LeadProcessingCommunication::query()
            ->selectRaw(sprintf(
                'MAX(%s) AS `created_at`, %s AS `lead_id`',
                LeadProcessingCommunication::CREATED_AT,
                LeadProcessingCommunication::FIELD_LEAD_ID
            ))
            ->groupBy('lead_id')
            ->pluck('created_at', 'lead_id')
            ->map(fn ($createdAt, $leadId) => $this->assertDatabaseHas(LeadProcessingCommunication::TABLE, [
                LeadProcessingCommunication::FIELD_LEAD_ID => $leadId,
                LeadProcessingCommunication::FIELD_MOST_RECENT => 1,
                LeadProcessingCommunication::CREATED_AT => $createdAt,
            ]));
    }
}
