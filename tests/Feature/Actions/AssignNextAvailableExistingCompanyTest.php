<?php

namespace Tests\Feature\Actions;

use App\Actions\Company\AssignNextAvailableExistingCompany;
use App\Enums\Odin\Industry as OdinIndustry;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ServiceProduct;
use App\Models\User;
use App\Models\USZipCode;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use JetBrains\PhpStorm\NoReturn;
use PHPUnit\Framework\Attributes\Test;
use SolarInvestments\Testing\SkipTestWhenRunningCI;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class AssignNextAvailableExistingCompanyTest extends TestCase
{
    use RefreshDatabase, SkipTestWhenRunningCI;

    protected Industry $industry;

    protected Address $address;

    protected function setUp(): void
    {
        parent::setUp();

        Schema::connection('readonly')->disableForeignKeyConstraints();

        Location::truncate();

        Schema::connection('readonly')->enableForeignKeyConstraints();

        $this->industry = Industry::factory()->createQuietly([
            'name' => OdinIndustry::ROOFING->value,
        ]);

        $industryService = IndustryService::factory()->createQuietly([
            'industry_id' => $this->industry->id,
        ]);

        $serviceProduct = ServiceProduct::factory()->createQuietly([
            'id' => 2,
            'industry_service_id' => $industryService->id,
        ]);

        $industryTwo = Industry::factory()->createQuietly([
            'name' => OdinIndustry::SIDING->value,
        ]);

        $industryServiceTwo = IndustryService::factory()->createQuietly([
            'industry_id' => $industryTwo->id,
        ]);

        $serviceProductTwo = ServiceProduct::factory()->createQuietly([
            'id' => 3,
            'industry_service_id' => $industryServiceTwo->id,
        ]);

        $location = Location::whereType('co')->whereState('CA')->whereCounty('Los Angeles')->first()
            ?? Location::factory()->createQuietly([
                'type' => 'CO',
                'county' => 'Los Angeles',
                'state' => 'CA',
            ]);

        $this->address = Address::factory()->createQuietly([
            'state' => $location->state,
            'county' => $location->county,
            'county_location_id' => $location->id,
        ]);

        ConsumerProduct::factory(10)->createQuietly([
            'good_to_sell' => true,
            'service_product_id' => $serviceProduct->id,
            'address_id' => $this->address->id,
            'created_at' => now()->subDay(1),
        ]);

        $locationTwo = Location::whereType('co')->whereState('NJ')->whereCounty('Monmouth')->first()
            ?? Location::factory()->createQuietly([
                'type' => 'CO',
                'county' => 'Monmouth',
                'state' => 'NJ',
            ]);

        $addressTwo = Address::factory()->createQuietly([
            'state' => $locationTwo->state,
            'county' => $locationTwo->county,
            'county_location_id' => $locationTwo->id,
        ]);

        ConsumerProduct::factory(10)->createQuietly([
            'good_to_sell' => true,
            'service_product_id' => $serviceProductTwo->id,
            'address_id' => $addressTwo->id,
            'created_at' => now()->subDay(1),
        ]);
    }

    #[Test]
    public function it_returns_the_next_available_company_based_on_the_locations_with_the_lowest_avg_legs_sold(): void
    {
        $this->markTestIncomplete();

        Role::findOrCreate('business-development-manager');

        Role::findOrCreate('sales-development-representative');

        $company = Company::factory()->createQuietly();

        CompanyIndustry::factory()->createQuietly([
            'company_id' => $company->id,
            'industry_id' => $this->industry->id,
        ]);

        CompanyLocation::factory()->createQuietly([
            'company_id' => $company->id,
            'address_id' => $this->address->id,
        ]);

        $this->actingAs(User::factory()->create());

        $nextAvailableCompany = app(AssignNextAvailableExistingCompany::class)->run();

        $this->assertNotNull($nextAvailableCompany);

        $this->assertTrue($nextAvailableCompany->is($company));
    }

    #[Test]
    public function it_returns_the_next_available_company_based_on_the_locations_with_the_lowest_avg_legs_sold_with_timezone(): void
    {
        $this->markTestIncomplete();

        $company = Company::factory()->createQuietly();

        CompanyIndustry::factory()->create([
            'company_id' => $company->id,
            'industry_id' => $this->industry->id,
        ]);

        CompanyLocation::factory()->create([
            'company_id' => $company->id,
            'address_id' => $this->address->id,
        ]);

        $tzAddress = Address::factory()->create();

        // THERE IS NO EASY FACTORY FOR THIS
        $zipCode = USZipCode::factory()->create();

        CompanyLocation::factory()->create([
            'company_id' => $company->id,
            'address_id' => $tzAddress,
            'is_primary' => true,
        ]);

        dd($company->primaryLocation->address->usZipCode);

        $this->actingAs(User::factory()->create());

        $nextAvailableCompany = app(AssignNextAvailableExistingCompany::class)->run([
            // ...
        ]);

        $this->assertNull($nextAvailableCompany);

        $this->assertTrue($nextAvailableCompany->is($company));
    }
}
