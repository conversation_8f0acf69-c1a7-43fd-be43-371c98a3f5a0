<?php

namespace Tests\Feature\Marketing;

use App\DTO\EmailService\OutgoingEmailDTO;
use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Services\EmailAddress\EmailAddressService;
use App\Services\MarketingCampaign\Events\ClickedEvent;
use App\Services\MarketingCampaign\Events\ComplaintEvent;
use App\Services\MarketingCampaign\Events\DeliveredEvent;
use App\Services\MarketingCampaign\Events\FailedEvent;
use App\Services\MarketingCampaign\Events\OpenedEvent;
use App\Services\MarketingCampaign\SocketLabsEventInterpreter;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;

class MarketingWebhookTest extends TestCase
{
    use RefreshDatabase;

    private EmailAddressService $emailService;

    private SocketLabsEventInterpreter $interpreter;

    private MarketingCampaign $campaign;

    private MarketingCampaignConsumer $consumer;

    const string SOCKET_LABS_SECRET_KEY = 'abc123';

    const float  TEST_TIMESTAMP = **********;

    protected Carbon $now;

    protected function setUp(): void
    {
        $this->now = Carbon::createFromTimestamp(self::TEST_TIMESTAMP);

        Carbon::setTestNow($this->now);

        parent::setUp();

        $this->campaign = MarketingCampaign::factory()->create();
        $this->consumer = MarketingCampaignConsumer::factory()->create([
            MarketingCampaignConsumer::FIELD_ID => 1,
            MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID => $this->campaign->id,
            MarketingCampaignConsumer::FIELD_STATUS => MarketingCampaignConsumerStatus::SENT,
            MarketingCampaignConsumer::FIELD_SENT_AT => now(),
        ]);

        Config::set('services.marketing.email.socket_labs.secret_key', self::SOCKET_LABS_SECRET_KEY);
        $this->interpreter = app(SocketLabsEventInterpreter::class);
    }

    #[DataProvider('webhookProvider')]
    public function test_webhook(array $payload, array $headers, array $expected)
    {
        $request = Request::create('/api/webhook/marketing/email', 'POST', [], [], [], [], json_encode($payload));

        $request->headers->set('Content-Type', 'application/json');

        // Apply any additional headers
        foreach ($headers as $key => $value) {
            $request->headers->set($key, $value);
        }

        $response = $this->interpreter->interpret($request);

        $this->assertEquals($expected['event'], $response::class);

        $response->trigger();

        $this->consumer->refresh();

        foreach ($expected['assert'] as $field => $expected) {
            $this->assertEquals($expected, $this->consumer->{$field});
        }
    }

    public static function webhookProvider(): array
    {
        return [
            'Socket Labs Delivered Event' => [
                'payload' => [
                    'Type' => 'Delivered',
                    'Response' => 'Sample Response',
                    'LocalIp' => ':01',
                    'RemoteMta' => 'Sample RemoteMta',
                    'Data' => [
                        'Meta' => [
                            ['Key' => OutgoingEmailDTO::EMAIL_META_MCC_ID_KEY, 'Value' => '1'],
                        ],
                        'Tags' => ['Sample Tag', 'Sample Message'],
                    ],
                    'DateTime' => '2025-04-07T22:34:44.1688027Z',
                    'MailingId' => 'SLNL-0-9999999-9999999',
                    'MessageId' => 'SampleMessageId',
                    'Address' => '<EMAIL>',
                    'ServerId' => 12345,
                    'SubaccountId' => 15286,
                    'IpPoolId' => 12345,
                    'SecretKey' => self::SOCKET_LABS_SECRET_KEY,
                    'source' => 'socket_labs',
                ],
                'headers' => [
                    'host' => 'herring-immense-surely.ngrok-free.app',
                    'user-agent' => 'SocketLabs-EventWebhooks/*******',
                    'content-length' => '442',
                    'content-type' => 'application/json; charset=utf-8',
                    'request-context' => 'appId=cid-v1:298a8cc8-6cfb-4a75-badb-ef6184316de8',
                    'request-id' => '|f258c96def4f8723152c2ee73800bb12.8e4ceb8bee7441d9.',
                    'traceparent' => '00-f258c96def4f8723152c2ee73800bb12-8e4ceb8bee7441d9-00',
                    'x-forwarded-for' => '**************',
                    'x-forwarded-host' => 'herring-immense-surely.ngrok-free.app',
                    'x-forwarded-proto' => 'https',
                    'accept-encoding' => 'gzip',
                ],
                'expected' => [
                    'event' => DeliveredEvent::class,
                    'assert' => [
                        MarketingCampaignConsumer::FIELD_STATUS => MarketingCampaignConsumerStatus::DELIVERED,
                        MarketingCampaignConsumer::FIELD_DELIVERED_AT => CarbonImmutable::createFromTimestamp(self::TEST_TIMESTAMP)->format('Y-m-d H:i:s'),
                    ],
                ],
            ],
            'Socket Labs Click Event' => [
                'payload' => [
                    'TrackingType' => 0,
                    'Type' => 'Tracking',
                    'ClientIp' => '***************',
                    'Url' => 'https://example.com',
                    'UserAgent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.117 Safari/537.36',
                    'Data' => [
                        'Meta' => [
                            ['Key' => OutgoingEmailDTO::EMAIL_META_MCC_ID_KEY, 'Value' => '1'],
                        ],
                        'Tags' => ['Sample Tag', 'Sample Message'],
                    ],
                    'DateTime' => '2025-04-07T23:01:08.6697068Z',
                    'MailingId' => 'SLNL-0-9999999-9999999',
                    'MessageId' => 'SampleMessageId',
                    'Address' => '<EMAIL>',
                    'ServerId' => 12345,
                    'SubaccountId' => 15286,
                    'IpPoolId' => 12345,
                    'SecretKey' => self::SOCKET_LABS_SECRET_KEY,
                    'source' => 'socket_labs',
                ],
                'headers' => [
                    'host' => ['herring-immense-surely.ngrok-free.app'],
                    'user-agent' => ['SocketLabs-EventWebhooks/*******'],
                    'content-length' => ['569'],
                    'content-type' => ['application/json; charset=utf-8'],
                    'request-context' => ['appId=cid-v1:298a8cc8-6cfb-4a75-badb-ef6184316de8'],
                    'request-id' => ['|d7c03faa81744736285e561bfde98053.f0f2e6d4382f2e67.'],
                    'traceparent' => ['00-d7c03faa81744736285e561bfde98053-f0f2e6d4382f2e67-00'],
                    'x-forwarded-for' => ['**************'],
                    'x-forwarded-host' => ['herring-immense-surely.ngrok-free.app'],
                    'x-forwarded-proto' => ['https'],
                    'accept-encoding' => ['gzip'],
                ],
                'expected' => [
                    'event' => ClickedEvent::class,
                    'assert' => [
                        MarketingCampaignConsumer::FIELD_CLICKED_AT => CarbonImmutable::createFromTimestamp(self::TEST_TIMESTAMP)->format('Y-m-d H:i:s'),
                    ],
                ],
            ],
            'Socket Labs Complaint Event' => [
                'payload' => [
                    'Type' => 'Complaint',
                    'UserAgent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.117 Safari/537.36',
                    'Data' => [
                        'Meta' => [
                            ['Key' => OutgoingEmailDTO::EMAIL_META_MCC_ID_KEY, 'Value' => '1'],
                        ],
                        'Tags' => ['Sample Tag', 'Sample Message'],
                    ],
                    'From' => '<EMAIL>',
                    'To' => '<EMAIL>',
                    'Length' => 999,
                    'DateTime' => '2025-04-07T23:04:42.6302084Z',
                    'MailingId' => 'SLNL-0-9999999-9999999',
                    'MessageId' => 'SampleMessageId',
                    'Address' => '<EMAIL>',
                    'ServerId' => 12345,
                    'SubaccountId' => 15286,
                    'IpPoolId' => 12345,
                    'SecretKey' => self::SOCKET_LABS_SECRET_KEY,
                    'source' => 'socket_labs',
                ],
                'headers' => [
                    'host' => ['herring-immense-surely.ngrok-free.app'],
                    'user-agent' => ['SocketLabs-EventWebhooks/*******'],
                    'content-length' => ['440'],
                    'content-type' => ['application/json; charset=utf-8'],
                    'request-context' => ['appId=cid-v1:298a8cc8-6cfb-4a75-badb-ef6184316de8'],
                    'request-id' => ['|0f59838da52c412be30e8a5679af970e.9c8ef72cb590f09e.'],
                    'traceparent' => ['00-0f59838da52c412be30e8a5679af970e-9c8ef72cb590f09e-00'],
                    'x-forwarded-for' => ['**************'],
                    'x-forwarded-host' => ['herring-immense-surely.ngrok-free.app'],
                    'x-forwarded-proto' => ['https'],
                    'accept-encoding' => ['gzip'],
                ],
                'expected' => [
                    'event' => ComplaintEvent::class,
                    'assert' => [], // not doing anything atm
                ],
            ],
            'Socket Labs Failed Event' => [
                'payload' => [
                    'Type' => 'Failed',
                    'BounceStatus' => 'Sample BounceStatus',
                    'DiagnosticCode' => 'Sample DiagnosticCode',
                    'FromAddress' => '<EMAIL>',
                    'FailureCode' => 2001,
                    'FailureType' => 'Permanent',
                    'Reason' => 'Sample Reason',
                    'RemoteMta' => 'Sample RemoteMta',
                    'Data' => [
                        'Meta' => [
                            ['Key' => OutgoingEmailDTO::EMAIL_META_MCC_ID_KEY, 'Value' => '1'],
                        ],
                        'Tags' => ['Sample Tag', 'Sample Message'],
                    ],
                    'DateTime' => '2025-04-07T23:06:58.7028052Z',
                    'MailingId' => 'SLNL-0-9999999-9999999',
                    'MessageId' => 'SampleMessageId',
                    'Address' => '<EMAIL>',
                    'ServerId' => 12345,
                    'SubaccountId' => 15286,
                    'IpPoolId' => 12345,
                    'SecretKey' => self::SOCKET_LABS_SECRET_KEY,
                    'source' => 'socket_labs',
                ],
                'headers' => [
                    'host' => ['herring-immense-surely.ngrok-free.app'],
                    'user-agent' => ['SocketLabs-EventWebhooks/*******'],
                    'content-length' => ['576'],
                    'content-type' => ['application/json; charset=utf-8'],
                    'request-context' => ['appId=cid-v1:298a8cc8-6cfb-4a75-badb-ef6184316de8'],
                    'request-id' => ['|f4550a9c1c4b43ce3d05f3dc67574ea4.f3f4bbf4a55e63c5.'],
                    'traceparent' => ['00-f4550a9c1c4b43ce3d05f3dc67574ea4-f3f4bbf4a55e63c5-00'],
                    'x-forwarded-for' => ['**************'],
                    'x-forwarded-host' => ['herring-immense-surely.ngrok-free.app'],
                    'x-forwarded-proto' => ['https'],
                    'accept-encoding' => ['gzip'],
                ],
                'expected' => [
                    'event' => FailedEvent::class,
                    'assert' => [
                        MarketingCampaignConsumer::FIELD_STATUS => MarketingCampaignConsumerStatus::ERROR,
                    ],
                ],
            ],
            'Socket Labs Open Event' => [
                'payload' => [
                    'TrackingType' => 1,
                    'Type' => 'Tracking',
                    'ClientIp' => '***************',
                    'Url' => 'https://example.com',
                    'UserAgent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.117 Safari/537.36',
                    'Data' => [
                        'Meta' => [
                            ['Key' => OutgoingEmailDTO::EMAIL_META_MCC_ID_KEY, 'Value' => '1'],
                        ],
                        'Tags' => ['Sample Tag', 'Sample Message'],
                    ],
                    'DateTime' => '2025-04-07T23:09:13.1766239Z',
                    'MailingId' => 'SLNL-0-9999999-9999999',
                    'MessageId' => 'SampleMessageId',
                    'Address' => '<EMAIL>',
                    'ServerId' => 12345,
                    'SubaccountId' => 15286,
                    'IpPoolId' => 12345,
                    'SecretKey' => self::SOCKET_LABS_SECRET_KEY,
                    'source' => 'socket_labs',
                ],
                'headers' => [
                    'host' => ['herring-immense-surely.ngrok-free.app'],
                    'user-agent' => ['SocketLabs-EventWebhooks/*******'],
                    'content-length' => ['569'],
                    'content-type' => ['application/json; charset=utf-8'],
                    'request-context' => ['appId=cid-v1:298a8cc8-6cfb-4a75-badb-ef6184316de8'],
                    'request-id' => ['|bcc48c94f6bca1bae332de3364aeb516.d953aae0dcf91712.'],
                    'traceparent' => ['00-bcc48c94f6bca1bae332de3364aeb516-d953aae0dcf91712-00'],
                    'x-forwarded-for' => ['**************'],
                    'x-forwarded-host' => ['herring-immense-surely.ngrok-free.app'],
                    'x-forwarded-proto' => ['https'],
                    'accept-encoding' => ['gzip'],
                ],
                'expected' => [
                    'event' => OpenedEvent::class,
                    'assert' => [
                        MarketingCampaignConsumer::FIELD_OPENED_AT => CarbonImmutable::createFromTimestamp(self::TEST_TIMESTAMP)->format('Y-m-d H:i:s'),
                    ],
                ],
            ],
        ];
    }
}
