<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class CompanyRoleHistoryControllerTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function history_with_empty_data(): void
    {
        $this->actingAs(User::factory()->create())
            ->get(route('internal-api.v1.companies.history.role', [
                'company' => Company::factory()->createQuietly()->id,
                'role' => Role::findOrCreate('account-manager')->name,
            ]))
            ->assertOk()
            ->assertJson([
                'data' => [],
            ]);
    }

    #[Test]
    public function history_with_full_data(): void
    {
        $company = Company::factory()->createQuietly();

        $user = User::factory()->createQuietly();

        $role = Role::findOrCreate('account-manager');

        $company->assign($user)->as($role->name);

        $this->actingAs(User::factory()->create())
            ->get(route('internal-api.v1.companies.history.role', [
                'company' => $company->id,
                'role' => $role->name,
            ]))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'title',
                        'date',
                        'payload',
                    ],
                ],
            ])
            ->assertJsonPath('data.0.title', "Assigned to $user->name")
            ->assertJsonPath('data.0.payload.0', 'Author: System');
    }
}
