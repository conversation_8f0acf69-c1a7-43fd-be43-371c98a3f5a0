<?php

namespace Tests\Feature\Http\Controllers\Prospects;

use App\Actions\Company\AssignNextAvailableExistingCompany;
use App\Enums\OpportunityNotifications\OpportunityNotificationConfigType;
use App\Models\CompanyUserRelationship;
use App\Models\MissedProducts\OpportunityNotification;
use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Prospects\Contact;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\QueueSortPosition;
use App\Models\RoleConfiguration;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Sentry;
use SolarInvestments\Testing\SkipTestWhenRunningCI;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class ProspectingAPIControllerTest extends TestCase
{
    use RefreshDatabase, SkipTestWhenRunningCI;

    #[Test]
    public function get_my_converted_companies_with_companies(): void
    {
        $user = User::factory()->withRole('business-development-manager')->create()->givePermissionTo(Permission::findOrCreate('prospecting'));

        $company = Company::factory()->createQuietly();

        $company->assign($user)->as('business-development-manager');

        $this->actingAs($user)
            ->get(route('internal-api.v2.prospecting.get-my-converted-companies'))
            ->assertOk()
            ->assertJson([
                'companies' => [
                    [
                        'id' => $company->id,
                        'name' => $company->name,
                        'industries' => $company->industries->toArray(),
                        'state' => $company->locations?->first()?->address?->state ?? '',
                        'decision_maker' => $company->users?->first()?->first_name.' '.$company->users?->first()?->last_name ?? '',
                        'website' => $company->website ?? '',
                        'profile_link' => $company->getAdminProfileUrl(),
                    ],
                ],
            ]);
    }

    #[Test]
    public function get_my_converted_companies_with_no_companies(): void
    {
        Role::findOrCreate('business-development-manager');

        $user = User::factory()->create()->givePermissionTo(Permission::findOrCreate('prospecting'));

        $this->actingAs($user)
            ->get(route('internal-api.v2.prospecting.get-my-converted-companies'))
            ->assertOk()
            ->assertJson([
                'companies' => [],
            ]);
    }

    #[Test]
    public function get_my_converted_companies_with_no_permission(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user)
            ->get(route('internal-api.v2.prospecting.get-my-converted-companies'))
            ->assertForbidden();
    }

    #[Test]
    public function get_next_available_prospect_with_no_permission(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user)
            ->get(route('internal-api.v2.prospecting.get-next-available-prospect'))
            ->assertForbidden();
    }

    #[Test]
    public function get_next_available_prospect_with_cant_work_sourced_prospects(): void
    {
        $user = User::factory()->create()->givePermissionTo(Permission::findOrCreate('prospecting'));

        $this->actingAs($user)
            ->get(route('internal-api.v2.prospecting.get-next-available-prospect'))
            ->assertForbidden();
    }

    #[Test]
    public function get_next_available_prospect_with_can_work_sourced_prospects(): void
    {
        $this->setUpSkipTestWhenRunningCI();

        $this->markTestSkipped('fails locally, revisit');

        $user = User::factory()->create()->givePermissionTo(
            Permission::findOrCreate('prospecting'),
            Permission::findOrCreate('work-sourced-prospects'),
        );

        $company = Company::factory()->createQuietly();

        $industry = Industry::factory()->create();

        $industryService = IndustryService::factory()->create([
            'industry_id' => $industry->id,
        ]);

        $prospect = NewBuyerProspect::factory()->create([
            'status' => 'initial',
            'user_id' => 0,
            'decision_maker_phone' => '1234567890',
            'decision_maker_email' => '<EMAIL>',
            'company_website' => 'https://test.com',
            'industry_service_ids' => [$industryService->id],
            'company_id' => $company->id,
        ]);

        $this->actingAs($user)
            ->get(route('internal-api.v2.prospecting.get-next-available-prospect'))
            ->assertOk()
            ->assertJson([
                'prospect' => [
                    'reference' => $prospect->external_reference,
                    'user_id' => $prospect->refresh()->user_id,
                    'company_id' => $prospect->company_id,
                    'status' => 'active',
                    'resolution' => null,
                    'additional_data' => '',
                    'company_name' => $prospect->company_name,
                    'company_website' => $prospect->company_website,
                    'company_description' => $prospect->company_description,
                    'company_phone' => $prospect->company_phone,
                    'address_street' => $prospect->address_street,
                    'address_city_key' => $prospect->address_city_key,
                    'address_state_abbr' => $prospect->address_state_abbr,
                    'decision_maker_first_name' => $prospect->decision_maker_first_name,
                    'decision_maker_last_name' => $prospect->decision_maker_last_name,
                    'decision_maker_email' => $prospect->decision_maker_email,
                    'decision_maker_phone' => $prospect->decision_maker_phone,
                    'notes' => $prospect->notes,
                    'industry_ids' => $prospect->industry_service_ids,
                    'duplicate_companies' => [],
                    'source' => $prospect->source->value,
                ],
            ]);

        $this->assertDatabaseCount('new_buyer_prospects', 1);

        $this->assertDatabaseHas('new_buyer_prospects', [
            'user_id' => $user->id,
            'status' => 'active',
        ]);
    }

    #[Test]
    public function get_next_available_prospect_with_reserved_prospect(): void
    {
        $this->setUpSkipTestWhenRunningCI();

        $user = User::factory()->create()->givePermissionTo(
            Permission::findOrCreate('prospecting'),
            Permission::findOrCreate('work-sourced-prospects'),
        );

        $company = Company::factory()->createQuietly();

        $industry = Industry::factory()->create();

        $industryService = IndustryService::factory()->create([
            'industry_id' => $industry->id,
        ]);

        $reservingUser = User::factory()->create();

        NewBuyerProspect::factory()->has(Contact::factory())->create([
            'status' => 'initial',
            'user_id' => $reservingUser->id,
            'decision_maker_phone' => '1234567890',
            'decision_maker_email' => '<EMAIL>',
            'company_website' => 'https://test.com',
            'industry_service_ids' => [$industryService->id],
            'company_id' => $company->id,
        ]);

        Sentry::shouldReceive('captureException')->once();

        $this->actingAs($user)
            ->get(route('internal-api.v2.prospecting.get-next-available-prospect'))
            ->assertServerError();

        $this->assertDatabaseCount('new_buyer_prospects', 1);

        $this->assertDatabaseHas('new_buyer_prospects', [
            'user_id' => $reservingUser->id,
            'status' => 'initial',
        ]);
    }

    #[Test]
    public function get_next_available_prospect_with_industries_from_user_role_configuration()
    {
        $user = User::factory()->create()->givePermissionTo(
            Permission::findOrCreate('prospecting'),
            Permission::findOrCreate('work-sourced-prospects'),
        );

        $company = Company::factory()->createQuietly();

        $businessDevelopmentManagerRole = Role::findOrCreate('business-development-manager');

        $industries = Industry::factory(2)->create();

        $industryServices = IndustryService::factory(2)->sequence(fn (Sequence $sequence) => [
            'industry_id' => $industries->get($sequence->index)->id,
        ])->create();

        RoleConfiguration::factory()
            ->for($businessDevelopmentManagerRole)
            ->for($user)
            ->create([
                'data' => [
                    'industries' => [
                        $industries->first()->id,
                    ],
                ],
            ]);

        NewBuyerProspect::factory()->create([
            'status' => 'initial',
            'user_id' => 0,
            'decision_maker_phone' => '1234567890',
            'decision_maker_email' => '<EMAIL>',
            'company_website' => 'https://test.com',
            'source' => 'alist',
            'industry_service_ids' => [$industryServices->last()->id],
            'company_id' => $company->id,
        ]);

        $this->actingAs($user)
            ->get(route('internal-api.v2.prospecting.get-next-available-prospect'))
            ->assertNotFound();

        $this->assertDatabaseCount('new_buyer_prospects', 1);

        $this->assertDatabaseHas('new_buyer_prospects', [
            'user_id' => 0,
            'status' => 'initial',
        ]);
    }

    #[Test]
    public function release_company_back_to_queue_with_no_permission(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user)
            ->post(route('internal-api.v2.prospecting.release-company-back-to-queue', Company::factory()->createQuietly()))
            ->assertForbidden();
    }

    #[Test]
    public function release_company_back_to_queue_with_company_doesnt_have_business_development_manager(): void
    {
        $user = User::factory()->create()->givePermissionTo(
            Permission::findOrCreate('prospecting'),
            Permission::findOrCreate('work-sourced-prospects'),
        );

        $company = Company::factory()->createQuietly();

        Role::findOrCreate('business-development-manager');

        $this->actingAs($user)
            ->post(route('internal-api.v2.prospecting.release-company-back-to-queue', $company))
            ->assertOk();

        $this->assertDatabaseCount('queue_sort_positions', 0);
    }

    #[Test]
    public function release_company_back_to_queue_with_company_business_development_manager_doesnt_match_current_user(): void
    {
        $user = User::factory()->create()->givePermissionTo(
            Permission::findOrCreate('prospecting'),
            Permission::findOrCreate('work-sourced-prospects'),
        );

        $company = Company::factory()->createQuietly();

        $company->assign(User::factory()->create())->as(Role::findOrCreate('business-development-manager')->name);

        $this->actingAs($user)
            ->post(route('internal-api.v2.prospecting.release-company-back-to-queue', $company))
            ->assertOk();

        $this->assertDatabaseCount('queue_sort_positions', 0);
    }

    #[Test]
    public function release_company_back_to_queue_with_company_business_development_manager_matches_current_user(): void
    {
        $user = User::factory()->create()->givePermissionTo(
            Permission::findOrCreate('prospecting'),
            Permission::findOrCreate('work-sourced-prospects'),
        );

        $company = Company::factory()->createQuietly();

        $company->assign($user)->as(Role::findOrCreate('business-development-manager')->name);

        $this->actingAs($user)
            ->post(route('internal-api.v2.prospecting.release-company-back-to-queue', $company))
            ->assertOk();

        $this->assertDatabaseCount('queue_sort_positions', 1);

        $this->assertDatabaseHas('queue_sort_positions', [
            'queue_name' => 'existing_companies',
            'class_name' => Company::class,
            'model_id' => $company->id,
            'released_by_user_id' => $user->id,
            'ordinal_value' => 1,
        ]);

        $this->assertTrue(QueueSortPosition::query()->first()->released_at->isToday());

        $this->assertEmpty($company->refresh()->businessDevelopmentManagerCompanies);
    }

    #[Test]
    public function scan_missed_products_with_companies(): void
    {
        $opportunityNotificationConfig = OpportunityNotificationConfig::factory()->create([
            'type' => OpportunityNotificationConfigType::BDM_COMPANIES,
        ])->refresh();

        $user = User::factory()->create()->givePermissionTo(Permission::findOrCreate('prospecting'));

        $company = Company::factory()->createQuietly();

        CompanyUserRelationship::factory()->create([
            'user_id' => $user->id,
            'company_id' => $company->id,
            'role_id' => Role::findOrCreate('business-development-manager')->id,
        ]);

        $opportunityNotification = OpportunityNotification::factory()->create([
            'company_id' => $company->id,
        ]);

        $this->actingAs($user)
            ->get(route('internal-api.v2.prospecting.scan-missed-products'))
            ->assertOk()
            ->assertJson([
                'missed_products' => [
                    'config' => [
                        'maximum_send_frequency' => $opportunityNotificationConfig->maximum_send_frequency,
                        'lead_threshold' => $opportunityNotificationConfig->lead_threshold,
                        'days_to_query' => $opportunityNotificationConfig->days_to_query,
                    ],
                    'companies' => [
                        $company->id => [
                            'missed_product_count' => 0,
                            'last_sent_at' => $opportunityNotification->sent_at->timestamp * 1000,
                            'can_send_at' => max(0, $opportunityNotification->sent_at->addDays($opportunityNotificationConfig->maximum_send_frequency)->timestamp * 1000),
                        ],
                    ],
                ],
            ]);
    }

    #[Test]
    public function scan_missed_products_with_no_companies(): void
    {
        $this->markTestSkipped('fails locally, revisit');

        $opportunityNotificationConfig = OpportunityNotificationConfig::factory()->create([
            'type' => OpportunityNotificationConfigType::BDM_COMPANIES,
        ])->refresh();

        $this->actingAs(User::factory()->create()->givePermissionTo(Permission::findOrCreate('prospecting')))
            ->get(route('internal-api.v2.prospecting.scan-missed-products'))
            ->assertOk()
            ->assertJson([
                'missed_products' => [
                    'config' => [
                        'maximum_send_frequency' => $opportunityNotificationConfig->maximum_send_frequency,
                        'lead_threshold' => $opportunityNotificationConfig->lead_threshold,
                        'days_to_query' => $opportunityNotificationConfig->days_to_query,
                    ],
                    'companies' => [],
                ],
            ]);
    }

    #[Test]
    public function scan_missed_products_with_no_configuration(): void
    {
        $this->actingAs(User::factory()->create()->givePermissionTo(Permission::findOrCreate('prospecting')))
            ->get(route('internal-api.v2.prospecting.scan-missed-products'))
            ->assertServerError();
    }

    #[Test]
    public function scan_missed_products_with_no_permission(): void
    {
        $this->actingAs(User::factory()->create())
            ->get(route('internal-api.v2.prospecting.scan-missed-products'))
            ->assertForbidden();
    }

    #[Test]
    public function it_defers_to_the_existing_company_assignment_service_to_determine_the_next_available_company(): void
    {
        $this->mock(AssignNextAvailableExistingCompany::class, fn ($mock) => $mock->shouldReceive('run')->once());

        $this->actingAs(User::factory()
            ->withRole('business-development-manager')
            ->create()
            ->givePermissionTo(Permission::findOrCreate('prospecting'))
        )->get(route('internal-api.v2.prospecting.get-next-available-company'));
    }

    #[Test]
    public function it_returns_the_next_available_existing_company(): void
    {
        $company = Company::factory()->has(Industry::factory())->createQuietly();

        $this->mock(AssignNextAvailableExistingCompany::class, fn (MockInterface $mock) => $mock->shouldReceive('run')->andReturn($company)->once());

        $this->actingAs(User::factory()
            ->withRole('business-development-manager')
            ->create()
            ->givePermissionTo(Permission::findOrCreate('prospecting'))
        )->get(route('internal-api.v2.prospecting.get-next-available-company'))
            ->assertJson([
                'company_id' => $company->id,
                'profile_link' => $company->getAdminProfileUrl(),
                'company_name' => $company->name,
            ]);
    }
}
