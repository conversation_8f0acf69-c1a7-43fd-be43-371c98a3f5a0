<?php

declare(strict_types=1);

namespace Tests\Feature\Http\Controllers\DashboardAPI;

use App\Models\Legacy\Location;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ReferenceDataControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Location $countyLocation;

    protected Location $stateLocation;

    protected Location $zipLocation;

    protected function setUp(): void
    {
        parent::setUp();

        Schema::connection('readonly')->disableForeignKeyConstraints();

        Location::truncate();

        Schema::connection('readonly')->enableForeignKeyConstraints();

        $this->user = User::factory()->create();
    }

    #[Test]
    public function it_gets_states_empty()
    {
        $this->actingAs($this->user)->get(route('internal-api.v1.campaign-wizard.locality-store.states'))
            ->assertOk()
            ->assertJson([
                'data' => [
                    'status' => true,
                    'states' => [],
                    'counties' => [],
                ],
            ]);
    }

    #[Test]
    public function it_gets_states_full()
    {
        $this->countyLocation = Location::factory()->create([
            'state' => 'CA',
            'state_abbr' => 'CA',
            'state_key' => 'CA',
            'county' => 'Orange',
            'county_key' => 'Orange',
            'type' => Location::TYPE_COUNTY,
        ]);

        $this->stateLocation = Location::factory()->create([
            'state' => 'CA',
            'state_abbr' => 'CA',
            'state_key' => 'CA',
            'type' => Location::TYPE_STATE,
        ]);

        $this->zipLocation = Location::factory()->create([
            'zip_code' => '92618',
            'state' => 'CA',
            'state_abbr' => 'CA',
            'state_key' => 'CA',
            'type' => Location::TYPE_ZIP_CODE,
        ]);

        $this->actingAs($this->user)->get(route('internal-api.v1.campaign-wizard.locality-store.states'))
            ->assertOk()
            ->assertJson([
                'data' => [
                    'status' => true,
                    'states' => [
                        [
                            'id' => $this->stateLocation->id,
                            'state_name' => 'CA',
                            'state_key' => 'CA',
                            'total_counties' => 1,
                            'total_zip_codes' => 1,
                        ],
                    ],
                    'counties' => [
                        'CA' => [
                            [
                                'county_key' => 'Orange',
                                'county' => 'Orange',
                                'id' => $this->countyLocation->id,
                            ],
                        ],
                    ],
                ],
            ]);
    }
}
