<?php

namespace Tests\Feature\Http\Controllers\DashboardAPI;

use App\Jobs\DispatchPubSubEvent;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadSalesType;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\PropertyType;
use App\Models\Odin\QualityTier;
use App\Models\SaleType;
use App\Services\Dashboard\JWT\DashboardJWTService;
use App\Services\Dashboard\JWT\DashboardTokenModel;
use Database\Seeders\IndustriesSeeder;
use Database\Seeders\IndustryServicesSeeder;
use Database\Seeders\LocationsSeeder;
use Database\Seeders\ProductCountyBidPricesSeeder;
use Database\Seeders\ProductCountyFloorPricesSeeder;
use Database\Seeders\ProductsSeeder;
use Database\Seeders\PropertyTypesSeeder;
use Database\Seeders\QualityTiersSeeder;
use Database\Seeders\SaleTypesSeeder;
use Database\Seeders\ServiceProductsSeeder;
use Illuminate\Foundation\Testing\DatabaseTruncation;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Schema;
use PHPUnit\Framework\Attributes\Group;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CampaignPriceControllerTest extends TestCase
{
    use DatabaseTruncation;

    protected function setUp(): void
    {
        parent::setUp();

        Schema::connection('readonly')->disableForeignKeyConstraints();

        EloquentCompany::query()->truncate();
        LeadCampaign::query()->truncate();
        Location::query()->truncate();

        Schema::connection('readonly')->enableForeignKeyConstraints();

        $this->seed(IndustriesSeeder::class);

        $this->seed(IndustryServicesSeeder::class);

        $this->seed(ProductsSeeder::class);

        $this->seed(ServiceProductsSeeder::class);

        $this->seed(SaleTypesSeeder::class);

        $this->seed(LocationsSeeder::class);

        LeadSalesType::query()->create([
            'id' => LeadSalesType::LEAD_SALE_TYPE_EXCLUSIVE_ID,
            'key_value' => LeadSalesType::KEY_VALUE_EXCLUSIVE,
            'name' => LeadSalesType::KEY_VALUE_EXCLUSIVE,
        ]);

        $this->seed(QualityTiersSeeder::class);

        $this->seed(PropertyTypesSeeder::class);

        $this->seed(ProductCountyFloorPricesSeeder::class);

        $this->seed(ProductCountyBidPricesSeeder::class);
    }

    #[Test, Group('slow')]
    public function it_bids_counties(): void
    {
        Queue::fake([DispatchPubSubEvent::class]);

        $legacyCompany = EloquentCompany::factory()->create();

        $this->assertDatabaseCount('tblcompany', 1, 'readonly');

        $company = Company::factory()->createQuietly([
            'legacy_id' => $legacyCompany->companyid,
        ]);

        CompanyCampaign::factory()->createQuietly([
            'company_id' => $company->id,
        ]);

        $legacyCampaign = LeadCampaign::factory()->create([
            'company_id' => $legacyCompany->companyid,
        ]);

        ProductCampaign::factory()->create([
            'parent_legacy_lead_campaign_id' => $legacyCampaign->id,
            'industry_service_id' => IndustryService::first()->id,
            'product_id' => Product::first()->id,
        ]);

        $mock = $this->partialMock(DashboardJWTService::class);

        $mock->shouldReceive('validate')->andReturn(true);

        $user = CompanyUser::factory()->create();

        $mock->shouldReceive('decode')->andReturn(new DashboardTokenModel($user->id));

        $this->withHeader('X-CLIENT-BEARER', (new DashboardJWTService('test', 5000))->generate($user->id))
            ->patch(route('dashboard-api.v3.companies.campaigns.prices.bid-county', [
                'companyId' => $company->id,
                'industry' => Industry::first()->id,
                'service' => Industry::first()->services->first()->id,
                'campaignUuid' => $legacyCampaign->uuid,
            ]), [
                'sale_type_id' => SaleType::first()->id,
                'quality_tier_id' => QualityTier::first()->id,
                'price' => 300,
                'campaign_uuid' => $legacyCampaign->uuid,
                'state_abbr' => Location::query()->where('type', Location::TYPE_COUNTY)->first()->state_abbr,
                'county_key' => Location::query()->where('type', Location::TYPE_COUNTY)->first()->county_key,
                'property_type_id' => PropertyType::first()->id,
            ])
            ->assertOk()
            ->assertJson([
                'data' => [
                    'status' => true,
                ],
            ]);

        Queue::assertPushed(DispatchPubSubEvent::class);
    }
}
