<?php

namespace Tests\Feature\Http\Controllers\API\Users;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class UserBaseAPIControllerTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_gets_user_roles_and_permissions_with_no_authenticated_user(): void
    {
        $this->get(route('internal-api.v1.users.get-user-roles-and-permissions'))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function it_gets_user_roles_and_permissions_with_authenticated_user(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user)
            ->get(route('internal-api.v1.users.get-user-roles-and-permissions'))
            ->assertOk()
            ->assertJson([
                'data' => [
                    'roles' => [],
                    'permissions' => [],
                    'ids' => [
                        'user' => $user->id,
                        'accountManager' => null,
                        'successManager' => null,
                    ],
                ],
            ]);
    }
}
