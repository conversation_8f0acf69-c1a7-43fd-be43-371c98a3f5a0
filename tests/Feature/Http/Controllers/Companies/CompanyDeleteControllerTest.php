<?php

namespace Tests\Feature\Http\Controllers\Companies;

use App\Jobs\DeleteCompany;
use App\Models\Call;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Text;
use App\Models\User;
use App\Repositories\ActivityLog\ActivityLogRepository;
use App\Services\Companies\Delete\CompanyDeleteService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class CompanyDeleteControllerTest extends TestCase
{
    use RefreshDatabase;

    protected Company $company;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->createQuietly();

        $this->company = Company::factory()->createQuietly();

    }

    #[Test]
    public function it_gets_info(): void
    {
        $companyUser = CompanyUser::factory()->createQuietly([
            'company_id' => $this->company->id,
        ]);

        Call::factory()->createQuietly([
            'other_number' => $companyUser->cell_phone,
            'relation_type' => 'company_user',
            'relation_id' => $companyUser->id,
        ]);

        Text::factory()->createQuietly([
            'other_number' => $companyUser->cell_phone,
            'relation_type' => 'company_user',
            'relation_id' => $companyUser->id,
        ]);

        $this->actingAs($this->user)
            ->getJson(route('internal-api.v1.companies.delete.info', $this->company))
            ->assertOk()
            ->assertExactJsonStructure([
                'data' => [
                    'calls' => [
                        'title',
                        'total',
                        'data' => [
                            '*' => [
                                'to',
                                'from',
                                'recording',
                                'timestamp',
                            ],
                        ],
                    ],
                    'texts' => [
                        'title',
                        'total',
                        'data' => [
                            '*' => [
                                'to',
                                'from',
                                'content',
                                'timestamp',
                            ],
                        ],
                    ],
                    'contacts' => [
                        'title',
                        'total',
                        'data' => [
                            '*' => [
                                'id',
                                'name',
                                'status',
                                'email',
                                'cell_phone',
                                'office_phone',
                            ],
                        ],
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_validates_deletion(): void
    {
        $this->actingAs($this->user)
            ->getJson(route('internal-api.v1.companies.delete.validate-delete', $this->company))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    'deletable' => true,
                    'data' => [
                        [
                            'title' => 'Company has no Product Assignments',
                            'data' => true,
                        ],
                        [
                            'title' => 'Company has no Invoices',
                            'data' => true,
                        ],
                        [
                            'title' => 'Company has no Payment Methods',
                            'data' => true,
                        ],
                        [
                            'title' => 'Company has no bundle invoices',
                            'data' => true,
                        ],
                        [
                            'title' => 'Company has no Billing Profiles',
                            'data' => true,
                        ],
                        [
                            'title' => 'Company has no Credit',
                            'data' => true,
                        ],
                        [
                            'title' => 'Company has no reviews',
                            'data' => true,
                        ],
                        [
                            'title' => 'Company not already queued for Deletion',
                            'data' => true,
                        ],
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_gets_impact(): void
    {
        $companyDeleteService = new CompanyDeleteService($this->company);

        $this->actingAs($this->user)
            ->getJson(route('internal-api.v1.companies.delete.impact', $this->company))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    'data' => $companyDeleteService->previewDelete(),
                ],
            ]);
    }

    #[Test]
    public function it_cancels_deletion(): void
    {
        $companyDeleteService = new CompanyDeleteService($this->company);

        $companyDeleteService->markForDeletion();

        $this->assertTrue($companyDeleteService->markedForDeletion());

        $this->actingAs($this->user)
            ->postJson(route('internal-api.v1.companies.delete.cancel', $this->company))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    'status' => true,
                ],
            ]);

        $this->assertFalse($companyDeleteService->markedForDeletion());
    }

    #[Test]
    public function it_queues_a_company_for_deletion(): void
    {
        Queue::fake();

        $this->user->givePermissionTo(Permission::findOrCreate('company/delete'));

        $this->spy(ActivityLogRepository::class)->shouldReceive('createActivityLog');

        $this->actingAs($this->user)
            ->deleteJson(route('internal-api.v1.companies.delete.dispatch', $this->company))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    'status' => true,
                ],
            ]);

        Queue::assertCount(1);

        Queue::assertPushed(DeleteCompany::class);
    }
}
