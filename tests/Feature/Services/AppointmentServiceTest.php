<?php

namespace Tests\Feature\Services;

use App\DataModels\Odin\Prices\BestRevenueScenarioDataModel;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\QualityTier;
use App\Models\AppointmentDelivery;
use App\Models\AppointmentProcessingAllocation;
use App\Models\ComputedRejectionStatistic;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product as ProductModel;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignSchedule;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Legacy\LeadProcessingRepository;
use App\Services\Legacy\APIConsumer;
use App\Services\Legacy\LeadDeliveryService;
use App\Services\Odin\Appointments\AppointmentService;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use App\Services\ProductPricing\BestRevenueScenario\BestRevenueScenarioServiceFactory;
use Carbon\Carbon;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;

class AppointmentServiceTest extends TestCase
{
    use RefreshDatabase;

    private bool $initialized = false;

    private ?AppointmentService $appointmentService;

    private ?ProductProcessingService $productProcessingService;

    private ?string $apiConsumerBaseUrl;

    private ?string $schedulingBaseUrl;

    protected function setUp(): void
    {
        parent::setUp();

        $this->markTestSkipped('Appointments deactivated');

        if (blank(config('services.twilio.sid'))) {
            $this->markTestSkipped('Skipping due to missing Twilio SID.');
        }

        if (blank(config('services.twilio.token'))) {
            $this->markTestSkipped('Skipping due to missing Twilio Token.');
        }

        if (blank(config('services.twilio.ml_sid'))) {
            $this->markTestSkipped('Skipping due to missing Twilio ML SID.');
        }

        if (! $this->initialized) {
            $this->appointmentService = app(AppointmentService::class);
            $this->productProcessingService = app(ProductProcessingService::class);

            config(['sales.brs_driver' => BestRevenueScenarioServiceFactory::DRIVER_DUMMY]);
            config(['services.scheduling.base_api_url' => 'http://test.com']);
            config(['services.admin_integration.base_url' => 'http://test.com']);

            $this->apiConsumerBaseUrl = app(APIConsumer::class)->getBaseURL();
            $this->schedulingBaseUrl = config('services.scheduling.base_api_url');

            $this->initialized = true;
        }

        Http::preventStrayRequests();
        Http::fake([
            "{$this->apiConsumerBaseUrl}/auth/get_token?*" => Http::response([
                APIConsumer::RESPONSE_CLIENT_TOKEN => 'token',
            ], 200),

            $this->apiConsumerBaseUrl.LeadDeliveryService::API_SAVE_QUOTE_COMPANY_FOR_APPOINTMENT => Http::sequence()
                ->push([APIConsumer::RESPONSE_RESULT => 1], 200)
                ->push([APIConsumer::RESPONSE_RESULT => 2], 200)
                ->push([APIConsumer::RESPONSE_RESULT => 3], 200)
                ->push([APIConsumer::RESPONSE_RESULT => 4], 200)
                ->whenEmpty(Http::response([APIConsumer::RESPONSE_RESULT => 1], 200)),

            $this->apiConsumerBaseUrl.LeadDeliveryService::API_SAVE_QUOTE_LOG_FOR_APPOINTMENT => Http::response([APIConsumer::RESPONSE_RESULT => true], 200),

            $this->apiConsumerBaseUrl.LeadProcessingRepository::API_UPDATE_LEAD_STATUS_ENDPOINT => Http::response([APIConsumer::RESPONSE_RESULT => true], 200),

            "{$this->schedulingBaseUrl}/auth-token" => Http::response([
                'status' => true,
                'data' => [
                    'token' => 'token',
                    'expires_in' => 3600,
                ],
            ], 200),

            "{$this->schedulingBaseUrl}/integration/schedules-availability?*" => Http::response([
                'status' => true,
                'data' => [
                    'availabilities' => [
                        1 => true,
                        2 => true,
                        3 => true,
                        4 => true,
                    ],
                ],
            ], 200),

            "{$this->schedulingBaseUrl}/integration/event/*" => Http::response([
                'status' => true,
                'data' => [

                ],
            ], 200),
        ]);
    }

    private function clearProductAssignments(): bool
    {
        ProductAssignment::query()->delete();

        return true;
    }

    private function prepareConsumerProducts(): bool
    {
        $solarLeadServiceProductId = ServiceProduct::query()
            ->whereHas(ServiceProduct::RELATION_PRODUCT, function ($has) {
                $has->where(ProductModel::FIELD_NAME, ProductEnum::LEAD);
            })
            ->whereHas(ServiceProduct::RELATION_SERVICE, function ($has) {
                $has->where(IndustryService::FIELD_SLUG, 'solar-installation');
            })
            ->firstOrFail()
            ->{ServiceProduct::FIELD_ID};

        $solarApptServiceProductId = ServiceProduct::query()
            ->whereHas(ServiceProduct::RELATION_PRODUCT, function ($has) {
                $has->where(ProductModel::FIELD_NAME, ProductEnum::APPOINTMENT);
            })
            ->whereHas(ServiceProduct::RELATION_SERVICE, function ($has) {
                $has->where(IndustryService::FIELD_SLUG, 'solar-installation');
            })
            ->firstOrFail()
            ->{ServiceProduct::FIELD_ID};

        ConsumerProduct::query()
            ->where(ConsumerProduct::FIELD_ID, 1)
            ->update([
                ConsumerProduct::FIELD_SERVICE_PRODUCT_ID => $solarLeadServiceProductId,
                ConsumerProduct::FIELD_CONTACT_REQUESTS => 3,
                ConsumerProduct::FIELD_GOOD_TO_SELL => true,
                ConsumerProduct::FIELD_STATUS => ConsumerProduct::STATUS_INITIAL,
                ConsumerProduct::CREATED_AT => Carbon::now('UTC')->subHour(),
            ]);

        ConsumerProduct::query()
            ->whereIn(ConsumerProduct::FIELD_ID, [2, 3, 4])
            ->update([
                ConsumerProduct::FIELD_SERVICE_PRODUCT_ID => $solarApptServiceProductId,
                ConsumerProduct::FIELD_CONTACT_REQUESTS => 3,
                ConsumerProduct::FIELD_GOOD_TO_SELL => true,
                ConsumerProduct::FIELD_STATUS => ConsumerProduct::STATUS_INITIAL,
                ConsumerProduct::CREATED_AT => Carbon::now('UTC')->subHour(),
            ]);

        return true;
    }

    private function prepareAppointmentProcessingAllocations(): bool
    {
        AppointmentProcessingAllocation::query()->forceDelete();

        AppointmentProcessingAllocation::factory(3)
            ->state([
                AppointmentProcessingAllocation::FIELD_LEAD_CONSUMER_PRODUCT_ID => 1,
                AppointmentProcessingAllocation::FIELD_LEAD_PROCESSOR_ID => 1,
                AppointmentProcessingAllocation::FIELD_PROCESSING_SCENARIO => '',
                AppointmentProcessingAllocation::FIELD_ALLOCATED => 0,
                AppointmentProcessingAllocation::FIELD_DELIVERED => 0,
                AppointmentProcessingAllocation::FIELD_FAILED_APPT_ALLOCATION => 0,
                AppointmentProcessingAllocation::FIELD_CANCELLED_REJECTED => 0,
                AppointmentProcessingAllocation::FIELD_ERROR => '',
                AppointmentProcessingAllocation::FIELD_ALLOCATED_AS_LEAD => 0,
            ])
            ->sequence(fn ($seq) => [AppointmentProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID => $seq->index + 2])
            ->create();

        return true;
    }

    private function prepareProductAppointments(): bool
    {
        ProductAppointment::factory(3)
            ->state([ProductAppointment::LEAD_CONSUMER_PRODUCT_ID => 1])
            ->sequence(fn ($seq) => [ProductAppointment::CONSUMER_PRODUCT_ID => $seq->index + 2])
            ->create();

        return true;
    }

    private function prepareProductCampaignSchedules(): bool
    {
        $productCampaignIds = ProductCampaign::query()
            ->whereHas(ProductCampaign::RELATION_PRODUCT, function ($has) {
                $has->where(ProductModel::FIELD_NAME, ProductEnum::APPOINTMENT);
            })
            ->limit(4)
            ->pluck(ProductCampaign::FIELD_ID)
            ->toArray();

        ProductCampaignSchedule::factory(count($productCampaignIds))
            ->sequence(fn ($seq) => [
                ProductCampaignSchedule::FIELD_SCHEDULE_ID => $seq->index + 1,
                ProductCampaignSchedule::FIELD_PRODUCT_CAMPAIGN_ID => $productCampaignIds[$seq->index],
            ])
            ->create();

        return true;
    }

    private function prepareProductCampaigns(int $productCampaignsCount = 3): bool
    {
        if ($productCampaignsCount === 0) {
            ProductCampaign::query()->delete();

            return true;
        }

        $productCampaignIds = ProductCampaign::query()
            ->whereHas(ProductCampaign::RELATION_PRODUCT, function ($has) {
                $has->where(ProductModel::FIELD_NAME, ProductEnum::APPOINTMENT);
            })
            ->limit($productCampaignsCount)
            ->pluck(ProductCampaign::FIELD_ID)
            ->toArray();

        ProductCampaign::query()
            ->whereNotIn(ProductCampaign::FIELD_ID, $productCampaignIds)
            ->delete();

        return true;
    }

    private function prepareAllocationTestData(int $productCampaignsCount = 3): bool
    {
        $this->prepareProductCampaigns($productCampaignsCount);
        $this->prepareConsumerProducts();
        $this->prepareAppointmentProcessingAllocations();
        $this->clearProductAssignments();
        $this->prepareProductAppointments();
        $this->prepareProductCampaignSchedules();

        return true;
    }

    private function assertSuccessfulAllocations(int $productCampaignsCount): void
    {
        if ($productCampaignsCount > 0) {
            Http::assertSent(function (Request $request) {
                return $request->url() === "{$this->schedulingBaseUrl}/auth-token";
            });

            Http::assertSent(function (Request $request) {
                return preg_match('/'.str_replace('/', '\/', $this->schedulingBaseUrl)."\/integration\/schedules-availability\?.+/", $request->url());
            });

            Http::assertSent(function (Request $request) {
                return preg_match('/'.str_replace('/', '\/', $this->schedulingBaseUrl)."\/integration\/event\/\d+/", $request->url());
            });

            Http::assertSent(function (Request $request) {
                return preg_match('/'.str_replace('/', '\/', $this->apiConsumerBaseUrl)."\/auth\/get_token\?.+/", $request->url());
            });

            Http::assertSent(function (Request $request) {
                return $request->url() === $this->apiConsumerBaseUrl.LeadDeliveryService::API_SAVE_QUOTE_LOG_FOR_APPOINTMENT;
            });

            Http::assertSent(function (Request $request) {
                return $request->url() === $this->apiConsumerBaseUrl.LeadProcessingRepository::API_UPDATE_LEAD_STATUS_ENDPOINT;
            });

            Http::assertSent(function (Request $request) {
                return $request->url() === $this->apiConsumerBaseUrl.LeadDeliveryService::API_SAVE_QUOTE_LOG_FOR_APPOINTMENT;
            });
        }

        $this->assertDatabaseHas(
            ConsumerProduct::TABLE,
            [
                ConsumerProduct::FIELD_ID => 1,
                ConsumerProduct::FIELD_STATUS => $productCampaignsCount > 0 ? ConsumerProduct::STATUS_ALLOCATED : ConsumerProduct::STATUS_INITIAL,
            ]
        );

        $apptConsumerProductIds = AppointmentProcessingAllocation::query()->pluck(AppointmentProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID);

        $apaCount = AppointmentProcessingAllocation::query()
            ->whereIn(AppointmentProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID, $apptConsumerProductIds)
            ->where([
                AppointmentProcessingAllocation::FIELD_LEAD_CONSUMER_PRODUCT_ID => 1,
                AppointmentProcessingAllocation::FIELD_ALLOCATED => true,
                AppointmentProcessingAllocation::FIELD_ALLOCATED_AS_LEAD => false,
                AppointmentProcessingAllocation::FIELD_FAILED_APPT_ALLOCATION => false,
            ])
            ->distinct()
            ->count();

        $this->assertEquals($productCampaignsCount, $apaCount);

        $paCount = ProductAssignment::query()
            ->whereIn(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, $apptConsumerProductIds)
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->distinct()
            ->count();

        $this->assertEquals($productCampaignsCount, $paCount);

        $adCount = AppointmentDelivery::query()
            ->whereHas(AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT, function ($has) use ($apptConsumerProductIds) {
                $has->whereIn(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, $apptConsumerProductIds);
            })
            ->count();

        $this->assertEquals($productCampaignsCount, $adCount);

        $cpCount = ConsumerProduct::query()
            ->whereIn(ConsumerProduct::FIELD_ID, $apptConsumerProductIds)
            ->where(ConsumerProduct::FIELD_STATUS, ConsumerProduct::STATUS_ALLOCATED)
            ->count();

        $this->assertEquals($productCampaignsCount, $cpCount);

        $failedApaCount = AppointmentProcessingAllocation::query()
            ->whereIn(AppointmentProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID, $apptConsumerProductIds)
            ->where([
                AppointmentProcessingAllocation::FIELD_LEAD_CONSUMER_PRODUCT_ID => 1,
                AppointmentProcessingAllocation::FIELD_ALLOCATED => false,
                AppointmentProcessingAllocation::FIELD_ALLOCATED_AS_LEAD => false,
                AppointmentProcessingAllocation::FIELD_FAILED_APPT_ALLOCATION => true,
            ])
            ->count();

        $this->assertEquals($apptConsumerProductIds->count() - $apaCount, $failedApaCount);
    }

    public static function processAppointmentAllocationsDataProvider(): array
    {
        return [
            [
                'productCampaignsCount' => 3,
            ],
            [
                'productCampaignsCount' => 2,
            ],
            [
                'productCampaignsCount' => 1,
            ],
            [
                'productCampaignsCount' => 0,
            ],
        ];
    }

    /**
     * @dataProvider processAppointmentAllocationsDataProvider
     */
    #[DataProvider('processAppointmentAllocationsDataProvider')]
    public function test_process_appointment_allocations(
        int $productCampaignsCount
    ): void {
        $this->prepareAllocationTestData($productCampaignsCount);

        $this->assertTrue($this->productProcessingService->processAppointmentAllocations());

        $this->assertSuccessfulAllocations($productCampaignsCount);
    }

    public function test_get_best_revenue_scenario_results_for_appointments()
    {
        $this->prepareAllocationTestData();

        $allocations = AppointmentProcessingAllocation::all();

        $brsResults = $this->appointmentService->getBestRevenueScenarioResultsForAppointments(
            $allocations,
            BestRevenueScenarioServiceFactory::make(BestRevenueScenarioServiceFactory::DRIVER_DUMMY),
            $allocations->count()
        );

        $this->assertEquals($allocations->count(), count($brsResults));

        $apptConsumerProductIds = $brsResults->keys()->toArray();

        foreach ($allocations as $allocation) {
            $this->assertContains($allocation->{AppointmentProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}, $apptConsumerProductIds);
        }

        foreach ($brsResults as $brsResult) {
            $this->assertInstanceOf(BestRevenueScenarioDataModel::class, $brsResult);
        }
    }

    /**
     * @throws Exception
     */
    public static function permuteBRSResultsDataProvider(): array
    {
        return [
            [
                'data' => [
                    2 => [1, 2, 3],
                    3 => [1, 2, 3],
                    4 => [1, 2, 3],
                ],
                'expected' => [
                    2 => 1,
                    3 => 2,
                    4 => 3,
                ],
            ],
            [
                'data' => [
                    2 => [1, 2, 3],
                    3 => [1, 2, 3],
                    4 => [4],
                ],
                'expected' => [
                    2 => 1,
                    3 => 2,
                    4 => 4,
                ],
            ],
            [
                'data' => [
                    2 => [1],
                    3 => [1, 2, 3],
                    4 => [4],
                ],
                'expected' => [
                    2 => 1,
                    3 => 2,
                    4 => 4,
                ],
            ],
            [
                'data' => [
                    2 => [1, 2, 3],
                    3 => [1],
                    4 => [4],
                ],
                'expected' => [
                    2 => 2,
                    3 => 1,
                    4 => 4,
                ],
            ],
            [
                'data' => [
                    2 => [3, 2, 1],
                    3 => [1, 2],
                    4 => [4],
                ],
                'expected' => [
                    2 => 3,
                    3 => 1,
                    4 => 4,
                ],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    private function generateBrsResults($brsData): Collection
    {
        $brsResults = collect();
        foreach ($brsData as $consumerProductId => $brsSetup) {
            $brs = new BestRevenueScenarioDataModel(
                $consumerProductId,
                QualityTier::IN_HOME,
                123
            );

            $campaigns = collect();
            foreach ($brsSetup as $companyId) {
                $campaigns->push(collect([
                    BestRevenueScenarioDataModel::PRICE => 100,
                    BestRevenueScenarioDataModel::UNRECTIFIED_PRICE => 100,
                    BestRevenueScenarioDataModel::BUDGET_USAGE => 0.00,
                    ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY => 0.00,
                    ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID => 0.00,
                    BestRevenueScenarioDataModel::COMPANY_ID => $companyId,
                    BestRevenueScenarioDataModel::CAMPAIGN_ID => $companyId,
                    BestRevenueScenarioDataModel::BUDGET => 'Test Budget',
                    BestRevenueScenarioDataModel::CAMPAIGN_BUDGET_ID => $companyId,
                    BestRevenueScenarioDataModel::SCHEDULE_ID => 0,
                ]));
            }

            $brs->setScenario(
                3,
                $campaigns->sum(BestRevenueScenarioDataModel::PRICE),
                $campaigns
            );

            $brsResults->push($brs);
        }

        return $brsResults;
    }

    /**
     * @dataProvider permuteBRSResultsDataProvider
     *
     * @throws Exception
     */
    #[DataProvider('permuteBRSResultsDataProvider')]
    public function test_permute_brs_results(
        array $data,
        array $expected
    ) {
        $brsResults = $this->generateBrsResults($data);

        $potentialCompanies = $this->appointmentService->permuteBRSResults($brsResults, 3);

        foreach ($expected as $consumerProductId => $companyId) {
            $this->assertEquals($potentialCompanies->get($consumerProductId)->keys()->first(), $companyId);
        }
    }
}
