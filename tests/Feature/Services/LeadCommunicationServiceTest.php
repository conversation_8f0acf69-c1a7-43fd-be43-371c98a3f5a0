<?php

namespace Tests\Feature\Services;

use App\Models\LeadProcessingCommunication;
use App\Services\LeadProcessing\LeadCommunicationService;
use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Factories\Sequence;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class LeadCommunicationServiceTest extends TestCase
{
    private readonly LeadCommunicationService $service;

    protected function setUp(): void
    {
        parent::setUp();

        if (collect(
            [
                config('services.twilio.sid'),
                config('services.twilio.token'),
                config('services.twilio.verify_service_sid'),
            ]
        )->some(fn ($item) => empty($item))) {
            $this->markTestSkipped('Skipping due to missing necessary Twilio credentials in config');
        }

        $this->service = app(LeadCommunicationService::class);
    }

    #[Test]
    public function can_record_contact_attempt(): void
    {
        $now = CarbonImmutable::now('UTC');

        CarbonImmutable::setTestNow($now);

        LeadProcessingCommunication::query()->delete();

        LeadProcessingCommunication::factory(5)
            ->typeAction()
            ->state([LeadProcessingCommunication::FIELD_LEAD_ID => 1])
            ->sequence(fn (Sequence $s) => [
                LeadProcessingCommunication::CREATED_AT => $now->subDays($s->index + 1),
            ])
            ->create();

        $this->service->recordContactAttempt(
            1,
            1,
            1
        );

        $this->assertEquals(1, LeadProcessingCommunication::where(LeadProcessingCommunication::FIELD_MOST_RECENT, true)->count());

        $this->assertDatabaseHas(LeadProcessingCommunication::TABLE, [
            LeadProcessingCommunication::FIELD_LEAD_ID => 1,
            LeadProcessingCommunication::FIELD_TYPE => LeadProcessingCommunication::TYPE_ACTION,
            LeadProcessingCommunication::FIELD_RELATION_ID => 1,
            LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID => 1,
            LeadProcessingCommunication::FIELD_MOST_RECENT => true,
            LeadProcessingCommunication::CREATED_AT => $now->format('Y-m-d H:i:s'),
            LeadProcessingCommunication::UPDATED_AT => $now->format('Y-m-d H:i:s'),
        ]);
    }
}
