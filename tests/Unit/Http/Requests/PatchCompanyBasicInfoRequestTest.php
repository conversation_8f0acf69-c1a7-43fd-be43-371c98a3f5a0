<?php

namespace Tests\Unit\Http\Requests;

use App\Http\Requests\PatchCompanyBasicInfoRequest;
use App\Models\Odin\Company;
use App\Models\SuccessManager;
use App\Models\User;
use App\Services\UserAuthorizationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Route;
use Illuminate\Testing\TestResponse;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class PatchCompanyBasicInfoRequestTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        TestResponse::macro('assertAuthorized', function () {
            return $this->assertStatus(302)->assertSessionHasErrors([
                'companyname',
                'name',
                'website',
                'prescreened',
            ]);
        });

        Role::findOrCreate('business-development-manager');

        $role = Role::findOrCreate('admin');
        $role->givePermissionTo(Permission::findOrCreate('company/admin-status/edit'));
        $role->givePermissionTo(Permission::findOrCreate('company/basic-status/edit'));
        $this->user = User::factory()->create()->assignRole($role)->givePermissionTo(Permission::findOrCreate('companies'));

        $this->actingAs($this->user);

        Route::name('test.company')->patch('test/company/{company_id}', static function (
            PatchCompanyBasicInfoRequest $request
        ) {
            return response();
        });
    }

    #[Test]
    public function it_doesnt_authorize_if_missing_admin_or_business_development_manager_role_even_with_companies_permission(
    ): void {
        $this->user = User::factory()->create()->givePermissionTo('companies');

        $company = Company::factory()->createQuietly();

        $this->actingAs($this->user)->patch(route('test.company', $company->id))->assertForbidden();
    }

    #[Test, DataProvider('roles')]
    public function it_authorizes_if_correct_role_with_companies_permission(string $role): void
    {
        $this->user->removeRole('admin')->assignRole($role);

        $company = Company::factory()->createQuietly();

        $this->actingAs($this->user)->patch(route('test.company', $company->id))->assertAuthorized();
    }

    #[Test]
    public function it_doesnt_authorize_if_user_has_business_development_manager_role_but_is_lacking_companies_permission(
    ): void {
        $this->user->removeRole('admin')->assignRole('business-development-manager')->revokePermissionTo(Permission::findOrCreate('companies'));

        $company = Company::factory()->createQuietly();

        $this->actingAs($this->user)->patch(route('test.company', $company->id))->assertForbidden();
    }

    #[Test]
    public function it_authorizes_if_user_has_admin_role_even_when_lacking_companies_permission(): void
    {
        $this->user->revokePermissionTo('companies');

        $company = Company::factory()->createQuietly();

        $this->actingAs($this->user)->patch(route('test.company', $company->id))->assertAuthorized();
    }

    #[Test]
    public function it_doesnt_authorize_if_an_account_manager_is_passed_and_user_cannot_edit_account_manager(): void
    {
        $this->partialMock(UserAuthorizationService::class)->shouldReceive('canEditAccountManager')->andReturn(false);

        $company = Company::factory()->createQuietly();

        $this->patch(route('test.company', $company->id), [
            'account_manager_id' => User::factory()->create()->id,
        ])->assertForbidden();
    }

    #[Test]
    public function it_does_authorize_if_an_account_manager_is_passed_and_user_can_edit_account_manager(): void
    {
        $this->partialMock(UserAuthorizationService::class)->shouldReceive('canEditAccountManager')->andReturn(true);

        $company = Company::factory()->createQuietly();

        $this->patch(route('test.company', $company->id), [
            'account_manager_id' => User::factory()->create()->id,
        ])->assertAuthorized();
    }

    #[Test]
    public function it_doesnt_authorize_if_an_a_success_manager_is_passed_and_user_cannot_edit_success_manager(): void
    {
        $this->partialMock(UserAuthorizationService::class)->shouldReceive('canEditSuccessManager')->andReturn(false);

        $company = Company::factory()->createQuietly();

        $this->patch(route('test.company', $company->id), [
            'success_manager_id' => SuccessManager::factory()->create()->id,
        ])->assertForbidden();
    }

    #[Test]
    public function it_does_authorize_if_an_a_success_manager_is_passed_and_user_can_edit_success_manager(): void
    {
        $this->partialMock(UserAuthorizationService::class)->shouldReceive('canEditSuccessManager')->andReturn(true);

        $company = Company::factory()->createQuietly();

        $this->patch(route('test.company', $company->id), [
            'success_manager_id' => SuccessManager::factory()->create()->id,
        ])->assertAuthorized();
    }

    public static function roles(): array
    {
        return [
            'admin' => ['admin'],
            'business-development-manager' => ['business-development-manager'],
        ];
    }
}
