<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('daily_ad_cost_accounts', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('platform_slug');
            $table->unsignedBigInteger('advertiser')->nullable();
            $table->string('platform_account_id');
            $table->unsignedBigInteger('industry_id')->nullable();
            $table->unsignedBigInteger('industry_service_id')->nullable();
            $table->boolean('exclude')->default(false);
            $table->json('data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ad_cost_accounts');
    }
};
